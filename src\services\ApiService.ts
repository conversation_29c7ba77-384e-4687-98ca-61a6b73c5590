import BaseService from './BaseService'
import type { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios'
import api from './api.interceptor'

const ApiService = {
    fetchData<Response = unknown, Request = Record<string, unknown>>(
        param: AxiosRequestConfig<Request>
    ) {
        return new Promise<AxiosResponse<Response>>((resolve, reject) => {
            api({
                method: param.method,
                url: param.url,
                data: param.data,
            }).then((res) => {
                if (res.data.errorCode !== 0) {
                    reject(res.data.message)
                }
                resolve(res)

            }).catch((err) => {
                reject(err.response.data.message || "Something went wrong")
            });
        });
    }
};
export default ApiService
