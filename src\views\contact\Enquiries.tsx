import { useState, useMemo, useEffect, InputHTMLAttributes } from 'react'
import Table from '@/components/ui/Table'
import Pagination from '@/components/ui/Pagination'
import Select from '@/components/ui/Select'
import { useReactTable, getCoreRowModel, getFilteredRowModel, getPaginationRowModel, flexRender, getSortedRowModel, } from '@tanstack/react-table'
import type { ColumnDef, ColumnFiltersState, FilterFn, } from '@tanstack/react-table'
import Input from '@/components/ui/Input'
import { rankItem } from '@tanstack/match-sorter-utils'
import api from '@/services/api.interceptor'
import endpoints from '@/endpoints'
import Breadcrumb from '../modals/BreadCrumb'
import { MdOutlineFileDownload } from 'react-icons/md'
import { Button } from '@/components/ui'

/*eslint-disable*/
interface DebouncedInputProps
  extends Omit<
    InputHTMLAttributes<HTMLInputElement>,
    'onChange' | 'size' | 'prefix'
  > {
  value: string | number
  onChange: (value: string | number) => void
  debounce?: number
}

const { Tr, Th, Td, THead, TBody, Sorter } = Table

function DebouncedInput({
  value: initialValue,
  onChange,
  debounce = 500,
  ...props
}: DebouncedInputProps) {
  const [value, setValue] = useState(initialValue)

  useEffect(() => {
    setValue(initialValue)
  }, [initialValue])

  useEffect(() => {
    const timeout = setTimeout(() => {
      onChange(value)
    }, debounce)

    return () => clearTimeout(timeout)
  }, [value])

  return (
    <div className="flex justify-end">
      <div className="flex items-center mb-4">
        <Input
          {...props}
          value={value}
          onChange={(e) => setValue(e.target.value)}
        />
      </div>
    </div>
  )
}

type Option = {
  value: number
  label: string
}

const pageSizeOption = [
  { value: 10, label: '10 / page' },
  { value: 20, label: '20 / page' },
  { value: 30, label: '30 / page' },
  { value: 40, label: '40 / page' },
  { value: 50, label: '50 / page' },
]

const breadcrumbItems = [
  { title: 'Contact Enquiries', url: '' },
];

const Enquiries = () => {
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [globalFilter, setGlobalFilter] = useState('')
  const columns = useMemo<ColumnDef<any>[]>(
    () => [
      {
        header: 'Sl No.',
        accessorKey: 'slNo',
        cell: (info) => info.row.index + 1,
        enableSorting: false,
      },
      {
        header: 'Full Name',
        accessorKey: 'fullName',
        enableSorting: false,
      },
      {
        header: 'Email',
        accessorKey: 'email',
        enableSorting: false,
      },
      {
        header: 'Mobile',
        id: 'mobile',
        enableSorting: false,
        accessorFn: (row) => {
          return row.countryCode + row.mobile
        }
      },
      {
        header: 'Store',
        accessorKey: 'store.name.en',
        enableSorting: false,
      },
      {
        header: 'Messsage',
        accessorKey: 'message',
        enableSorting: false,
      }
    ],
    []
  )

  const [data, setData] = useState<any[]>([])
  const totalData = data?.length

  const baseUrl = import.meta.env.VITE_ASSET_URL

  const fuzzyFilter: FilterFn<any> = (row, columnId, value, addMeta) => {
    const itemRank = rankItem(row.getValue(columnId), value)

    addMeta({
      itemRank,
    })

    return itemRank.passed
  }

  const table = useReactTable({
    data,
    columns,
    filterFns: {
      fuzzy: fuzzyFilter,
    },
    state: {
      columnFilters,
      globalFilter,
    },
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    globalFilterFn: fuzzyFilter,
    getSortedRowModel: getSortedRowModel(),
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  })

  const onPaginationChange = (page: number) => {
    table.setPageIndex(page - 1)
  }

  const onSelectChange = (value = 0) => {
    table.setPageSize(Number(value))
  }

  useEffect(() => {
    api
      .get(endpoints.getContactUs)
      .then((res) => {
        setData(res?.data?.result)
      })
      .catch((error) => {
        console.error('Error fetching data: ', error)
      })
  }, [])

  const exportToCSV = () => {
    const csvRows = []
    const headers = [
      'Sl No.',
      'Full Name',
      'Email',
      'Mobile',
      'Store',
      'Message',
    ]
    csvRows.push(headers.join(','))

    data.forEach((item, index: number) => {
      const row = [
        index + 1,
        item?.fullName,
        item?.email,
        item?.mobile,
        item?.store.name.en,
        item?.message,
      ]
      csvRows.push(row.join(','))
    })

    const csvString = csvRows.join('\n')
    const a = document.createElement('a')
    a.href = 'data:text/csv;charset=utf-8,' + encodeURIComponent(csvString)
    a.target = '_blank'
    a.download = 'insurance-enquiries.csv'
    a.click()
  }

  return (
    <div>
      <div className='mb-4 flex'>
        <h2>Enquiries</h2>
        <Button
          variant="twoTone"
          color="indigo-600"
          className="mr-2 mb-2 ml-auto"
          icon={<MdOutlineFileDownload />}
          onClick={exportToCSV}
        >
          Export Report
        </Button>
      </div>
      <Breadcrumb items={breadcrumbItems} />
      <div className="mb-4 flex space-x-2 justify-end">
        <DebouncedInput
          value={globalFilter ?? ''}
          className="p-2 font-lg shadow border border-block"
          placeholder="Search all columns..."
          onChange={(value) => setGlobalFilter(String(value))}
        />
      </div>
      <Table>
        <THead>
          {table.getHeaderGroups().map((headerGroup) => (
            <Tr key={headerGroup.id}>
              {headerGroup.headers.map((header) => {
                return (
                  <Th
                    key={header.id}
                    colSpan={header.colSpan}
                  >
                    {header.column.getCanSort() ? (
                      <div
                        className="cursor-pointer select-none"
                        onClick={header.column.getToggleSortingHandler()}
                      >
                        {flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                        <Sorter sort={header.column.getIsSorted()} />
                      </div>
                    ) : (
                      <div>
                        {flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                      </div>
                    )}
                  </Th>
                )
              })}
            </Tr>
          ))}
        </THead>
        <TBody>
          {table.getRowModel().rows.map((row) => {
            return (
              <Tr key={row.id}>
                {row.getVisibleCells().map((cell) => {
                  return (
                    <Td key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </Td>
                  )
                })}
              </Tr>
            )
          })}
        </TBody>
      </Table>
      <div className="flex items-center justify-between mt-4">
        <Pagination
          pageSize={table.getState().pagination.pageSize}
          currentPage={table.getState().pagination.pageIndex + 1}
          total={totalData}
          onChange={onPaginationChange}
        />
        <div style={{ minWidth: 130 }}>
          <Select<Option>
            size="sm"
            isSearchable={false}
            value={pageSizeOption.filter(
              (option) =>
                option.value ===
                table.getState().pagination.pageSize
            )}
            options={pageSizeOption}
            onChange={(option) => onSelectChange(option?.value)}
          />
        </div>
      </div>
    </div>
  )
}

export default Enquiries