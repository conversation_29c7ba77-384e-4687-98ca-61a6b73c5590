import SalesByCategories from './home/<USER>'
import DashboardHeader from './home/<USER>'
import SalesReport from './home/<USER>'
import Statistic from './home/<USER>'
import LatestOrder from './home/<USER>'
import TopProduct from './home/<USER>'
import { useEffect, useState } from 'react'
import api from '@/services/api.interceptor'
import endpoints from '@/endpoints'
type Statistic = {
    value: number
    growShrink: number
}

export type DashboardData = {
    statisticData?: {
        revenue: Statistic
        orders: Statistic
        purchases: Statistic
    }
    salesReportData?: {
        series: {
            name: string
            data: number[]
        }[]
        categories: string[]
    }
    topProductsData?: {
        id: string
        name: string
        img: string
        sold: number
    }[]
    latestOrderData?: {
        id: string
        date: number
        customer: string
        status: number
        paymentMehod: string
        totalAmount: number
    }[]
    salesByCategoriesData?: {
        labels: string[]
        data: number[]
    }
}

export default function Home() {
    const [statisticData, setStatisticData] = useState<any>();
    const [latestOrdersData, setLatestOrdersData] = useState<any>();
    const [topSellingData, setTopSellingData] = useState<any>();
    const [salesByCategoriesData, setSalesByCategoriesData] = useState<any>();
    const [salesReportData, setSalesReportData] = useState<any>();

    const fetchStatisticData = async () => {
        try {
            const response = await api.get(endpoints.statisticData);
            const data = response.data;
            if (data.success && data.result && data.result.statisticData) {
                setStatisticData({
                    revenue: {
                        value: data.result.statisticData.revenue.value,
                        growShrink: data.result.statisticData.revenue.growShrink,
                    },
                    orders: {
                        value: data.result.statisticData.orders.value,
                        growShrink: parseFloat(data.result.statisticData.orders.growShrink),
                    },
                    purchases: {
                        value: parseFloat(data.result.statisticData.purchases.value),
                        growShrink: parseFloat(data.result.statisticData.purchases.growShrink),
                    },
                });
            }
        } catch (error) {
            console.error('Error fetching statistic data:', error);
        }
    };

    const fetchlatestOrderData = async () => {
        try {
            const response = await api.get(endpoints.latestOrderData);
            const data = response.data;
            if (data.success && data.result) {
                // Transform the fetched data to match the structure expected by mockDashboardData
                const transformedData = data.result.map((order: any) => ({
                    id: order.id,
                    date: new Date(order.date).getTime(), // Convert date to timestamp
                    customer: order.customer,
                    status: order.status,
                    paymentMehod: order.paymentMethod, // Corrected typo from 'paymentMehod' to 'paymentMethod'
                    totalAmount: order.totalAmount,
                }));
                setLatestOrdersData(transformedData);
            }
        } catch (error) {
            console.error('Error fetching statistic data:', error);
        }
    }

    const fetchTopSellingData = async () => {
        try {
            const response = await api.get(endpoints.topSellingData);
            const data = response.data;
            if (data.success && data.result) {
                setTopSellingData(data.result);
            }
        } catch (error) {
            console.error('Error fetching statistic data:', error);
        }
    }

    const fetchSalesByCategoriesData = async () => {
        try {
            const response = await api.get(endpoints.salesByCategories);
            const data = response.data;
            if (data.success && data.result) {
                setSalesByCategoriesData(data.result);
            }
        } catch (error) {
            console.error('Error fetching statistic data:', error);
        }
    }

    const fetchSalesReportData = async () => {
        try {
            const response = await api.get(endpoints.salesReportData);
            const data = response.data;
            if (data.success && data.result) {
                setSalesReportData(data.result);
            }
        } catch (error) {
            console.error('Error fetching statistic data:', error);
        }
    }

    useEffect(() => {
        fetchStatisticData();
        fetchlatestOrderData();
        fetchTopSellingData();
        fetchSalesByCategoriesData();
        fetchSalesReportData();
    }, []);

    // Mock data
    const mockDashboardData: DashboardData = {
        statisticData: statisticData ?? {
            revenue: { value: 5000, growShrink: 10 },
            orders: { value: 100, growShrink: -5 },
            purchases: { value: 300, growShrink: 8 },
        },
        salesReportData: salesReportData ?? {
            series: [
                { name: '', data: [100, 200, 300, 400] },
            ],
            categories: ['Dec 2023', 'Jan 2024', 'Feb 2024', 'Mar 2024'],
        },
        topProductsData: topSellingData ?? [
            {
                id: '1',
                name: 'Blue Light Blocking Glasses',
                img: 'blue-light-glasses.jpg',
                sold: 120,
            },
            {
                id: '2',
                name: 'Designer Sunglasses',
                img: 'designer-sunglasses.jpg',
                sold: 90,
            },
            {
                id: '3',
                name: 'Color Contact Lenses',
                img: 'color-contact-lenses.jpg',
                sold: 80,
            },
            {
                id: '4',
                name: 'Prescription Eyeglasses',
                img: 'prescription-eyeglasses.jpg',
                sold: 70,
            },
            {
                id: '5',
                name: 'Sports Goggles',
                img: 'sports-goggles.jpg',
                sold: 60,
            },
        ],
        latestOrderData: latestOrdersData ?? [
            {
                id: '123',
                date: 1647312000,
                customer: 'John Doe',
                status: 1,
                paymentMehod: 'Credit Card',
                totalAmount: 500,
            },
            {
                id: '124',
                date: 1647313000,
                customer: 'Jane Doe',
                status: 2,
                paymentMehod: 'PayPal',
                totalAmount: 300,
            },
        ],
        salesByCategoriesData: salesByCategoriesData ?? {
            labels: ['Eyeglasses', 'Sunglasses', 'Contact Lenses', 'Goggles'],
            data: [250, 180, 320, 120],
        },
    }
    return (
        <div className="flex flex-col gap-4 h-full">
            <DashboardHeader />
            <Statistic data={mockDashboardData.statisticData} />
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                <SalesReport
                    data={mockDashboardData?.salesReportData}
                    className="col-span-2"
                />
                <SalesByCategories
                    data={mockDashboardData?.salesByCategoriesData}
                />
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                <LatestOrder
                    data={mockDashboardData?.latestOrderData}
                    className="lg:col-span-2"
                />
                <TopProduct data={mockDashboardData?.topProductsData} />
            </div>
        </div>
    )
}
