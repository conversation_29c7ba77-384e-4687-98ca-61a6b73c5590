/* eslint-disable */
import {
    Ava<PERSON>,
    But<PERSON>,
    Dialog,
    FormItem,
    Input,
    Radio,
    Select,
    Tooltip,
    toast,
} from '@/components/ui'
import endpoints from '@/endpoints'
import api from '@/services/api.interceptor'
import React, { forwardRef, useCallback, useEffect, useRef, useState } from 'react'
import { DragDropContext, Draggable, Droppable } from 'react-beautiful-dnd'
import Notification from '@/components/ui/Notification'
import type { MouseEvent } from 'react'
import { FaPen } from 'react-icons/fa'
import { useNavigate } from 'react-router-dom'
import { IoIosCloseCircleOutline, IoMdCloseCircle } from 'react-icons/io'
import { Controller, useFieldArray, useForm } from 'react-hook-form'
import { HiOutlinePlus } from 'react-icons/hi'
import { MdDeleteOutline } from 'react-icons/md'
import {
    IoCheckmarkCircle,
    IoCloseCircle,
    IoDuplicateOutline,
} from 'react-icons/io5'
import { LaptopMockup, PhoneMockup } from '@/components/shared/Mockup'

import {
    DndContext,
    closestCenter,
    KeyboardSensor,
    PointerSensor,
    useSensor,
    useSensors,
    DragOverlay,
} from '@dnd-kit/core';
import {
    arrayMove,
    rectSortingStrategy,
    SortableContext,
    sortableKeyboardCoordinates,
    useSortable,
} from '@dnd-kit/sortable';
import { createPortal } from 'react-dom'
import { CSS } from "@dnd-kit/utilities";

const baseUrl = import.meta.env.VITE_ASSET_URL


const options: any = [
    { value: 'true', label: 'True' },
    { value: 'false', label: 'False' },
]

const DashboardDnd = () => {
    const [isNewWidget, setNewWidget] = useState(false)
    const [items, setItems] = React.useState([])
    const [isUpdate, setIsUpdate] = React.useState(false)
    const [refid, setRefid] = React.useState('')
    const [formSubmitted, setFormSubmitted] = React.useState(false)
    const [dialogIsOpen, setIsOpen] = useState(false)
    const [editDialogOpen, setEditDialogOpen] = useState(false)
    const [collectionOptions, setCollectionOptions] = useState<any>([])
    const [collectionType, setCollectionType] = useState('single')
    const [selectedCollections, setSelectedCollections] = useState<any>([])
    const [widgetId, setWidgetId] = useState(null)
    const [widgetType, setWidgetType] = useState(null)
    const [categories, setCategories] = useState<any>([])
    const [brands, setBrands] = useState<any>([])
    const [collections, setCollections] = useState<any>([])
    const [singleCollection, setSingleCollection] = useState<any>('')
    const [newSingleCollection, setNewSingleCollection] = useState<any>([])
    const [checkedCollection, setCheckedCollection] = useState<any>([])
    const [insuranceForm, setInsuranceForm] = useState<any>({
        image: null,
        title: '',
        titleAr: '',
        description: '',
        descriptionAr: '',
        buttonText: '',
        buttonTextAr: '',
        link: '',
    })
    const [formErrors, setFormErrors] = useState({
        title: '',
        titleAr: '',
        description: '',
        descriptionAr: '',
        buttonText: '',
        buttonTextAr: '',
        link: '',
        image: '',
    })
    const [brandVideoForm, setBrandVideoForm] = useState<any>({
        description: '',
        descriptionAr: '',
        link: "",
        image: null,
        video: null,
        subImage: null,
    })
    const [brandBannerType, setBrandBannerType] = useState<any>('video')
    const [brandVideoFile, setBrandVideoFile] = useState<any>(null)
    const [brandVideoPreview, setBrandVideoPreview] = useState<any>('')
    const [gridPreviews, setGridPreviews] = useState<string[]>([])
    const [imagePreviews, setImagePreviews] = useState<string[]>([])
    const [mainBannerPreviews, setMainBannerPreviews] = useState<string[]>([])
    const [virtualtryForm, setVirtualtryForm] = useState<any>({
        title: '',
        titleAr: '',
        description: '',
        descriptionAr: '',
        buttonText: '',
        buttonTextAr: '',
        link: '',
        image: null,
        subImage: null,
    })
    const [beforeAfter, setBeforeAfter] = useState<any>([])
    const [showAvatars, setShowAvatars] = useState(false)
    const [beforeFile, setBeforeFile] = useState<any>(null)
    const [afterFile, setAfterFile] = useState<any>(null)
    const fileInputBeforeRef = useRef<HTMLInputElement>(null)
    const fileInputAfterRef = useRef<HTMLInputElement>(null)
    const [mainBannerType, setMainBannerType] = useState<any>('videoBanner')
    const [mainVideoForm, setMainVideoForm] = useState<any>({
        mobileVideo: null,
        video: null,
        link: '',
    })
    const [categoryTitleEn, setCategoryTitleEn] = useState<any>('')
    const [categoryTitleAr, setCategoryTitleAr] = useState<any>('')

    const [shopByBrandTitleEn, setShopByBrandTitleEn] = useState<string>('')
    const [shopByBrandTitleAr, setShopByBrandTitleAr] = useState<string>('')
    const [shopByBrandBtnEn, setShopByBrandBtnEn] = useState<string>('')
    const [shopByBrandBtnAr, setShopByBrandBtnAr] = useState<string>('')

    const [brandsTitleEn, setBrandsTitleEn] = useState<string>('')
    const [brandsTitleAr, setBrandsTitleAr] = useState<string>('')

    const [collectionsLabelEn, setCollectionsLabelEn] = useState<string>('')
    const [collectionsLabelAr, setCollectionsLabelAr] = useState<string>('')

    const [frameShapeTitleEn, setFrameShapeTitleEn] = useState<string>('')
    const [frameShapeTitleAr, setFrameShapeTitleAr] = useState<string>('')

    const [beforeAfterTitleEn, setBeforeAfterTitleEn] = useState<string>('')
    const [beforeAfterTitleAr, setBeforeAfterTitleAr] = useState<string>('')

    const [brandCollectionTitleEn, setBrandCollectionTitleEn] = useState<string>('')
    const [brandCollectionTitleAr, setBrandCollectionTitleAr] = useState<string>('')


    const navigate = useNavigate()

    const sensors = useSensors(
        useSensor(PointerSensor, {
            activationConstraint: {
                distance: 5
            }
        }),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        })
    );

    const [activeCat, setActiveCat] = useState(null)

    function handleDragStart(event: any) {
        const { active } = event;
        const cat = categories.findIndex(((item: any) => item?.refid == active.id));

        setActiveCat(categories[cat]);
    }


    const handleDragEndCat = useCallback((event: any) => {
        const { active, over }: any = event;

        if (active?.id !== over?.id) {
            const oldIndex = categories.findIndex(((item: any) => item?.refid == active.id));
            const newIndex = categories.findIndex(((item: any) => item?.refid == over.id));
            let newCats = arrayMove(categories, oldIndex, newIndex);
            newCats.forEach((category: any, index: number) => {
                category.order = index + 1
            })
            setCategories(newCats);

            api.put(endpoints.updateCategoryOrder, {
                categories: newCats,
            }).then((res) => {
                if (res?.status == 200) {
                    getCategories()
                }
            })
        }
        setActiveCat(null)
    }, [categories]);

    // const onDragEnd = useCallback(
    //     (result: any) => {
    //         if (!result.destination) return

    //         const reorderedCategories = [...categories]
    //         const [reorderedCategory] = reorderedCategories.splice(
    //             result.source.index,
    //             1
    //         )
    //         reorderedCategories.splice(
    //             result.destination.index,
    //             0,
    //             reorderedCategory
    //         )

    //         // Update the order property of each category based on the new arrangement
    //         reorderedCategories.forEach((category: any, index: number) => {
    //             category.order = index + 1
    //         })

    //         setCategories(reorderedCategories)
    //         console.log('categories', categories)
    //         api.put(endpoints.updateCategoryOrder, {
    //             categories: reorderedCategories,
    //         }).then((res) => {
    //             if (res?.status == 200) {
    //                 getCategories()
    //             }
    //         })
    //     },
    //     [categories]
    // )

    const {
        handleSubmit,
        control,
        setValue,
        watch,
        formState: { errors },
    } = useForm<any>()

    const { fields, append, remove } = useFieldArray({
        control,
        name: 'fields',
    })

    const {
        fields: gridFields,
        append: gridAppend,
        remove: gridRemove,
    } = useFieldArray({
        control,
        name: 'gridFields',
    })

    const {
        fields: mainBannerFields,
        append: mainBannerAppend,
        remove: mainBannerRemove,
    } = useFieldArray({
        control,
        name: 'mainBannerFields',
    })

    const {
        fields: topSliderFields,
        append: topSliderAppend,
        remove: topSliderRemove,
    }: any = useFieldArray({
        control,
        name: 'topSliderFields',
    })

    const [currentSlide, setCurrentSlide] = useState<number>(0)
    const [sliderVideo, setSliderVideo] = useState<any>(null)
    const [sliderMobileVideo, setSliderMobileVideo] = useState<any>(null)
    const [sliderImage, setSliderImage] = useState<any>(null)

    const onSubmit = (data: any) => {
        const payload = {
            widgetId: widgetId,
            mainTitle: {
                en: brandCollectionTitleEn,
                ar: brandCollectionTitleAr,
            },
            sliders: data.fields.map((item: any) => ({
                image: item.imageUrl,
                title: {
                    en: item.titleEn,
                    ar: item.titleAr
                },
                buttonText: {
                    en: item.buttonTextEn,
                    ar: item.buttonTextAr,
                },
                link: item.link,
            })),
        }
        api.post(endpoints.setSlider, payload)
            .then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification
                            type="success"
                            title="Slider updated successfully."
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    setFormSubmitted(true)
                    setEditDialogOpen(false)
                }
            })
            .catch((err) => {
                toast.push(
                    <Notification
                        type="warning"
                        title={err.response.data.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )
            })
    }

    const changeMainBannerType = (e: any) => {
        if (e == 'mainBanner') {
            if (mainBannerFields.length == 0) {
                mainBannerAppend({})
            }
        }
        setMainBannerType(e)
    }

    const handleVideoFile = (e: any) => {
        const file = e.target.files[0]
        console.log(e.target.id)
        if (file) {
            const maxFileSize = 10 * 1024 * 1024
            if (file.size > maxFileSize) {
                toast.push(
                    <Notification
                        type="warning"
                        title="File size exceeds the maximum limit of 10MB."
                    />,
                    {
                        placement: 'top-center',
                    }
                )
                return
            }

            const formData = new FormData()
            formData.append('video', file)

            // api.post(endpoints.videoUpload, formData).then((res) => {
            //     if (res?.status == 200) {
            //         setMainVideoForm({
            //             ...mainVideoForm,
            //             [e.target.id]: res.data?.videoUrl,
            //         })
            //     }
            // })
        } else {
            toast.push(
                <Notification
                    type="warning"
                    title="Please select a video file."
                />,
                {
                    placement: 'top-center',
                }
            )
        }
    }

    const handleSliderVideo = (e: any, field: string) => {
        const file = e.target.files[0]
        console.log(e.target.id)
        if (file) {
            const maxFileSize = 10 * 1024 * 1024
            if (file.size > maxFileSize) {
                toast.push(
                    <Notification
                        type="warning"
                        title="File size exceeds the maximum limit of 10MB."
                    />,
                    {
                        placement: 'top-center',
                    }
                )
                return
            }

            const formData = new FormData()
            formData.append('video', file)

            api.post(endpoints.videoUpload, formData).then((res) => {
                if (res?.status == 200) {
                    if (field.includes('mobileVideo')) {
                        setSliderMobileVideo(res.data?.videoUrl)
                    } else {
                        setSliderVideo(res.data?.videoUrl)
                    }
                    setValue(field, res.data?.videoUrl)
                }
            })
        } else {
            toast.push(
                <Notification
                    type="warning"
                    title="Please select a video file."
                />,
                {
                    placement: 'top-center',
                }
            )
        }
    }

    const handleMainBannerSubmit = (data: any) => {
        const payload = {
            widgetId: widgetId,
            type: mainBannerType,
            sliders: data.mainBannerFields.map((item: any) => ({
                image: item.imageUrl,
                link: item.link,
            })),
        }

        api.post(endpoints.setSlider, payload)
            .then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification
                            type="success"
                            title="Home Page updated successfully."
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    setFormSubmitted(true)
                }
            })
            .catch((err) => {
                toast.push(
                    <Notification
                        type="warning"
                        title={err.response.data.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )
            })
    }

    const handleImages = async (e: any, index: number, type: string) => {
        const image = e.target.files?.[0]
        if (image) {
            const formData = new FormData()
            formData.append('video', image)

            try {
                const res = await api.post(endpoints.videoUpload, formData)
                const imageUrl = res?.data?.videoUrl
                if (type == 'gridFields') {
                    setValue(`gridFields.${index}.imageUrl`, imageUrl)
                    setGridPreviews((prevPreviews) => {
                        const newPreviews = [...prevPreviews]
                        newPreviews[index] = imageUrl
                        return newPreviews
                    })
                } else if (type == 'fields') {
                    setValue(`fields.${index}.imageUrl`, imageUrl)
                    setImagePreviews((prevPreviews) => {
                        const newPreviews = [...prevPreviews]
                        newPreviews[index] = imageUrl
                        return newPreviews
                    })
                } else if (type == 'mainBannerFields') {
                    setValue(`mainBannerFields.${index}.imageUrl`, imageUrl)
                    setMainBannerPreviews((prevPreviews) => {
                        const newPreviews = [...prevPreviews]
                        newPreviews[index] = imageUrl
                        return newPreviews
                    })
                }
            } catch (error) {
                console.error('Error fetching data: ', error)
            }
        }
    }

    const handleSliderImages = async (e: any, index: number, name: string = "image") => {
        const image = e?.target?.files?.[0]
        if (image) {
            const formData = new FormData()
            formData.append('video', image)

            try {
                const res = await api.post(endpoints.videoUpload, formData)
                const imageUrl = res?.data?.videoUrl
                setValue(`topSliderFields.${index}.${name}`, imageUrl)
            } catch (error) {
                console.error('Error fetching data: ', error)
            }
        }
    }

    const brandVideoChange = (event: any) => {
        const file = event.target.files[0]
        const maxFileSize = 4 * 1024 * 1024
        if (file) {
            if (file.size > maxFileSize) {
                toast.push(
                    <Notification
                        type="warning"
                        title="File size exceeds the maximum limit of 4MB."
                    />,
                    {
                        placement: 'top-center',
                    }
                )
                return
            }
            setBrandVideoFile(file)
            const videoURL = URL.createObjectURL(file)
            setBrandVideoPreview(videoURL)
        }
    }

    const brandVideoUpload = (index: number) => {
        const formData = new FormData()
        formData.append('video', brandVideoFile)
        api.post(endpoints.videoUpload, formData)
            .then((res) => {
                if (res?.status == 200) {
                    setBrandVideoForm({
                        ...brandVideoForm,
                        description: brandVideoForm?.description?.en,
                        descriptionAr: brandVideoForm?.description?.ar,
                        video: res?.data?.videoUrl,
                    })
                    setValue(`brandSliderFields.${index}.video`, res?.data?.videoUrl)
                    setBrandVideoFile(null)
                }
            })
            .catch((error) => {
                console.error('Error uploading video: ', error)
            })
    }

    const brandLogoUpload = (event: any, index: number) => {
        const file = event.target.files[0]
        const maxFileSize = 4 * 1024 * 1024
        if (file) {
            if (file.size > maxFileSize) {
                toast.push(
                    <Notification
                        type="warning"
                        title="File size exceeds the maximum limit of 4MB."
                    />,
                    {
                        placement: 'top-center',
                    }
                )
                return
            }
            const formData = new FormData()
            formData.append('video', file)
            api.post(endpoints.videoUpload, formData)
                .then((res) => {
                    if (res?.status == 200) {
                        setBrandVideoForm({
                            ...brandVideoForm,
                            subImage: res?.data?.videoUrl,
                        })
                        setValue(`brandSliderFields.${index}.subImage`, res?.data?.videoUrl)
                    }
                })
                .catch((error) => {
                    console.error('Error uploading video: ', error)
                })
        }
    }

    const BrandImageUpload = (event: any, index: number) => {
        const file = event.target.files[0]
        const maxFileSize = 4 * 1024 * 1024
        if (file) {
            if (file.size > maxFileSize) {
                toast.push(
                    <Notification
                        type="warning"
                        title="File size exceeds the maximum limit of 4MB."
                    />,
                    {
                        placement: 'top-center',
                    }
                )
                return
            }
            const formData = new FormData()
            formData.append('video', file)
            api.post(endpoints.videoUpload, formData)
                .then((res) => {
                    if (res?.status == 200) {
                        setBrandVideoForm({
                            ...brandVideoForm,
                            image: res?.data?.videoUrl,
                        })
                        setValue(`brandSliderFields.${index}.image`, res?.data?.videoUrl)
                    }
                })
                .catch((error) => {
                    console.error('Error uploading video: ', error)
                })
        }
    }

    const mainVideoUpload = () => {
        console.log(mainVideoForm)
        api.post(endpoints.setBanner, {
            widgetId: widgetId,
            ...mainVideoForm,
            type: mainBannerType,
            newWidget: isNewWidget,
        })
            .then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification
                            type="success"
                            title="Home Page updated"
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    setFormSubmitted(true)
                    setEditDialogOpen(false)
                }
            })
            .catch((err) => {
                toast.push(
                    <Notification
                        type="warning"
                        title={err.response.data.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )
            })
    }

    const handleBrandVideoForm = () => {
        let updatedBrandVideoForm = { ...brandVideoForm }
        if (brandBannerType === 'video') {
            delete updatedBrandVideoForm.image
        } else {
            delete updatedBrandVideoForm.video
        }

        api.post(endpoints.setBanner, {
            widgetId: widgetId,
            ...updatedBrandVideoForm,
            description: {
                en: updatedBrandVideoForm?.description,
                ar: updatedBrandVideoForm?.descriptionAr,
            }
        })
            .then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification
                            type="success"
                            title="Home Page updated"
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    setFormSubmitted(true)
                    setEditDialogOpen(false)
                }
            })
            .catch((err) => {
                toast.push(
                    <Notification
                        type="warning"
                        title={err.response.data.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )
            })
    }

    const handleBrandSlider = (data: any) => {

        let slideIndex = null;
        let msg = "";

        for (let [key, slide] of data.brandSliderFields.entries()) {
            if (slide?.type == "video") {
                if (!slide?.video) {
                    slideIndex = key
                    msg = "Video is required"
                }
            } else {
                if (!slide?.image) {
                    slideIndex = key
                    msg = "Image is required"
                }
            }
        }

        if (slideIndex != null) {
            setCurrentSlide(slideIndex)
            toast.push(
                <Notification
                    type="warning"
                    title={msg}
                />,
                {
                    placement: 'top-center',
                }
            )
            return
        }

        console.log(data)
        api.post(endpoints.setSlider, {
            widgetId: widgetId,
            sliders: data.brandSliderFields
        })
            .then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification
                            type="success"
                            title="Home Page updated"
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    setFormSubmitted(true)
                    setEditDialogOpen(false)
                }
            })
            .catch((err) => {
                toast.push(
                    <Notification
                        type="warning"
                        title={err.response.data.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )
            })
    }

    const deleteBrandSlider = () => {
        brandSliderRemove(currentBrandSlider)
        setCurrentBrandSlider(currentBrandSlider - 1)
    }

    const deleteTopSlider = () => {
        topSliderRemove(currentSlide)
        setCurrentSlide(currentSlide - 1)
    }

    const handleSave = (data: any) => {
        const payload = {
            widgetId: widgetId,
            sliders: data.gridFields.map((item: any) => ({
                image: item.imageUrl,
                link: item.link,
            })),
        }
        api.post(endpoints.setSlider, payload)
            .then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification
                            type="success"
                            title="Home Page updated"
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    getHomeOrder()
                    setGridPreviews([])
                    setEditDialogOpen(false)
                }
            })
            .catch((err) => {
                toast.push(
                    <Notification
                        type="warning"
                        title={err.response.data.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )
            })
    }

    const handleRadioChange = (event: any) => {
        setSingleCollection(event.target.value)
    }


    const handleRadioChangeNew = (event: any) => {
        setNewSingleCollection(event.target.value)
    }

    const saveSingleCollection = () => {
        api.put(endpoints.setSingleCollection, {
            collection: singleCollection,
            widgetId: widgetId,
        })
            .then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification
                            type="success"
                            title="Home Page updated"
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    setFormSubmitted(true)
                    setEditDialogOpen(false)
                }
            })
            .catch((err) => {
                toast.push(
                    <Notification
                        type="warning"
                        title={err.response.data.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )
            })
    }

    const saveNewSingleCollection = () => {
        api.put(endpoints.setSingleCollection, {
            collection: newSingleCollection,
            widgetId: widgetId,
        })
            .then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification
                            type="success"
                            title="Home Page updated"
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    setFormSubmitted(true)
                    setEditDialogOpen(false)
                }
            })
            .catch((err) => {
                toast.push(
                    <Notification
                        type="warning"
                        title={err.response.data.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )
            })
    }

    const openDialog = () => {
        setIsOpen(true)
    }

    const getBeforeAfter = () => {
        api.get(endpoints.beforeAfter).then((res) => {
            if (res.status == 200) {
                console.log(res?.data?.result)
                setBeforeAfter(res?.data?.result)
            }
        })
    }

    const handleAvatarClick = (avatarType: any) => {
        if (avatarType === 'before' && fileInputBeforeRef.current) {
            fileInputBeforeRef.current.click()
        } else if (avatarType === 'after' && fileInputAfterRef.current) {
            fileInputAfterRef.current.click()
        }
    }

    const handleFileChange = (event: any, avatarType: any) => {
        const files = event.target.files[0]
        if (files) {
            const formData = new FormData()
            formData.append('video', files)
            api.post(endpoints.videoUpload, formData).then((res) => {
                if (res?.status == 200) {
                    if (avatarType === 'before') {
                        setBeforeFile(res.data?.videoUrl)
                    } else if (avatarType === 'after') {
                        setAfterFile(res.data?.videoUrl)
                    }
                }
            })
        }
    }

    const handleSaveClick = () => {
        if (beforeFile && afterFile) {
            api.post(endpoints.beforeAfter, {
                beforeImage: beforeFile,
                afterImage: afterFile,
            })
                .then((res) => {
                    if (res.status == 200) {
                        toast.push(
                            <Notification
                                type="success"
                                title="Home Page updated"
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                        setFormSubmitted(true)
                        setEditDialogOpen(false)
                        setShowAvatars(false)
                        setBeforeFile(null)
                        setAfterFile(null)
                    }
                })
                .catch((err) => {
                    toast.push(
                        <Notification
                            type="warning"
                            title={err.response.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                })
        } else {
            toast.push(
                <Notification
                    type="warning"
                    title="Please upload before and after images"
                />,
                {
                    placement: 'top-center',
                }
            )
        }
    }

    const openEditDialog = (id: any, type: any, isNewLayout: boolean) => {
        if (isNewLayout) {
            setWidgetId(null)
            setNewWidget(true)
            setWidgetType(type)
            setEditDialogOpen(true)
            // Initialize other states as needed for a new layout
            setInsuranceForm({
                image: null,
                title: '',
                titleAr: '',
                description: '',
                descriptionAr: '',
                buttonText: '',
                buttonTextAr: '',
                link: '',
            })
            setBrandVideoForm({ description: '', descriptionAr: '', video: null, subImage: null, link: "" })
            setVirtualtryForm({
                title: '',
                titleAr: '',
                description: '',
                descriptionAr: '',
                buttonText: '',
                buttonTextAr: '',
                link: '',
                image: null,
                subImage: null,
            })
        } else {
            setWidgetId(id)
            setWidgetType(type)
            setEditDialogOpen(true)
            const selectedWidget: any = items.find(
                (widget: any) => widget?._id == id
            )
            if (selectedWidget?.type == "videoBanner") {
                topSliderRemove()
                selectedWidget?.items?.forEach((item: any, index: number) => {
                    topSliderAppend({
                        type: item?.type,
                        srcType: item?.srcType ?? "file",
                        src: item?.src,
                        mobileSrc: item?.mobileSrc,
                        video: item?.video ?? "",
                        mobileVideo: item?.mobileVideo ?? "",
                        link: item?.link,
                        image: item?.image ?? "",
                        mobileImage: item?.mobileImage ?? "",
                        isActive: !item?.isActive ? { label: "False", value: "false" } : { label: "True", value: "true" }
                    })
                })
                setCurrentSlide(0)
            }
            if (selectedWidget?.type == 'categories')
                setCategoryTitleEn(selectedWidget?.items[0].title?.en)
            setCategoryTitleAr(selectedWidget?.items[0]?.title?.ar);

            if (selectedWidget?.type == 'shopByBrands')
                setShopByBrandTitleEn(selectedWidget?.items[0].title?.en)
            setShopByBrandTitleAr(selectedWidget?.items[0].title?.ar);
            setShopByBrandBtnEn(selectedWidget?.items[0].buttonText?.en)
            setShopByBrandBtnAr(selectedWidget?.items[0].buttonText?.ar);

            if (selectedWidget?.type == 'brands')
                setBrandsTitleEn(selectedWidget?.items[0].title?.en)
            setBrandsTitleAr(selectedWidget?.items[0].title?.ar);

            if (selectedWidget?.type == 'collectionsSingle')
                setSingleCollection(selectedWidget?.keyword[0].id);

            if (selectedWidget?.type == 'newCollectionsSingle')
                setNewSingleCollection(selectedWidget?.keyword[0].id);

            if (selectedWidget?.type == 'collectionsMultiple')
                setCollectionsLabelEn(selectedWidget?.items?.[0].title?.en)
            setCollectionsLabelAr(selectedWidget?.items?.[0].title?.ar);

            if (selectedWidget?.type == 'frameShape')
                setFrameShapeTitleEn(selectedWidget?.items?.[0].title?.en)
            setFrameShapeTitleAr(selectedWidget?.items?.[0].title?.ar);

            if (selectedWidget?.type == 'insuranceBanner') {
                const data = selectedWidget.items[0]
                setInsuranceForm({
                    image: data?.image,
                    title: data?.title?.en,
                    titleAr: data?.title?.ar,
                    description: data?.description?.en,
                    descriptionAr: data?.description?.ar,
                    buttonText: data?.buttonText?.en,
                    buttonTextAr: data?.buttonText?.ar,
                    link: data?.link,
                })
            }
            if (selectedWidget?.type == 'brandVideoBanner') {
                const data = selectedWidget.items[0]
                if (data?.image) setBrandBannerType('image')
                else setBrandBannerType('video')
                setBrandVideoForm({
                    link: data?.link,
                    description: data?.description?.en,
                    descriptionAr: data?.description?.ar,
                    video: data?.video ? data?.video : null,
                    image: data?.image ? data?.image : null,
                    subImage: data?.subImage,
                })
                brandSliderRemove()
                setCurrentBrandSlider(0)
                selectedWidget.items?.forEach((item: any) => {
                    brandSliderAppend({
                        description: item?.description?.en,
                        descriptionAr: item?.description?.ar,
                        subImage: item?.subImage,
                        image: item?.image ? item?.image : null,
                        link: item?.link,
                        type: item?.type ? item?.type : item?.image ? 'image' : 'video',
                        video: item?.video ? item?.video : null
                    })
                })

            }
            if (selectedWidget?.type == 'threeGridBanner') {
                if (selectedWidget.items.length > 0) {
                    const data = selectedWidget.items
                    setValue(
                        'gridFields',
                        data.map((item: any) => ({
                            link: item.link,
                            imageUrl: item?.image,
                        }))
                    )
                    setGridPreviews(data.map((item: any) => item?.image))
                } else gridAppend({})
            }
            if (selectedWidget?.type == 'beforeAfter') {
                getBeforeAfter()
                setBeforeAfterTitleEn(selectedWidget?.items?.[0].title?.en)
                setBeforeAfterTitleAr(selectedWidget?.items?.[0].title?.ar)
            }
            if (selectedWidget?.type == 'brandsCollection') {
                if (selectedWidget.items.length > 0) {
                    console.log(selectedWidget)
                    const data = selectedWidget.items
                    console.log(data)
                    setValue(
                        'fields',
                        data.map((item: any) => ({
                            titleEn: item?.title?.en,
                            titleAr: item?.title?.ar,
                            buttonTextEn: item?.buttonText?.en,
                            buttonTextAr: item?.buttonText?.ar,
                            link: item.link,
                            imageUrl: item?.image,
                        }))
                    )
                    setImagePreviews(data.map((item: any) => item?.image))
                    setBrandCollectionTitleEn(selectedWidget?.mainTitle?.en)
                    setBrandCollectionTitleAr(selectedWidget?.mainTitle?.ar)
                } else append({})
            }
            if (selectedWidget?.type == 'virtualTryBanner') {
                const data = selectedWidget.items[0]

                setVirtualtryForm({
                    image: data?.image,
                    subImage: data?.subImage,
                    title: data?.title?.en,
                    titleAr: data?.title?.ar,
                    description: data?.description?.en,
                    descriptionAr: data?.description?.ar,
                    buttonText: data?.buttonText?.en,
                    buttonTextAr: data?.buttonText?.ar,
                    link: data?.link,
                })
            }

            // if (
            //     selectedWidget?.type == 'videoBanner' ||
            //     selectedWidget?.type == 'mainBanner'
            // ) {
            //     setMainBannerType(selectedWidget?.type)
            //     const data = selectedWidget.items
            //     if (selectedWidget?.type == 'videoBanner') {
            //         setMainVideoForm({
            //             video: data[0]?.video,
            //             mobileVideo: data[0]?.mobileVideo,
            //             link: data[0]?.link,
            //         })
            //     } else if (selectedWidget?.type == 'mainBanner') {
            //         setValue(
            //             'mainBannerFields',
            //             data.map((item: any) => ({
            //                 imageUrl: item?.image,
            //                 link: item?.link,
            //             }))
            //         )
            //         setMainBannerPreviews(data.map((item: any) => item?.image))
            //     }
            // }
        }
    }

    const handleCollectionSelection = (selectedOptions: any) => {
        setSelectedCollections(selectedOptions)
    }

    const handleCategorySelection = (event: any) => {
        const categoryId = event.target.value
        const isChecked = event.target.checked

        api.put(endpoints.updateInHome + categoryId, {
            inHome: isChecked,
        }).then((res) => {
            if (res.status == 200) {
                toast.push(
                    <Notification
                        type="success"
                        title={`${res.data.result.name.en} is ${res.data.result.inHome ? 'added' : 'removed'
                            } in home`}
                    />,
                    { placement: 'top-center' }
                )
                getHomeOrder()
                getCategories()
            }
        })
    }

    const collectionSelection = (value: any) => {
        const updatedIems = checkedCollection.includes(value)
            ? checkedCollection.filter((item: any) => item !== value)
            : [...checkedCollection, value]
        setCheckedCollection(updatedIems)
    }

    const setMultipleCollections = () => {
        api.put(endpoints.setMultipleCollections, {
            collections: checkedCollection,
            widgetId: widgetId,
            title: {
                en: collectionsLabelEn,
                ar: collectionsLabelAr
            },
        })
            .then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification
                            type="success"
                            title="Home Page updated"
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    setFormSubmitted(true)
                    setEditDialogOpen(false)
                }
            })
            .catch((err) => {
                toast.push(
                    <Notification
                        type="warning"
                        title={err.response.data.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )
            })
    }

    const handleInsuranceFile = (event: any) => {
        const file = event.target.files[0]
        const formData = new FormData()
        formData.append('video', file)
        api.post(endpoints.videoUpload, formData)
            .then((res) => {
                if (res.status == 200) {
                    setInsuranceForm({
                        ...insuranceForm,
                        image: res.data?.videoUrl,
                    })
                }
            })
            .catch((err) => {
                console.log('err', err)
            })
    }

    const handleVirtualTryFile = (event: any, type: any) => {
        const file = event.target.files[0]
        const formData = new FormData()
        formData.append('video', file)
        api.post(endpoints.videoUpload, formData)
            .then((res) => {
                if (res.status == 200) {
                    if (type == 'image')
                        setVirtualtryForm({
                            ...virtualtryForm,
                            image: res.data?.videoUrl,
                        })
                    else if (type == 'subImage')
                        setVirtualtryForm({
                            ...virtualtryForm,
                            subImage: res.data?.videoUrl,
                        })
                }
            })
            .catch((err) => {
                console.log('err', err)
            })
    }

    const handleValidation = () => {
        let isValid = true
        const newErrors = {
            title: '',
            description: '',
            buttonText: '',
            link: '',
            image: '',
        }
        if (!insuranceForm.image) {
            newErrors.image = 'Image is required'
            isValid = false
        }

        if (!insuranceForm.title.trim()) {
            newErrors.title = 'Title is required'
            isValid = false
        }

        if (!insuranceForm.description.trim()) {
            newErrors.description = 'Description is required'
            isValid = false
        }

        if (!insuranceForm.buttonText.trim()) {
            newErrors.buttonText = 'Button Text is required'
            isValid = false
        }

        if (!insuranceForm.link.trim()) {
            newErrors.link = 'Link is required'
            isValid = false
        }

        setFormErrors(newErrors)
        return isValid
    }

    const handleInsuranceForm = () => {
        if (handleValidation()) {
            api.post(endpoints.setBanner, {
                widgetId: widgetId,
                ...insuranceForm,
                title: {
                    en: insuranceForm.title,
                    ar: insuranceForm.titleAr
                },
                description: {
                    en: insuranceForm.description,
                    ar: insuranceForm.descriptionAr,
                },
                buttonText: {
                    en: insuranceForm.buttonText,
                    ar: insuranceForm.buttonTextAr,
                }
            })
                .then((res) => {
                    if (res.status == 200) {
                        toast.push(
                            <Notification
                                type="success"
                                title="Banner updated"
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                        setFormSubmitted(true)
                        setEditDialogOpen(false)
                    }
                })
                .catch((err) => {
                    toast.push(
                        <Notification
                            type="warning"
                            title={err.response.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                })
        } else {
            console.log('Form validation failed')
        }
    }

    const handleVirtualTryForm = () => {
        api.post(endpoints.setBanner, {
            widgetId: widgetId,
            ...virtualtryForm,
            title: {
                en: virtualtryForm?.title,
                ar: virtualtryForm?.titleAr,
            },
            description: {
                en: virtualtryForm?.description,
                ar: virtualtryForm?.descriptionAr,
            },
            buttonText: {
                en: virtualtryForm?.buttonText,
                ar: virtualtryForm?.buttonTextAr
            }
        })
            .then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification type="success" title="Banner updated" />,
                        {
                            placement: 'top-center',
                        }
                    )
                    setFormSubmitted(true)
                    setEditDialogOpen(false)
                }
            })
            .catch((err) => {
                toast.push(
                    <Notification
                        type="warning"
                        title={err.response.data.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )
            })
    }

    const handleBrandSelection = (event: any) => {
        const brandId = event.target.value
        const isChecked = event.target.checked

        api.put(endpoints.brandInHome + brandId, { inHome: isChecked }).then(
            (res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification
                            type="success"
                            title={`${res.data.result.name.en} is ${res.data.result.inHome ? 'added' : 'removed'
                                } in home`}
                        />,
                        { placement: 'top-center' }
                    )
                    getHomeOrder()
                    getBrands()
                }
            }
        )
    }

    const onDialogClose = (e: MouseEvent) => {
        let keywords: any = []
        if (collectionType == 'single-collection') {
            keywords.push({
                title: selectedCollections.label,
                keyword: selectedCollections.slug,
            })
        } else if (collectionType == 'multiple-collection') {
            keywords = selectedCollections.map((item: any) => {
                return {
                    title: item.label,
                    keyword: item.slug,
                }
            })
        }

        const data = {
            type: collectionType,
            keyword: keywords,
        }
        api.put(endpoints.addWidget + refid, data).then((res) => {
            if (res.status == 200) {
                setIsOpen(false)
                toast.push(
                    <Notification type="success" title={res.data.message} />,
                    {
                        placement: 'top-center',
                    }
                )
            }
        })
    }

    const handleCollectionTypeChange = (event: any) => {
        const selectedType = event.target.value
        setCollectionType(selectedType)
    }

    function saveOrder() {
        if (isUpdate) {
            api.put(endpoints.updateHomeOrder + refid, items).then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification
                            type="success"
                            title={res.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    setFormSubmitted(true)
                }
            })
        } else {
            api.post(endpoints.saveHomeOrder, { items: items }).then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification
                            type="success"
                            title={res.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                }
            })
        }
    }

    function getCollections() {
        api.get(endpoints.collections).then((res) => {
            if (res?.status == 200) {
                const collections = res?.data?.result || []
                setCollections(collections)
                const newCollectionOptions = collections.map(
                    (collection: any) => ({
                        value: collection._id,
                        label: collection.title.en,
                        slug: collection.slug,
                    })
                )
                setCollectionOptions(newCollectionOptions)
            }
        })
    }

    function getCategories() {
        api.get(endpoints.parentCategories)
            .then((res) => {
                if (res?.status == 200) {
                    const categories = res?.data?.result || []
                    categories.sort((a: any, b: any) => a.order - b.order)
                    setCategories(categories)
                }
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }

    function getBrands() {
        api.get(endpoints.brands)
            .then((res) => {
                if (res?.status == 200) {
                    const brands = res?.data?.result || []
                    setBrands(brands)
                }
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }

    const getSelectedCollection = () => {
        api.get(endpoints.getSelectedCollections).then((res) => {
            if (res?.status == 200) {
                const selected =
                    res?.data?.result.map((item: any) => item.id) || []
                setCheckedCollection(selected)
            }
        })
    }

    const getHomeOrder = () => {
        api.get(endpoints.homeOrder).then((res) => {
            if (res.status == 200) {
                setItems(res?.data?.result[0].items)
                setIsUpdate(true)
                setRefid(res?.data?.result[0].refid)
            }
        })
    }

    const updatecategoryTitle = () => {
        api.post(endpoints.setBanner, {
            widgetId: widgetId,
            title: {
                en: categoryTitleEn,
                ar: categoryTitleAr
            },
        })
            .then((res) => {
                if (res.status == 200) {
                    setEditDialogOpen(false)
                    toast.push(
                        <Notification
                            type="success"
                            title={res.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    getHomeOrder()
                }
            })
            .catch((err) => {
                toast.push(
                    <Notification
                        type="warning"
                        title={err.response.data.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )
            })
    }

    const updateFrameShapeTitle = () => {
        api.post(endpoints.setBanner, {
            widgetId: widgetId,
            title: {
                en: frameShapeTitleEn,
                ar: frameShapeTitleAr
            },
        })
            .then((res) => {
                if (res.status == 200) {
                    setEditDialogOpen(false)
                    toast.push(
                        <Notification
                            type="success"
                            title={res.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    getHomeOrder()
                }
            })
            .catch((err) => {
                toast.push(
                    <Notification
                        type="warning"
                        title={err.response.data.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )
            })
    }

    const updateShopByBrandTitle = () => {
        api.post(endpoints.setBanner, {
            widgetId: widgetId,
            title: {
                en: shopByBrandTitleEn,
                ar: shopByBrandTitleAr
            },
            buttonText: {
                en: shopByBrandBtnEn,
                ar: shopByBrandBtnAr
            }
        })
            .then((res) => {
                if (res.status == 200) {
                    setEditDialogOpen(false)
                    toast.push(
                        <Notification
                            type="success"
                            title={res.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    getHomeOrder()
                }
            })
            .catch((err) => {
                toast.push(
                    <Notification
                        type="warning"
                        title={err.response.data.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )
            })
    }

    const updateBrandsTitle = () => {
        api.post(endpoints.setBanner, {
            widgetId: widgetId,
            title: {
                en: brandsTitleEn,
                ar: brandsTitleAr
            },
        })
            .then((res) => {
                if (res.status == 200) {
                    setEditDialogOpen(false)
                    toast.push(
                        <Notification
                            type="success"
                            title={res.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    getHomeOrder()
                }
            })
            .catch((err) => {
                toast.push(
                    <Notification
                        type="warning"
                        title={err.response.data.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )
            })
    }

    const updateBeforeAfterTitle = () => {
        api.post(endpoints.setBanner, {
            widgetId: widgetId,
            title: {
                en: beforeAfterTitleEn,
                ar: beforeAfterTitleAr
            },
        })
            .then((res) => {
                if (res.status == 200) {
                    setEditDialogOpen(false)
                    toast.push(
                        <Notification
                            type="success"
                            title={res.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    getHomeOrder()
                }
            })
            .catch((err) => {
                toast.push(
                    <Notification
                        type="warning"
                        title={err.response.data.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )
            })
    }

    useEffect(() => {
        getHomeOrder()
        getCollections()
        getCategories()
        getBrands()
        getSelectedCollection()
    }, [formSubmitted])

    // brand banner
    const [currentBrandSlider, setCurrentBrandSlider] = useState<any>(null)
    const {
        fields: brandSliderFields,
        append: brandSliderAppend,
        remove: brandSliderRemove
    } = useFieldArray({
        control,
        name: 'brandSliderFields',
    })

    // brand banner

    const handleDragEnd = (result: any) => {
        if (!result.destination) return

        const newItems = Array.from(items)
        const [reorderedItem] = newItems.splice(result.source.index, 1)
        newItems.splice(result.destination.index, 0, reorderedItem)

        setItems(newItems)
    }

    const handleWidgetToggle = (widgetId: any, isActive: any) => {
        console.log('handleWidgetToggle', widgetId)
        console.log('handleWidgetToggle', isActive)
        api.put(endpoints.widgetStatus, {
            widgetId: widgetId,
            isActive: !isActive,
        })
            .then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification
                            type="success"
                            title={res.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    getHomeOrder()
                }
            })
            .catch((err) => {
                toast.push(
                    <Notification
                        type="warning"
                        title={err.response.data.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )
            })
        // setItems((prevItems:any) =>
        //     prevItems.map((item:any) =>
        //         item._id === widgetId ? { ...item, isActive: !item.isActive } : item
        //     )
        // );
    }

    const onSliderSubmit = (data: any) => {

        let slideIndex = null;
        let msg = "";

        for (let [key, slide] of data?.topSliderFields?.entries()) {
            if (slide?.type == "video") {
                if(slide?.srcType == "file"){
                    if (!slide?.video) {
                        slideIndex = key
                        msg = "Desktop video is required"
                    }
                }
            } else {
                if (!slide?.image) {
                    slideIndex = key
                    msg = "Image is required"
                }
            }
        }

        if (slideIndex != null) {
            setCurrentSlide(slideIndex)
            toast.push(
                <Notification
                    type="warning"
                    title={msg}
                />,
                {
                    placement: 'top-center',
                }
            )
            return
        }

        api.post(endpoints.setSlider, {
            widgetId: widgetId,
            sliders: data?.topSliderFields?.map((item: any) => ({ ...item, isActive: item?.isActive?.value == "false" ? false : true }))
        })
            .then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification
                            type="success"
                            title="Home Page updated"
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    setFormSubmitted(true)
                    setEditDialogOpen(false)
                }
            })
            .catch((err) => {
                toast.push(
                    <Notification
                        type="warning"
                        title={err.response.data.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )
            })
    }

    return (
        <div className="flex">
            <DragDropContext onDragEnd={handleDragEnd}>
                <Droppable droppableId="droppable">
                    {(provided, snapshot) => (
                        <div
                            {...provided.droppableProps}
                            ref={provided.innerRef}
                            style={{
                                background: snapshot.isDraggingOver
                                    ? 'lightblue'
                                    : 'lightgrey',
                                padding: 10,
                                width: 600,
                                minHeight: 200,
                            }}
                        >
                            {items.map((item: any, index: any) => (
                                <Draggable
                                    key={item._id}
                                    draggableId={item._id}
                                    index={index}
                                    isDragDisabled={!item.isActive}
                                >
                                    {(provided, snapshot) => (
                                        <div
                                            ref={provided.innerRef}
                                            {...provided.draggableProps}
                                            {...provided.dragHandleProps}
                                            style={{
                                                position: 'relative',
                                                userSelect: 'none',
                                                padding: 16,
                                                margin: '0 0 8px 0',
                                                minHeight: '50px',
                                                backgroundColor:
                                                    snapshot.isDragging
                                                        ? '#263B4A'
                                                        : '#456C86',
                                                color: 'white',
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'space-between',
                                                opacity: item.isActive
                                                    ? 1
                                                    : 0.5,
                                                ...provided.draggableProps
                                                    .style,
                                            }}
                                        >
                                            {(item.type == 'videoBanner') && (
                                                <img
                                                    src={`/yateem_parts/mainBanner.png`}
                                                    alt={`Item ${index + 1
                                                        }`}
                                                />
                                            )}

                                            {item.type == 'categories' && (
                                                <img
                                                    src={`/yateem_parts/categories.png`}
                                                    alt={`Item ${index + 1}`}
                                                />
                                            )}

                                            {item.type == 'shopByBrands' && (
                                                <img
                                                    src={`/yateem_parts/shopByBrands.png`}
                                                    alt={`Item ${index + 1}`}
                                                />
                                            )}

                                            {item.type ==
                                                'collectionsSingle' && (
                                                    <img
                                                        src="/yateem_parts/collectionsSingle.png"
                                                        alt={`Item ${index + 1}`}
                                                    />
                                                )}

                                            {item.type == 'imageMap' && (
                                                <img
                                                    src={`/yateem_parts/imageMap.png`}
                                                    alt={`Item ${index + 1}`}
                                                />
                                            )}

                                            {item.type ==
                                                'collectionsMultiple' && (
                                                    <img
                                                        src="/yateem_parts/collectionsMultiple.png"
                                                        alt={`Item ${index + 1}`}
                                                    />
                                                )}

                                            {item.type == 'insuranceBanner' && (
                                                <img
                                                    src="/yateem_parts/insuranceBanner.png"
                                                    alt={`Item ${index + 1}`}
                                                />
                                            )}

                                            {item.type ==
                                                'brandVideoBanner' && (
                                                    <img
                                                        src={`/yateem_parts/brandVideoBanner.png`}
                                                        alt={`Item ${index + 1}`}
                                                    />
                                                )}

                                            {item.type == 'threeGridBanner' && (
                                                <img
                                                    src={`/yateem_parts/threeGridBanner.png`}
                                                    alt={`Item ${index + 1}`}
                                                />
                                            )}

                                            {item.type == 'frameShape' && (
                                                <img
                                                    src={`/yateem_parts/frameShape.png`}
                                                    alt={`Item ${index + 1}`}
                                                />
                                            )}

                                            {item.type == 'beforeAfter' && (
                                                <img
                                                    src={`/yateem_parts/beforeAfter.png`}
                                                    alt={`Item ${index + 1}`}
                                                />
                                            )}

                                            {item.type == 'brands' && (
                                                <img
                                                    src={`/yateem_parts/brands.png`}
                                                    alt={`Item ${index + 1}`}
                                                />
                                            )}

                                            {item.type ==
                                                'brandsCollection' && (
                                                    <img
                                                        src={`/yateem_parts/brandsCollection.png`}
                                                        alt={`Item ${index + 1}`}
                                                    />
                                                )}

                                            {item.type ==
                                                'virtualTryBanner' && (
                                                    <img
                                                        src={`/yateem_parts/virtualTryBanner.png`}
                                                        alt={`Item ${index + 1}`}
                                                    />
                                                )}

                                            {item.type ==
                                                'newCollectionsSingle' && (
                                                    <img
                                                        src="/yateem_parts/collectionsSingle.png"
                                                        alt={`Item ${index + 1}`}
                                                    />
                                                )}

                                            <div className="flex flex-col gap-2 absolute right-0 top-0 left-unset">
                                                {
                                                    // item.type != 'brands' &&
                                                    // item.type != 'frameShape' &&
                                                    item.isActive && (
                                                        <Button
                                                            // className="absolute right-0 top-0"
                                                            variant="solid"
                                                            size="sm"
                                                            icon={<FaPen />}
                                                            onClick={() => {
                                                                if (
                                                                    item.type ==
                                                                    'imageMap'
                                                                )
                                                                    navigate(
                                                                        `/image-map/${item._id}`
                                                                    )
                                                                else
                                                                    openEditDialog(
                                                                        item._id,
                                                                        item.type,
                                                                        false
                                                                    )
                                                            }}
                                                        />
                                                    )}
                                                <Tooltip title="Section will be hidden in website Home page">
                                                    <Button
                                                        className="absolute right-0 top-12"
                                                        variant="solid"
                                                        size="sm"
                                                        icon={
                                                            item.isActive ? (
                                                                <IoCloseCircle />
                                                            ) : (
                                                                <IoCheckmarkCircle />
                                                            )
                                                        }
                                                        onClick={() =>
                                                            handleWidgetToggle(
                                                                item._id,
                                                                item.isActive
                                                            )
                                                        }
                                                    />
                                                </Tooltip>
                                            </div>
                                            {/* <Button 
                                                className='absolute right-0 top-12'
                                                variant='solid'
                                                size='sm'
                                                icon={<IoDuplicateOutline />}
                                                onClick={() => openEditDialog(null, item.type, true)} 
                                            />   */}
                                        </div>
                                    )}
                                </Draggable>
                            ))}
                            {provided.placeholder}
                        </div>
                    )}
                </Droppable>
            </DragDropContext>

            <Button className="ml-14 mt-3" variant="solid" onClick={saveOrder}>
                Apply Changes
            </Button>

            {/* <Button className='ml-14 mt-3' variant='solid' onClick={() => openDialog()}>
                Add Widget
            </Button> */}

            <div>
                <Dialog
                    width={700}
                    isOpen={dialogIsOpen}
                    onClose={() => {
                        setIsOpen(false)
                    }}
                    onRequestClose={() => {
                        setIsOpen(false)
                    }}
                >
                    <div className="mt-5">
                        <div className="flow-root">
                            <h5>Add New Widget</h5>
                            <h6 className="text-gray-500 mt-4">
                                Banner Widget
                            </h6>
                            <div className="mt-2 flex space-x-4">
                                <div className="text-gray-500 font-semibold text-sm border  border-gray-400 p-2">
                                    Full Banner
                                </div>
                                <div className="text-gray-500 font-semibold text-sm border rounded-md border-gray-400 p-2">
                                    3 Banner Layout
                                </div>
                            </div>

                            <h6 className="text-gray-500 mt-4">
                                Collection Widget
                            </h6>

                            <div className="grid grid-cols-2 gap-3">
                                <div className="flex items-center ps-4 border border-gray-200 rounded dark:border-gray-700">
                                    <input
                                        checked={
                                            collectionType ==
                                            'single-collection'
                                        }
                                        id="single-collection"
                                        type="radio"
                                        value="single-collection"
                                        name="bordered-radio"
                                        onChange={handleCollectionTypeChange}
                                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                                    />
                                    <label
                                        htmlFor="single-collection"
                                        className="w-full py-4 ms-2 text-sm font-medium text-gray-900 dark:text-gray-300"
                                    >
                                        Single Collection
                                    </label>
                                </div>
                                <div className="flex items-center ps-4 border border-gray-200 rounded dark:border-gray-700">
                                    <input
                                        checked={
                                            collectionType ==
                                            'multiple-collection'
                                        }
                                        id="multiple-collection"
                                        type="radio"
                                        value="multiple-collection"
                                        name="bordered-radio"
                                        onChange={handleCollectionTypeChange}
                                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                                    />
                                    <label
                                        htmlFor="multiple-collection"
                                        className="w-full py-4 ms-2 text-sm font-medium text-gray-900 dark:text-gray-300"
                                    >
                                        Multiple Collection
                                    </label>
                                </div>
                            </div>

                            <Select
                                className="mt-2"
                                options={collectionOptions}
                                isMulti={
                                    collectionType == 'multiple-collection'
                                }
                                value={selectedCollections}
                                onChange={handleCollectionSelection}
                            />

                            <Button
                                variant="solid"
                                className="mt-4 float-right"
                                onClick={onDialogClose}
                            >
                                Save
                            </Button>
                        </div>
                    </div>
                </Dialog>
            </div>

            <div>
                <Dialog
                    width={1000}
                    height={"85vh"}
                    isOpen={editDialogOpen}
                    onClose={() => {
                        setEditDialogOpen(false)
                    }}
                    onRequestClose={() => setEditDialogOpen(false)}
                >
                    {(widgetType == 'videoBanner') && (
                        <div className="flex flex-col max-h-full">
                            <h4>Slides</h4>
                            <div className="flex gap-2 mt-2 max-w-full overflow-x-auto min-h-fit">
                                {topSliderFields.map((item: any, index: number) => (
                                    <button type='button'
                                        onClick={() => setCurrentSlide(index)}
                                        className={`p-2 px-4 font-semibold duration-200 rounded border border-indigo-700 ${currentSlide == index ? 'bg-indigo-700 text-white' : 'text-indigo-700 bg-white'}`}
                                    >
                                        {index + 1}
                                    </button>
                                ))}
                                <button type='button'
                                    onClick={() => {
                                        topSliderAppend({
                                            type: "image",
                                            video: "",
                                            mobileVideo: "",
                                            link: "",
                                            image: "",
                                            isActive: { label: "True", value: "true" }
                                        })

                                        setCurrentSlide(topSliderFields.length)
                                    }}
                                    className={`p-2 px-4 font-semibold duration-200 rounded border border-indigo-700 bg-indigo-700 text-white`}
                                >
                                    +
                                </button>
                            </div>
                            {topSliderFields.length > 1 && <div className="w-full flex mt-1 mb-2">
                                <Button onClick={deleteTopSlider} variant="solid" color="red" className='ml-auto'>
                                    Delete slide {currentSlide + 1}
                                </Button>
                            </div>}
                            <form className='flex flex-col max-h-full mt-2 overflow-y-auto' onSubmit={handleSubmit(onSliderSubmit)}>
                                <div className="flow-root max-h-full overflow-y-auto">
                                    <>

                                        {topSliderFields?.map((item: any, index: number) => {
                                            if (currentSlide != index) return null;
                                            const type = watch(`topSliderFields.${index}.type`)
                                            const srcType = watch(`topSliderFields.${index}.srcType`) ?? "file"
                                            return <>
                                                <div className="my-2 grid grid-cols-2 gap-3">
                                                    <div className="flex items-center ps-4 border border-gray-200 rounded dark:border-gray-700">
                                                        <input
                                                            checked={
                                                                type == 'video'
                                                            }
                                                            id="VideoRadio"
                                                            type="radio"
                                                            value="videoBanner"
                                                            name="bordered-radio"
                                                            className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                                                            onChange={() =>
                                                                setValue(`topSliderFields.${index}.type`, "video")
                                                            }
                                                        />
                                                        <label
                                                            htmlFor="VideoRadio"
                                                            className="w-full py-4 ms-2 text-sm font-medium text-gray-900 dark:text-gray-300"
                                                        >
                                                            Video Slider
                                                        </label>
                                                    </div>
                                                    <div className="flex items-center ps-4 border border-gray-200 rounded dark:border-gray-700">
                                                        <input
                                                            checked={
                                                                type == 'image'
                                                            }
                                                            id="imageRadio"
                                                            type="radio"
                                                            value="mainBanner"
                                                            name="bordered-radio"
                                                            onChange={() =>
                                                                setValue(`topSliderFields.${index}.type`, "image")
                                                            }
                                                            className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                                                        />
                                                        <label
                                                            htmlFor="imageRadio"
                                                            className="w-full py-4 ms-2 text-sm font-medium text-gray-900 dark:text-gray-300"
                                                        >
                                                            Image Slider
                                                        </label>
                                                    </div>
                                                    <FormItem label="is Active ?">
                                                        <Controller
                                                            name={`topSliderFields.${index}.isActive`}
                                                            defaultValue=""
                                                            control={control}
                                                            render={({ field }) => (
                                                                <Select
                                                                    defaultValue=""
                                                                    options={options}
                                                                    {...field}
                                                                />
                                                            )}
                                                        />
                                                    </FormItem>
                                                </div>
                                                {type == 'video' && (

                                                    <>
                                                        <div className="grid grid-cols-1 gap-4">
                                                            <FormItem label="Redirection Link">
                                                                <Controller
                                                                    name={`topSliderFields.${index}.link`}
                                                                    control={
                                                                        control
                                                                    }
                                                                    defaultValue=""
                                                                    render={({
                                                                        field,
                                                                    }) => (
                                                                        <Input
                                                                            type="text"
                                                                            {...field}
                                                                        />
                                                                    )}
                                                                />
                                                            </FormItem>
                                                        </div>
                                                        <label>Source Type</label>
                                                        <div className="grid grid-cols-2 gap-4 my-2">
                                                            <div className="flex items-center ps-4 border border-gray-200 rounded dark:border-gray-700">
                                                                <input
                                                                    checked={
                                                                        srcType == 'file'
                                                                    }
                                                                    id="fileRadio"
                                                                    type="radio"
                                                                    value="file"
                                                                    name="src-radio"
                                                                    onChange={() =>
                                                                        setValue(`topSliderFields.${index}.srcType`, "file")
                                                                    }
                                                                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                                                                />
                                                                <label
                                                                    htmlFor="fileRadio"
                                                                    className="w-full py-4 ms-2 text-sm font-medium text-gray-900 dark:text-gray-300"
                                                                >
                                                                    File
                                                                </label>
                                                            </div>
                                                            <div className="flex items-center ps-4 border border-gray-200 rounded dark:border-gray-700">
                                                                <input
                                                                    checked={
                                                                        srcType == 'link'
                                                                    }
                                                                    id="linkRadio"
                                                                    type="radio"
                                                                    value="link"
                                                                    name="src-radio"
                                                                    onChange={() =>
                                                                        setValue(`topSliderFields.${index}.srcType`, "link")
                                                                    }
                                                                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                                                                />
                                                                <label
                                                                    htmlFor="linkRadio"
                                                                    className="w-full py-4 ms-2 text-sm font-medium text-gray-900 dark:text-gray-300"
                                                                >
                                                                    Link
                                                                </label>
                                                            </div>
                                                        </div>
                                                        {srcType == 'file' && <><div className="grid grid-cols-2 w-full items-center">
                                                            <PhoneMockup>
                                                                <video
                                                                    className="h-full object-cover"
                                                                    autoPlay
                                                                    controls
                                                                    loop
                                                                    src={
                                                                        baseUrl +
                                                                        watch(`topSliderFields.${index}.mobileVideo`)
                                                                    }
                                                                />
                                                            </PhoneMockup>
                                                            <LaptopMockup>
                                                                <video
                                                                    className="h-full object-cover"
                                                                    autoPlay
                                                                    controls
                                                                    loop
                                                                    src={
                                                                        baseUrl +
                                                                        watch(`topSliderFields.${index}.video`)
                                                                    }
                                                                />
                                                            </LaptopMockup>
                                                        </div>
                                                            <div className="grid grid-cols-2 gap-4">
                                                                <FormItem label="Mobile Video (Preferred size: 720x1280px)">
                                                                    <Input
                                                                        type="file"
                                                                        id="mobileVideo"
                                                                        accept="video/mp4,video/x-m4v,video/*"
                                                                        onChange={(e) => handleSliderVideo(e, `topSliderFields.${index}.mobileVideo`)}
                                                                    />
                                                                    {/* {mainVideoForm?.video && ( */}

                                                                    {/* )} */}
                                                                </FormItem>
                                                                <FormItem label="Desktop Video (Preferred size: 1920x1080px)">
                                                                    <Input
                                                                        type="file"
                                                                        id="video"
                                                                        accept="video/mp4,video/x-m4v,video/*"
                                                                        onChange={(e) => handleSliderVideo(e, `topSliderFields.${index}.video`)}
                                                                    />
                                                                    {/* {mainVideoForm?.video && ( */}

                                                                    {/* )} */}
                                                                </FormItem>
                                                            </div>
                                                        </>}
                                                        {
                                                            srcType == 'link' && (
                                                                <div className="grid grid-cols-1">
                                                                    <FormItem label="Source Link">
                                                                        <Controller
                                                                            name={`topSliderFields.${index}.src`}
                                                                            control={
                                                                                control
                                                                            }
                                                                            defaultValue=""
                                                                            rules={{
                                                                                required:
                                                                                    'Field is required',
                                                                            }}
                                                                            render={({
                                                                                field,
                                                                            }) => (
                                                                                <Input
                                                                                    type="text"
                                                                                    {...field}
                                                                                />
                                                                            )}
                                                                        />
                                                                    </FormItem>
                                                                    <FormItem label="Mobile Source Link">
                                                                        <Controller
                                                                            name={`topSliderFields.${index}.mobileSrc`}
                                                                            control={
                                                                                control
                                                                            }
                                                                            defaultValue=""
                                                                            // rules={{
                                                                            //     required:
                                                                            //         'Field is required',
                                                                            // }}
                                                                            render={({
                                                                                field,
                                                                            }) => (
                                                                                <Input
                                                                                    type="text"
                                                                                    {...field}
                                                                                />
                                                                            )}
                                                                        />
                                                                    </FormItem>
                                                                </div>
                                                            )
                                                        }
                                                    </>
                                                )}

                                                {type == 'image' && (

                                                    <div
                                                        className="rounded-md  mt-2 py-6 px-3 relative"
                                                    >
                                                        <div className='grid grid-cols-2 gap-2'>
                                                            <FormItem label="Image (Preferred size: 3840x2160px)">
                                                                {/* <Controller
                                                                    name={`topSliderFields.${index}.image`}
                                                                    control={
                                                                        control
                                                                    }
                                                                    defaultValue=""
                                                                    render={({
                                                                        field,
                                                                    }) => (
                                                                        <div> */}
                                                                <Input
                                                                    type="file"
                                                                    // {...field}
                                                                    defaultValue={""}
                                                                    onChange={(
                                                                        e
                                                                    ) => {
                                                                        // field.onChange(
                                                                        //     e
                                                                        // )
                                                                        handleSliderImages(
                                                                            e,
                                                                            index
                                                                        )
                                                                    }}
                                                                />
                                                                {watch(`topSliderFields.${index}.image`) && (
                                                                    <img
                                                                        className="mt-2 w-20 mx-auto object-cover object-top"
                                                                        src={
                                                                            baseUrl +
                                                                            watch(`topSliderFields.${index}.image`)
                                                                        }
                                                                        alt={`Image Preview`}
                                                                    />
                                                                )}
                                                                {/* </div> */}
                                                                {/* )} */}
                                                                {/* /> */}
                                                            </FormItem>
                                                            <FormItem label="Mobile Image (Preferred size: 3840x2160px)">
                                                                {/* <Controller
                                                                    name={`topSliderFields.${index}.image`}
                                                                    control={
                                                                        control
                                                                    }
                                                                    defaultValue=""
                                                                    render={({
                                                                        field,
                                                                    }) => (
                                                                        <div> */}
                                                                <Input
                                                                    type="file"
                                                                    // {...field}
                                                                    defaultValue={""}
                                                                    onChange={(
                                                                        e
                                                                    ) => {
                                                                        // field.onChange(
                                                                        //     e
                                                                        // )
                                                                        handleSliderImages(
                                                                            e,
                                                                            index,
                                                                            "mobileImage"
                                                                        )
                                                                    }}
                                                                />
                                                                {watch(`topSliderFields.${index}.mobileImage`) && (
                                                                    <img
                                                                        className="mt-2 w-20 mx-auto object-cover object-top"
                                                                        src={
                                                                            baseUrl +
                                                                            watch(`topSliderFields.${index}.mobileImage`)
                                                                        }
                                                                        alt={`Image Preview`}
                                                                    />
                                                                )}
                                                                {/* </div> */}
                                                                {/* )} */}
                                                                {/* /> */}
                                                            </FormItem>
                                                        </div>
                                                        <div className="grid grid-cols-1 gap-2">
                                                            <FormItem label="Link">
                                                                <Controller
                                                                    name={`topSliderFields.${index}.link`}
                                                                    control={
                                                                        control
                                                                    }
                                                                    defaultValue=""
                                                                    render={({
                                                                        field,
                                                                    }) => (
                                                                        <Input
                                                                            type="text"
                                                                            {...field}
                                                                        />
                                                                    )}
                                                                />
                                                                {errors.topSliderFields &&
                                                                    (
                                                                        errors.topSliderFields as any
                                                                    )[currentSlide]
                                                                        ?.link && (
                                                                        <small className="text-red-600 py-3">
                                                                            {
                                                                                (
                                                                                    errors.topSliderFields as any
                                                                                )[
                                                                                    currentSlide
                                                                                ]
                                                                                    ?.link
                                                                                    .message as string
                                                                            }
                                                                        </small>
                                                                    )}
                                                            </FormItem>
                                                        </div>

                                                    </div>

                                                )}
                                            </>

                                        })}
                                    </>
                                </div>
                                <Button
                                    // type='button'
                                    variant="solid"
                                    className="mt-4 float-right ml-auto"
                                // onClick={mainVideoUpload}
                                >
                                    Save
                                </Button>
                            </form>
                        </div>
                    )}
                    {(widgetType == 'r' || widgetType == 'r') && (

                        <div className="flow-root max-h-full overflow-y-auto mt-4">
                            <div className="my-2 grid grid-cols-2 gap-3">
                                <div className="flex items-center ps-4 border border-gray-200 rounded dark:border-gray-700">
                                    <input
                                        checked={
                                            mainBannerType == 'videoBanner'
                                        }
                                        id="VideoRadio"
                                        type="radio"
                                        value="videoBanner"
                                        name="bordered-radio"
                                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                                        onChange={() =>
                                            changeMainBannerType(
                                                'videoBanner'
                                            )
                                        }
                                    />
                                    <label
                                        htmlFor="VideoRadio"
                                        className="w-full py-4 ms-2 text-sm font-medium text-gray-900 dark:text-gray-300"
                                    >
                                        Video Banner
                                    </label>
                                </div>
                                <div className="flex items-center ps-4 border border-gray-200 rounded dark:border-gray-700">
                                    <input
                                        checked={
                                            mainBannerType == 'mainBanner'
                                        }
                                        id="imageRadio"
                                        type="radio"
                                        value="mainBanner"
                                        name="bordered-radio"
                                        onChange={() =>
                                            changeMainBannerType(
                                                'mainBanner'
                                            )
                                        }
                                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                                    />
                                    <label
                                        htmlFor="imageRadio"
                                        className="w-full py-4 ms-2 text-sm font-medium text-gray-900 dark:text-gray-300"
                                    >
                                        Image Banner
                                    </label>
                                </div>
                                <div className="flex items-center ps-4 border border-gray-200 rounded dark:border-gray-700">
                                    <input
                                        checked={
                                            mainBannerType == 'src'
                                        }
                                        id="srcRadio"
                                        type="radio"
                                        value="mainBanner"
                                        name="bordered-radio"
                                        onChange={() =>
                                            changeMainBannerType(
                                                'src'
                                            )
                                        }
                                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
                                    />
                                    <label
                                        htmlFor="srcRadio"
                                        className="w-full py-4 ms-2 text-sm font-medium text-gray-900 dark:text-gray-300"
                                    >
                                        Src Banner
                                    </label>
                                </div>
                            </div>
                            {mainBannerType == 'videoBanner' && (
                                <>
                                    <div className="grid grid-cols-1 gap-4">
                                        <FormItem label="Redirection Link">
                                            <Input
                                                type="text"
                                                onChange={(e) =>
                                                    setMainVideoForm({
                                                        ...mainVideoForm,
                                                        link: e.target
                                                            .value,
                                                    })
                                                }
                                                value={mainVideoForm?.link}
                                            />
                                        </FormItem>
                                    </div>
                                    <div className="grid grid-cols-2 w-full items-center">
                                        <PhoneMockup>
                                            <video
                                                className="h-full object-cover"
                                                autoPlay
                                                controls
                                                loop
                                                src={
                                                    baseUrl +
                                                    mainVideoForm?.mobileVideo
                                                }
                                            />
                                        </PhoneMockup>
                                        <LaptopMockup>
                                            <video
                                                className="h-full object-cover"
                                                autoPlay
                                                controls
                                                loop
                                                src={
                                                    baseUrl +
                                                    mainVideoForm?.video
                                                }
                                            />
                                        </LaptopMockup>
                                    </div>
                                    <div className="grid grid-cols-2 gap-4">
                                        <FormItem label="Mobile Video (Preferred size: 720x1280px)">
                                            <Input
                                                type="file"
                                                id="mobileVideo"
                                                accept="video/mp4,video/x-m4v,video/*"
                                                onChange={handleVideoFile}
                                            />
                                            {/* {mainVideoForm?.video && ( */}

                                            {/* )} */}
                                        </FormItem>
                                        <FormItem label="Desktop Video (Preferred size: 1920x1080px)">
                                            <Input
                                                type="file"
                                                id="video"
                                                accept="video/mp4,video/x-m4v,video/*"
                                                onChange={handleVideoFile}
                                            />
                                            {/* {mainVideoForm?.video && ( */}

                                            {/* )} */}
                                        </FormItem>
                                    </div>
                                    <Button
                                        variant="solid"
                                        className="mt-4 float-right"
                                        onClick={mainVideoUpload}
                                    >
                                        Save
                                    </Button>
                                </>
                            )}

                            {mainBannerType == 'mainBanner' && (
                                <form
                                    onSubmit={handleSubmit(
                                        handleMainBannerSubmit
                                    )}
                                >
                                    <ul className="mt-6">
                                        {mainBannerFields.map(
                                            (item, index) => {
                                                return (
                                                    <li
                                                        key={item.id}
                                                        className="border-2 rounded-md border-gray-400 mt-2 py-6 px-3 relative"
                                                    >
                                                        <div className="grid grid-cols-2 gap-2">
                                                            <FormItem label="Link">
                                                                <Controller
                                                                    name={`mainBannerFields.${index}.link`}
                                                                    control={
                                                                        control
                                                                    }
                                                                    defaultValue=""
                                                                    rules={{
                                                                        required:
                                                                            'Field is required',
                                                                    }}
                                                                    render={({
                                                                        field,
                                                                    }) => (
                                                                        <Input
                                                                            type="text"
                                                                            {...field}
                                                                        />
                                                                    )}
                                                                />
                                                                {errors.mainBannerFields &&
                                                                    (
                                                                        errors.mainBannerFields as any
                                                                    )[index]
                                                                        ?.link && (
                                                                        <small className="text-red-600 py-3">
                                                                            {
                                                                                (
                                                                                    errors.mainBannerFields as any
                                                                                )[
                                                                                    index
                                                                                ]
                                                                                    ?.link
                                                                                    .message as string
                                                                            }
                                                                        </small>
                                                                    )}
                                                            </FormItem>
                                                        </div>

                                                        <div>
                                                            <FormItem label="Image (Preferred size: 3840x2160px)">
                                                                <Controller
                                                                    name={`mainBannerFields.${index}.image`}
                                                                    control={
                                                                        control
                                                                    }
                                                                    defaultValue=""
                                                                    render={({
                                                                        field,
                                                                    }) => (
                                                                        <div>
                                                                            <Input
                                                                                type="file"
                                                                                {...field}
                                                                                onChange={(
                                                                                    e
                                                                                ) => {
                                                                                    field.onChange(
                                                                                        e
                                                                                    )
                                                                                    handleImages(
                                                                                        e,
                                                                                        index,
                                                                                        'mainBannerFields'
                                                                                    )
                                                                                }}
                                                                            />
                                                                            {mainBannerPreviews[
                                                                                index
                                                                            ] && (
                                                                                    <img
                                                                                        className="mt-2 w-20 mx-auto object-cover object-top"
                                                                                        src={
                                                                                            baseUrl +
                                                                                            mainBannerPreviews[
                                                                                            index
                                                                                            ]
                                                                                        }
                                                                                        alt={`Image Preview ${index}`}
                                                                                    />
                                                                                )}
                                                                        </div>
                                                                    )}
                                                                />
                                                            </FormItem>
                                                        </div>

                                                        <div className="absolute right-0 top-0">
                                                            <IoIosCloseCircleOutline
                                                                size={30}
                                                                color="red"
                                                                onClick={() => {
                                                                    mainBannerRemove(
                                                                        index
                                                                    )
                                                                }}
                                                            />
                                                        </div>
                                                    </li>
                                                )
                                            }
                                        )}
                                    </ul>

                                    <Button
                                        variant="solid"
                                        className="mt-4"
                                        onClick={() => mainBannerAppend({})}
                                    >
                                        Add More
                                    </Button>

                                    <Button
                                        variant="solid"
                                        className="mt-4 float-right"
                                        type="submit"
                                    >
                                        Save
                                    </Button>
                                </form>
                            )}
                        </div>
                    )}

                    {widgetType == 'categories' && (
                        // <div className="mt-5 max-h-full overflow-y-auto">
                        //     <div className="flow-root">
                        //         <h5>Select Categories</h5>
                        //         <div className='my-3'>
                        //             <input
                        //             placeholder='Categories Title'
                        //             className='w-[250px] p-3 border border-gray-300 rounded-lg'
                        //             type="text"
                        //             value={categoryTitle}
                        //             onChange={(e) => setCategoryTitle(e.target.value)}
                        //             />
                        //             <Button
                        //                 variant="solid"
                        //                 className="ms-4"
                        //                 onClick={() => updatecategoryTitle()}
                        //              >
                        //                 Save
                        //                 </Button>
                        //         </div>
                        //         <ul className="grid w-full gap-4 md:grid-cols-4 mb-4">
                        //             {categories.map(
                        //                 (category: any, index: any) => (
                        //                     <li key={category.refid}>
                        //                         <input
                        //                             onChange={
                        //                                 handleCategorySelection
                        //                             }
                        //                             type="checkbox"
                        //                             id={category.refid}
                        //                             value={category.refid}
                        //                             className="hidden peer"
                        //                             checked={category.inHome}
                        //                         />
                        //                         <label
                        //                             htmlFor={category.refid}
                        //                             className="inline-flex items-center justify-between w-full p-5 text-gray-500 bg-white border-2 border-gray-200 rounded-lg cursor-pointer dark:hover:text-gray-300 dark:border-gray-700 peer-checked:border-blue-600 hover:text-gray-600 dark:peer-checked:text-gray-300 peer-checked:text-gray-600 hover:bg-gray-50 dark:text-gray-400 dark:bg-gray-800 dark:hover:bg-gray-700"
                        //                         >
                        //                             <div className="block">
                        //                                 <div className="w-full text-lg font-semibold">
                        //                                     {category.name.en}
                        //                                 </div>
                        //                                 <img
                        //                                     className="mt-2 w-28 h-18 mx-auto"
                        //                                     src={`${baseUrl}${category.image}`}
                        //                                     alt="category image"
                        //                                 />
                        //                             </div>
                        //                         </label>
                        //                     </li>
                        //                 )
                        //             )}
                        //         </ul>
                        //     </div>
                        // </div>
                        <div className="mt-5 max-h-full overflow-y-auto">
                            <div className="flow-root">
                                <h5>Select Categories</h5>
                                <div className="my-3 flex">
                                    <div className="flex flex-col">
                                        <label>English</label>
                                        <input
                                            placeholder="Categories Title"
                                            className="w-[250px] p-3 border border-gray-300 rounded-lg"
                                            type="text"
                                            value={categoryTitleEn}
                                            onChange={(e) =>
                                                setCategoryTitleEn(e.target.value)
                                            }
                                        />
                                    </div>
                                    <div className="flex flex-col ml-4">
                                        <label>Arabic</label>
                                        <input
                                            dir="rtl"
                                            placeholder="Categories Title"
                                            className="w-[250px] p-3 border border-gray-300 rounded-lg"
                                            type="text"
                                            value={categoryTitleAr}
                                            onChange={(e) =>
                                                setCategoryTitleAr(e.target.value)
                                            }
                                        />
                                    </div>
                                    <Button
                                        variant="solid"
                                        className="ms-4 mt-auto"
                                        onClick={updatecategoryTitle}
                                    >
                                        Save
                                    </Button>
                                </div>
                                <div className="grid grid-cols-5 gap-2">
                                    <DndContext
                                        sensors={sensors}
                                        collisionDetection={closestCenter}
                                        onDragEnd={handleDragEndCat}
                                        onDragStart={handleDragStart}
                                    >
                                        <SortableContext
                                            items={categories.map((item: any) => item?.refid)}
                                            strategy={rectSortingStrategy}
                                        >
                                            {categories.map((item: any) => <CategoryDrag key={item?.refid} id={item?.refid}>
                                                <Item handleCategorySelection={handleCategorySelection} category={item} />
                                            </CategoryDrag>)}
                                        </SortableContext>
                                        {createPortal(
                                            <DragOverlay>
                                                {activeCat ? <Item handleCategorySelection={handleCategorySelection} category={activeCat} /> : null}
                                            </DragOverlay>,
                                            document.getElementById("root") as any
                                        )}

                                    </DndContext>
                                </div>
                                {/* <DragDropContext
                                    onDragEnd={handleDragEnd}
                                >
                                    <Droppable
                                        droppableId="categories"
                                        direction="horizontal"
                                    >
                                        {(provided) => (
                                            <ul
                                                className="grid w-full gap-4 md:grid-cols-4 mb-4"
                                                {...provided.droppableProps}
                                                ref={provided.innerRef}
                                            >
                                                {categories.map(
                                                    (
                                                        category: any,
                                                        index: any
                                                    ) => (
                                                        <Draggable
                                                            key={category.refid}
                                                            draggableId={
                                                                category.refid
                                                            }
                                                            index={index}
                                                        >
                                                            {(provided) => (
                                                                <li
                                                                    ref={
                                                                        provided.innerRef
                                                                    }
                                                                    {...provided.draggableProps}
                                                                    {...provided.dragHandleProps}
                                                                >
                                                                    <input
                                                                        onChange={
                                                                            handleCategorySelection
                                                                        }
                                                                        type="checkbox"
                                                                        id={
                                                                            category.refid
                                                                        }
                                                                        value={
                                                                            category.refid
                                                                        }
                                                                        className="hidden peer"
                                                                        checked={
                                                                            category.inHome
                                                                        }
                                                                    />
                                                                    <label
                                                                        htmlFor={
                                                                            category.refid
                                                                        }
                                                                        className="inline-flex items-center justify-between w-full p-5 text-gray-500 bg-white border-2 border-gray-200 rounded-lg cursor-pointer dark:hover:text-gray-300 dark:border-gray-700 peer-checked:border-blue-600 hover:text-gray-600 dark:peer-checked:text-gray-300 peer-checked:text-gray-600 hover:bg-gray-50 dark:text-gray-400 dark:bg-gray-800 dark:hover:bg-gray-700"
                                                                    >
                                                                        <div className="block">
                                                                            <div className="w-full text-lg font-semibold">
                                                                                {
                                                                                    category
                                                                                        .name
                                                                                        .en
                                                                                }
                                                                            </div>
                                                                            <img
                                                                                className="mt-2 w-28 h-18 mx-auto"
                                                                                src={`${baseUrl}${category.image}`}
                                                                                alt="category image"
                                                                            />
                                                                        </div>
                                                                    </label>
                                                                </li>
                                                            )}
                                                        </Draggable>
                                                    )
                                                )}
                                                {provided.placeholder}
                                            </ul>
                                        )}
                                    </Droppable>
                                </DragDropContext> */}
                            </div>
                        </div>
                    )}

                    {widgetType == 'shopByBrands' && (
                        <div className="mt-5 max-h-full overflow-y-auto">
                            <div className="flow-root">
                                <h5>Select Brands</h5>
                                <div className="my-3 flex">
                                    <div className="flex flex-col">
                                        <label>English</label>
                                        <input
                                            placeholder="Categories Title"
                                            className="w-[250px] p-3 border border-gray-300 rounded-lg"
                                            type="text"
                                            value={shopByBrandTitleEn}
                                            onChange={(e) =>
                                                setShopByBrandTitleEn(e.target.value)
                                            }
                                        />
                                    </div>
                                    <div className="flex flex-col ml-4">
                                        <label>Arabic</label>
                                        <input
                                            dir="rtl"
                                            placeholder="Categories Title"
                                            className="w-[250px] p-3 border border-gray-300 rounded-lg"
                                            type="text"
                                            value={shopByBrandTitleAr}
                                            onChange={(e) =>
                                                setShopByBrandTitleAr(e.target.value)
                                            }
                                        />
                                    </div>
                                    {/* <Button
                                        variant="solid"
                                        className="ms-4 mt-auto"
                                        onClick={updateShopByBrandTitle}
                                    >
                                        Save
                                    </Button> */}
                                </div>
                                <div className="my-3 flex">
                                    <div className="flex flex-col">
                                        <label>Button Text English</label>
                                        <input
                                            className="w-[250px] p-3 border border-gray-300 rounded-lg"
                                            type="text"
                                            value={shopByBrandBtnEn}
                                            onChange={(e) =>
                                                setShopByBrandBtnEn(e.target.value)
                                            }
                                        />
                                    </div>
                                    <div className="flex flex-col ml-4">
                                        <label>Button Text Arabic</label>
                                        <input
                                            dir="rtl"
                                            className="w-[250px] p-3 border border-gray-300 rounded-lg"
                                            type="text"
                                            value={shopByBrandBtnAr}
                                            onChange={(e) =>
                                                setShopByBrandBtnAr(e.target.value)
                                            }
                                        />
                                    </div>
                                    <Button
                                        variant="solid"
                                        className="ms-4 mt-auto"
                                        onClick={updateShopByBrandTitle}
                                    >
                                        Save
                                    </Button>
                                </div>
                                <ul className="grid w-full gap-4 md:grid-cols-4 mb-4">
                                    {brands.map((brand: any, index: any) => (
                                        <li key={brand.refid}>
                                            <input
                                                onChange={handleBrandSelection}
                                                type="checkbox"
                                                id={brand.refid}
                                                value={brand.refid}
                                                className="hidden peer"
                                                checked={brand.inHome}
                                            />
                                            <label
                                                htmlFor={brand.refid}
                                                className="inline-flex items-center justify-between w-full p-5 text-gray-500 bg-white border-2 border-gray-200 rounded-lg cursor-pointer dark:hover:text-gray-300 dark:border-gray-700 peer-checked:border-blue-600 hover:text-gray-600 dark:peer-checked:text-gray-300 peer-checked:text-gray-600 hover:bg-gray-50 dark:text-gray-400 dark:bg-gray-800 dark:hover:bg-gray-700"
                                            >
                                                <div className="block">
                                                    <div className="w-full text-lg font-semibold">
                                                        {brand.name.en}
                                                    </div>
                                                    <img
                                                        className="mt-2 w-28 h-18 mx-auto"
                                                        src={`${baseUrl}${brand.image}`}
                                                        alt="brand image"
                                                    />
                                                </div>
                                            </label>
                                        </li>
                                    ))}
                                </ul>
                            </div>
                        </div>
                    )}

                    {widgetType == 'brands' && (
                        <div className="mt-5 max-h-full overflow-y-auto">
                            <div className="flow-root">
                                <h5>Title</h5>
                                <div className="my-3 flex">
                                    <div className="flex flex-col">
                                        <label>English</label>
                                        <input
                                            className="w-[250px] p-3 border border-gray-300 rounded-lg"
                                            type="text"
                                            value={brandsTitleEn}
                                            onChange={(e) =>
                                                setBrandsTitleEn(e.target.value)
                                            }
                                        />
                                    </div>
                                    <div className="flex flex-col ml-4">
                                        <label>Arabic</label>
                                        <input
                                            dir="rtl"
                                            className="w-[250px] p-3 border border-gray-300 rounded-lg"
                                            type="text"
                                            value={brandsTitleAr}
                                            onChange={(e) =>
                                                setBrandsTitleAr(e.target.value)
                                            }
                                        />
                                    </div>
                                    <Button
                                        variant="solid"
                                        className="ms-4 mt-auto"
                                        onClick={updateBrandsTitle}
                                    >
                                        Save
                                    </Button>
                                </div>
                            </div>
                        </div>
                    )}
                    {widgetType == 'frameShape' && (
                        <div className="mt-5 max-h-full overflow-y-auto">
                            <div className="flow-root">
                                <h5>Title</h5>
                                <div className="my-3 flex">
                                    <div className="flex flex-col">
                                        <label>English</label>
                                        <input
                                            className="w-[250px] p-3 border border-gray-300 rounded-lg"
                                            type="text"
                                            value={frameShapeTitleEn}
                                            onChange={(e) =>
                                                setFrameShapeTitleEn(e.target.value)
                                            }
                                        />
                                    </div>
                                    <div className="flex flex-col ml-4">
                                        <label>Arabic</label>
                                        <input
                                            dir="rtl"
                                            className="w-[250px] p-3 border border-gray-300 rounded-lg"
                                            type="text"
                                            value={frameShapeTitleAr}
                                            onChange={(e) =>
                                                setFrameShapeTitleAr(e.target.value)
                                            }
                                        />
                                    </div>
                                    <Button
                                        variant="solid"
                                        className="ms-4 mt-auto"
                                        onClick={updateFrameShapeTitle}
                                    >
                                        Save
                                    </Button>
                                </div>
                            </div>
                        </div>
                    )}

                    {widgetType == 'collectionsSingle' && (
                        <div className="max-h-full overflow-y-auto flex flex-col">
                            <h3 className="mb-5 mt-5">Select Collection</h3>
                            <ul className="grid w-full gap-6 md:grid-cols-4">
                                {collections.map(
                                    (collection: any, index: any) => (
                                        <li key={index}>
                                            <input
                                                type="radio"
                                                id={collection?._id}
                                                name={`collection_${index}`}
                                                value={collection?._id}
                                                className="hidden peer"
                                                required
                                                onChange={handleRadioChange}
                                                checked={
                                                    singleCollection ==
                                                    collection?._id
                                                }
                                            />
                                            <label
                                                htmlFor={collection?._id}
                                                className="inline-flex items-center justify-between w-full p-5 text-gray-500 bg-white border border-gray-200 rounded-lg cursor-pointer dark:hover:text-gray-300 dark:border-gray-700 dark:peer-checked:text-blue-500 peer-checked:border-blue-600 peer-checked:text-blue-600 hover:text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:bg-gray-800 dark:hover:bg-gray-700"
                                            >
                                                <div className="block">
                                                    <div className="w-full text-lg font-semibold">
                                                        {collection?.title?.en}
                                                    </div>
                                                    <img
                                                        className="mt-2 w-28 h-18 mx-auto"
                                                        src={`${baseUrl}${collection?.image}`}
                                                        alt="collection image"
                                                    />
                                                </div>
                                            </label>
                                        </li>
                                    )
                                )}
                            </ul>

                            <Button
                                variant="solid"
                                color="green"
                                className="mt-5 mx-auto"
                                onClick={saveSingleCollection}
                            >
                                Save
                            </Button>
                        </div>
                    )}

                    {widgetType == 'newCollectionsSingle' && (
                        <div className="max-h-full overflow-y-auto flex flex-col">
                            <h3 className="mb-5 mt-5">Select Collection</h3>
                            <ul className="grid w-full gap-6 md:grid-cols-4">
                                {collections.map(
                                    (collection: any, index: any) => (
                                        <li key={index}>
                                            <input
                                                type="radio"
                                                id={collection?._id}
                                                name={`collection_${index}`}
                                                value={collection?._id}
                                                className="hidden peer"
                                                required
                                                onChange={handleRadioChangeNew}
                                                checked={
                                                    newSingleCollection ==
                                                    collection?._id
                                                }
                                            />
                                            <label
                                                htmlFor={collection?._id}
                                                className="inline-flex items-center justify-between w-full p-5 text-gray-500 bg-white border border-gray-200 rounded-lg cursor-pointer dark:hover:text-gray-300 dark:border-gray-700 dark:peer-checked:text-blue-500 peer-checked:border-blue-600 peer-checked:text-blue-600 hover:text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:bg-gray-800 dark:hover:bg-gray-700"
                                            >
                                                <div className="block">
                                                    <div className="w-full text-lg font-semibold">
                                                        {collection?.title?.en}
                                                    </div>
                                                    <img
                                                        className="mt-2 w-28 h-18 mx-auto"
                                                        src={`${baseUrl}${collection?.image}`}
                                                        alt="collection image"
                                                    />
                                                </div>
                                            </label>
                                        </li>
                                    )
                                )}
                            </ul>

                            <Button
                                variant="solid"
                                color="green"
                                className="mt-5 mx-auto"
                                onClick={saveNewSingleCollection}
                            >
                                Save
                            </Button>
                        </div>
                    )}

                    {widgetType == 'collectionsMultiple' && (
                        <div className="mt-5 max-h-full overflow-y-auto">
                            <div className="flow-root">
                                <h5>Select Collections</h5>
                                <div className="flow-root my-2">
                                    <h5>Label</h5>
                                    <div className="my-1 flex">
                                        <div className="flex flex-col">
                                            <label>English</label>
                                            <input
                                                className="w-[250px] p-3 border border-gray-300 rounded-lg"
                                                type="text"
                                                value={collectionsLabelEn}
                                                onChange={(e) =>
                                                    setCollectionsLabelEn(e.target.value)
                                                }
                                            />
                                        </div>
                                        <div className="flex flex-col ml-4">
                                            <label>Arabic</label>
                                            <input
                                                dir="rtl"
                                                className="w-[250px] p-3 border border-gray-300 rounded-lg"
                                                type="text"
                                                value={collectionsLabelAr}
                                                onChange={(e) =>
                                                    setCollectionsLabelAr(e.target.value)
                                                }
                                            />
                                        </div>
                                    </div>
                                </div>
                                <ul className="grid w-full gap-4 md:grid-cols-4 mb-4">
                                    {collections.map(
                                        (collection: any, index: any) => (
                                            <li key={collection.refid}>
                                                <input
                                                    type="checkbox"
                                                    id={collection.refid}
                                                    value={collection.refid}
                                                    className="hidden peer"
                                                    checked={checkedCollection.includes(
                                                        collection.refid
                                                    )}
                                                    onChange={() =>
                                                        collectionSelection(
                                                            collection.refid
                                                        )
                                                    }
                                                />
                                                <label
                                                    htmlFor={collection.refid}
                                                    className="inline-flex items-center justify-between w-full p-5 text-gray-500 bg-white border-2 border-gray-200 rounded-lg cursor-pointer dark:hover:text-gray-300 dark:border-gray-700 peer-checked:border-blue-600 hover:text-gray-600 dark:peer-checked:text-gray-300 peer-checked:text-gray-600 hover:bg-gray-50 dark:text-gray-400 dark:bg-gray-800 dark:hover:bg-gray-700"
                                                >
                                                    <div className="block">
                                                        <div className="w-full text-lg font-semibold">
                                                            {
                                                                collection.title
                                                                    .en
                                                            }
                                                        </div>
                                                        <img
                                                            className="mt-2 w-28 h-18 mx-auto"
                                                            src={`${baseUrl}${collection.image}`}
                                                            alt="collection image"
                                                        />
                                                    </div>
                                                </label>
                                            </li>
                                        )
                                    )}
                                </ul>
                            </div>
                            <div className="flex flex-col items-center">
                                <Button
                                    variant="solid"
                                    color="green"
                                    onClick={setMultipleCollections}
                                >
                                    Update
                                </Button>
                            </div>
                        </div>
                    )}

                    {widgetType == 'insuranceBanner' && (
                        <div className=" max-h-full overflow-y-auto">
                            <h5 className="">Update Banner</h5>
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <FormItem
                                        className="mb-[0px]"
                                        label="Title English"
                                    >
                                        <Input
                                            type="text"
                                            value={insuranceForm.title}
                                            onChange={(e) =>
                                                setInsuranceForm({
                                                    ...insuranceForm,
                                                    title: e.target.value,
                                                })
                                            }
                                        />
                                        <small className="text-red-500">
                                            {formErrors.title}
                                        </small>
                                    </FormItem>
                                    <FormItem
                                        className="mb-[0px] mt-2"
                                        label="Title Arabic"
                                    >
                                        <Input
                                            dir='rtl'
                                            type="text"
                                            value={insuranceForm.titleAr}
                                            onChange={(e) =>
                                                setInsuranceForm({
                                                    ...insuranceForm,
                                                    titleAr: e.target.value,
                                                })
                                            }
                                        />
                                        <small className="text-red-500">
                                            {formErrors.titleAr}
                                        </small>
                                    </FormItem>

                                    <FormItem
                                        className="mb-[0px] mt-2"
                                        label="Button Text English"
                                    >
                                        <Input
                                            type="text"
                                            value={insuranceForm.buttonText}
                                            onChange={(e) =>
                                                setInsuranceForm({
                                                    ...insuranceForm,
                                                    buttonText: e.target.value,
                                                })
                                            }
                                        />
                                        <small className="text-red-500">
                                            {formErrors.buttonText}
                                        </small>
                                    </FormItem>

                                    <FormItem
                                        className="mb-[0px] mt-2"
                                        label="Button Text Arabic"
                                    >
                                        <Input
                                            dir='rtl'
                                            type="text"
                                            value={insuranceForm.buttonTextAr}
                                            onChange={(e) =>
                                                setInsuranceForm({
                                                    ...insuranceForm,
                                                    buttonTextAr: e.target.value,
                                                })
                                            }
                                        />
                                        <small className="text-red-500">
                                            {formErrors.buttonTextAr}
                                        </small>
                                    </FormItem>
                                </div>

                                <div className="flex flex-col gap-4">
                                    <FormItem
                                        className="mb-[0px]"
                                        label="Description English"
                                    >
                                        <Input
                                            type="text"
                                            textArea
                                            value={insuranceForm.description}
                                            onChange={(e) =>
                                                setInsuranceForm({
                                                    ...insuranceForm,
                                                    description: e.target.value,
                                                })
                                            }
                                        />
                                        <small className="text-red-500">
                                            {formErrors.description}
                                        </small>
                                    </FormItem>
                                    <FormItem
                                        className="mb-[0px]"
                                        label="Description Arabic"
                                    >
                                        <Input
                                            dir="rtl"
                                            type="text"
                                            textArea
                                            value={insuranceForm.descriptionAr}
                                            onChange={(e) =>
                                                setInsuranceForm({
                                                    ...insuranceForm,
                                                    descriptionAr: e.target.value,
                                                })
                                            }
                                        />
                                        <small className="text-red-500">
                                            {formErrors.descriptionAr}
                                        </small>
                                    </FormItem>
                                </div>
                            </div>

                            <FormItem label="Link">
                                <Input
                                    type="text"
                                    value={insuranceForm.link}
                                    onChange={(e) =>
                                        setInsuranceForm({
                                            ...insuranceForm,
                                            link: e.target.value,
                                        })
                                    }
                                />
                                <small className="text-red-500">
                                    {formErrors.link}
                                </small>
                            </FormItem>

                            <FormItem label="Image (Preferred size :2732*752 px)">
                                <Input
                                    type="file"
                                    onChange={(e: any) =>
                                        handleInsuranceFile(e)
                                    }
                                    accept="image/*"
                                />
                                <small className="text-red-500">
                                    {formErrors.image}
                                </small>
                                {insuranceForm.image && (
                                    <img
                                        src={baseUrl + insuranceForm?.image}
                                        alt="insurance banner"
                                        className="w-28 h-20 object-contain"
                                    />
                                )}
                            </FormItem>

                            <Button
                                variant="solid"
                                color="green"
                                onClick={handleInsuranceForm}
                            >
                                Save
                            </Button>
                        </div>
                    )}

                    {widgetType == 'brandVideoBanner' && (
                        <div className="max-h-full flex flex-col">
                            <h5 className="">Update Banner</h5>
                            <div className="flex gap-2 mt-2 max-w-full overflow-x-auto min-h-fit">
                                {brandSliderFields.map((item: any, index: number) => (
                                    <button type='button'
                                        onClick={() => setCurrentBrandSlider(index)}
                                        className={`p-2 px-4 font-semibold duration-200 rounded border border-indigo-700 ${currentBrandSlider == index ? 'bg-indigo-700 text-white' : 'text-indigo-700 bg-white'}`}
                                    >
                                        {index + 1}
                                    </button>
                                ))}
                                <button type='button'
                                    onClick={() => {
                                        brandSliderAppend({
                                            description: "",
                                            descriptionAr: "",
                                            subImage: "",
                                            link: "",
                                            type: "image",
                                            image: "",
                                            mobileImage: "",
                                            video: ""
                                        })
                                        setCurrentBrandSlider(brandSliderFields.length)
                                    }}
                                    className={`p-2 px-4 font-semibold duration-200 rounded border border-indigo-700 bg-indigo-700 text-white`}
                                >
                                    +
                                </button>
                            </div>
                            {brandSliderFields.length > 1 && <div className="w-full flex mt-1 mb-2">
                                <Button onClick={deleteBrandSlider} variant="solid" color="red" className='ml-auto'>
                                    Delete slide {currentBrandSlider + 1}
                                </Button>
                            </div>}
                            <form className='max-h-full flex flex-col overflow-y-auto' onSubmit={handleSubmit(handleBrandSlider)}>

                                {brandSliderFields.map((item: any, index: number) => {
                                    if (currentBrandSlider != index) return null;
                                    const type = watch(`brandSliderFields.${index}.type`)
                                    return <div className="max-h-full overflow-auto">
                                        <div className="grid grid-cols-2 gap-4">
                                            <FormItem label="Description English">
                                                <Controller
                                                    name={`brandSliderFields.${index}.description`}
                                                    control={control}
                                                    defaultValue=""
                                                    render={({ field }) => (
                                                        <Input
                                                            type="text"
                                                            textArea
                                                            {...field}
                                                        />
                                                    )}
                                                />
                                            </FormItem>
                                            <FormItem label="Description Arabic">
                                                <Controller
                                                    name={`brandSliderFields.${index}.descriptionAr`}
                                                    control={control}
                                                    defaultValue=""
                                                    render={({ field }) => (
                                                        <Input
                                                            type="text"
                                                            textArea
                                                            dir="rtl"
                                                            {...field}
                                                        />
                                                    )}
                                                />
                                            </FormItem>

                                            {/* <FormItem label="Brand Logo (Preferred size : 256*120 px)">
                                                <Input
                                                    type="file"
                                                    accept="image/*"
                                                    onChange={(e: any) =>
                                                        brandLogoUpload(e, index)
                                                    }
                                                />
                                                {watch(`brandSliderFields.${index}.subImage`) && (
                                                    <img
                                                        src={
                                                            baseUrl +
                                                            watch(`brandSliderFields.${index}.subImage`)
                                                        }
                                                        alt="brand logo"
                                                        className="w-28 h-20 object-contain"
                                                    />
                                                )}
                                            </FormItem> */}
                                            <FormItem label="Link">
                                                <Controller
                                                    name={`brandSliderFields.${index}.link`}
                                                    control={control}
                                                    defaultValue=""
                                                    render={({ field }) => (
                                                        <Input
                                                            type="text"
                                                            {...field}
                                                        />
                                                    )}
                                                />
                                            </FormItem>
                                        </div>
                                        <div>
                                            <Radio
                                                value="video"
                                                className="mr-4"
                                                name="brandType"
                                                checked={type == 'video'}
                                                onClick={() => {
                                                    setBrandBannerType('video')
                                                    setValue(`brandSliderFields.${index}.type`, 'video')
                                                }}
                                            >
                                                Video
                                            </Radio>
                                            <Radio
                                                value="image"
                                                name="brandType"
                                                checked={type == 'image'}
                                                onClick={() => {
                                                    setBrandBannerType('image')
                                                    setValue(`brandSliderFields.${index}.type`, 'image')
                                                }}
                                            >
                                                Image
                                            </Radio>
                                        </div>
                                        {type == 'video' && (
                                            <div className="mt-5 flex flex-col items-center justify-center border border-dashed border-gray-400 p-4 mb-4">
                                                <label
                                                    htmlFor="upload-button"
                                                    className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-full cursor-pointer"
                                                >
                                                    Select Video
                                                </label>
                                                <p>Preferred size : 1920*900 px</p>
                                                <input
                                                    type="file"
                                                    id="upload-button"
                                                    className="hidden"
                                                    accept="video/mp4,video/x-m4v,video/*"
                                                    onChange={brandVideoChange}
                                                />
                                                {brandVideoPreview ? (
                                                    <div
                                                        key={brandVideoPreview}
                                                        className="mt-4"
                                                    >
                                                        <video
                                                            width="320"
                                                            height="240"
                                                            controls
                                                            className="rounded-lg shadow-lg"
                                                        >
                                                            <source
                                                                src={brandVideoPreview}
                                                                type="video/mp4"
                                                            />
                                                            Your browser does not support
                                                            the video tag.
                                                        </video>
                                                    </div>
                                                ) : (
                                                    <div className="mt-4">
                                                        <video
                                                            width="320"
                                                            height="240"
                                                            controls
                                                            className="rounded-lg shadow-lg"
                                                        >
                                                            <source
                                                                src={
                                                                    baseUrl +
                                                                    watch(`brandSliderFields.${index}.video`)
                                                                }
                                                                type="video/mp4"
                                                            />
                                                            Your browser does not support
                                                            the video tag.
                                                        </video>
                                                    </div>
                                                )}
                                                {brandVideoFile && (
                                                    <button
                                                        type="button"
                                                        className="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-full mt-4"
                                                        onClick={() => brandVideoUpload(index)}
                                                    >
                                                        Upload
                                                    </button>
                                                )}
                                            </div>
                                        )}
                                        {type == 'image' && (
                                            <div>
                                                <FormItem label="Image">
                                                    <Input
                                                        type="file"
                                                        accept="image/*"
                                                        onChange={(e: any) =>
                                                            BrandImageUpload(e, index)
                                                        }
                                                    />
                                                    {watch(`brandSliderFields.${index}.image`) && (
                                                        <img
                                                            src={
                                                                baseUrl +
                                                                watch(`brandSliderFields.${index}.image`)
                                                            }
                                                            alt="brand logo"
                                                            className="w-28 h-20 object-contain"
                                                        />
                                                    )}
                                                </FormItem>
                                            </div>
                                        )}
                                    </div>
                                })}
                                <Button
                                    variant="solid"
                                    color="green"
                                    className='ml-auto'
                                >
                                    Save
                                </Button>
                            </form>
                        </div>
                    )}

                    {widgetType == 'threeGridBanner' && (
                        <form
                            onSubmit={handleSubmit(handleSave)}
                            className="max-h-full overflow-y-auto mt-4"
                        >
                            <ul className="mt-6">
                                {gridFields.map((item, index) => {
                                    return (
                                        <li
                                            key={item.id}
                                            className="border-2 rounded-md border-gray-400 mt-2 py-6 px-3 relative"
                                        >
                                            <div className="grid grid-cols-2 gap-2">
                                                <FormItem label="Link">
                                                    <Controller
                                                        name={`gridFields.${index}.link`}
                                                        control={control}
                                                        defaultValue=""
                                                        rules={{
                                                            required:
                                                                'Field is required',
                                                        }}
                                                        render={({ field }) => (
                                                            <Input
                                                                type="text"
                                                                {...field}
                                                            />
                                                        )}
                                                    />
                                                    {errors.gridFields &&
                                                        (
                                                            errors.gridFields as any
                                                        )[index]?.link && (
                                                            <small className="text-red-600 py-3">
                                                                {
                                                                    (
                                                                        errors.gridFields as any
                                                                    )[index]
                                                                        ?.link
                                                                        .message as string
                                                                }
                                                            </small>
                                                        )}
                                                </FormItem>
                                            </div>

                                            <div>
                                                <FormItem label="Image(Preferred size : 796*796 px)">
                                                    <Controller
                                                        name={`gridFields.${index}.image`}
                                                        control={control}
                                                        defaultValue=""
                                                        render={({ field }) => (
                                                            <div>
                                                                <Input
                                                                    type="file"
                                                                    {...field}
                                                                    onChange={(
                                                                        e
                                                                    ) => {
                                                                        field.onChange(
                                                                            e
                                                                        )
                                                                        handleImages(
                                                                            e,
                                                                            index,
                                                                            'gridFields'
                                                                        )
                                                                    }}
                                                                />
                                                                {gridPreviews[
                                                                    index
                                                                ] && (
                                                                        <img
                                                                            className="mt-2 w-20 mx-auto object-cover object-top"
                                                                            src={
                                                                                baseUrl +
                                                                                gridPreviews[
                                                                                index
                                                                                ]
                                                                            }
                                                                            alt={`Image Preview ${index}`}
                                                                        />
                                                                    )}
                                                            </div>
                                                        )}
                                                    />
                                                </FormItem>
                                            </div>

                                            <div className="absolute right-0 top-0">
                                                <IoIosCloseCircleOutline
                                                    size={30}
                                                    color="red"
                                                    onClick={() => {
                                                        gridRemove(index)
                                                        toast.push(
                                                            <Notification
                                                                type="success"
                                                                title="Item Removed"
                                                            />,
                                                            {
                                                                placement:
                                                                    'top-center',
                                                            }
                                                        )
                                                    }}
                                                />
                                            </div>
                                        </li>
                                    )
                                })}
                            </ul>

                            <Button
                                variant="solid"
                                className="mt-4"
                                onClick={() => gridAppend({})}
                            >
                                Add More
                            </Button>

                            <Button
                                variant="solid"
                                className="mt-4 float-right"
                                type="submit"
                            >
                                Save
                            </Button>
                        </form>
                    )}

                    {widgetType == 'beforeAfter' && (
                        <div className="max-h-full overflow-y-auto">
                            <div className="flow-root">
                                <h5>Title</h5>
                                <div className="my-3 flex">
                                    <div className="flex flex-col">
                                        <label>English</label>
                                        <input
                                            className="w-[250px] p-3 border border-gray-300 rounded-lg"
                                            type="text"
                                            value={beforeAfterTitleEn}
                                            onChange={(e) =>
                                                setBeforeAfterTitleEn(e.target.value)
                                            }
                                        />
                                    </div>
                                    <div className="flex flex-col ml-4">
                                        <label>Arabic</label>
                                        <input
                                            dir="rtl"
                                            className="w-[250px] p-3 border border-gray-300 rounded-lg"
                                            type="text"
                                            value={beforeAfterTitleAr}
                                            onChange={(e) =>
                                                setBeforeAfterTitleAr(e.target.value)
                                            }
                                        />
                                    </div>
                                    <Button
                                        variant="solid"
                                        className="ms-4 mt-auto"
                                        onClick={updateBeforeAfterTitle}
                                    >
                                        Save
                                    </Button>
                                </div>
                            </div>
                            <div className="grid grid-cols-3 gap-2 my-4">
                                {beforeAfter.map((item: any, index: any) => {
                                    return (
                                        <div
                                            key={index}
                                            className="image-box w-full bg-white border border-gray-300 p-4 shadow-md relative"
                                        >
                                            <div className="image-container flex justify-between mb-4">
                                                <img
                                                    src={item.beforeImage}
                                                    alt="Left Image"
                                                    className="max-w-[48%] rounded-md"
                                                />
                                                <img
                                                    src={item.afterImage}
                                                    alt="Right Image"
                                                    className="max-w-[48%] rounded-md"
                                                />
                                            </div>
                                            <MdDeleteOutline
                                                size={20}
                                                color="black"
                                                className="absolute top-0 right-0 cursor-pointer"
                                                onClick={() => {
                                                    api.delete(
                                                        endpoints.beforeAfter +
                                                        '/' +
                                                        item._id
                                                    )
                                                        .then((res) => {
                                                            if (
                                                                res.status ==
                                                                200
                                                            ) {
                                                                toast.push(
                                                                    <Notification
                                                                        type="success"
                                                                        title="Image deleted successfully."
                                                                    />,
                                                                    {
                                                                        placement:
                                                                            'top-center',
                                                                    }
                                                                )
                                                                setFormSubmitted(
                                                                    true
                                                                )
                                                                setEditDialogOpen(
                                                                    false
                                                                )
                                                            }
                                                        })
                                                        .catch((err) => {
                                                            toast.push(
                                                                <Notification
                                                                    type="warning"
                                                                    title={
                                                                        err
                                                                            .response
                                                                            .data
                                                                            .message
                                                                    }
                                                                />,
                                                                {
                                                                    placement:
                                                                        'top-center',
                                                                }
                                                            )
                                                        })
                                                }}
                                            />
                                        </div>
                                    )
                                })}
                            </div>
                            {!showAvatars && beforeAfter.length < 2 && (
                                <Button
                                    variant="solid"
                                    className="mt-4"
                                    onClick={() => {
                                        setShowAvatars(true)
                                    }}
                                >
                                    Add New
                                </Button>
                            )}

                            {showAvatars && (
                                <>
                                    <input
                                        type="file"
                                        ref={fileInputBeforeRef}
                                        style={{ display: 'none' }}
                                        onChange={(e) =>
                                            handleFileChange(e, 'before')
                                        }
                                    />
                                    <input
                                        type="file"
                                        ref={fileInputAfterRef}
                                        style={{ display: 'none' }}
                                        onChange={(e) =>
                                            handleFileChange(e, 'after')
                                        }
                                    />
                                    <div className="flex gap-6 my-3">
                                        <div
                                            onClick={() =>
                                                handleAvatarClick('before')
                                            }
                                        >
                                            <p>
                                                After File (Preferred size :
                                                1224*960 px)
                                            </p>
                                            <Avatar
                                                size={80}
                                                src={
                                                    beforeFile
                                                        ? ((baseUrl +
                                                            beforeFile) as string)
                                                        : ''
                                                }
                                                icon={<HiOutlinePlus />}
                                            />
                                        </div>
                                        <div
                                            onClick={() =>
                                                handleAvatarClick('after')
                                            }
                                        >
                                            <p>
                                                Before File(Preferred size :
                                                612*480 px)
                                            </p>
                                            <Avatar
                                                size={80}
                                                src={
                                                    afterFile
                                                        ? ((baseUrl +
                                                            afterFile) as string)
                                                        : ''
                                                }
                                                icon={<HiOutlinePlus />}
                                            />
                                        </div>
                                    </div>
                                    <Button
                                        variant="solid"
                                        color="green"
                                        onClick={handleSaveClick}
                                    >
                                        Save
                                    </Button>
                                </>
                            )}
                        </div>
                    )}

                    {widgetType == 'brandsCollection' && (
                        <div className="max-h-full overflow-y-auto">
                            <div className="flow-root">
                                <h5>Title</h5>
                                <div className="my-3 flex">
                                    <div className="flex flex-col">
                                        <label>English</label>
                                        <input
                                            className="w-[250px] p-3 border border-gray-300 rounded-lg"
                                            type="text"
                                            value={brandCollectionTitleEn}
                                            onChange={(e) =>
                                                setBrandCollectionTitleEn(e.target.value)
                                            }
                                        />
                                    </div>
                                    <div className="flex flex-col ml-4">
                                        <label>Arabic</label>
                                        <input
                                            dir="rtl"
                                            className="w-[250px] p-3 border border-gray-300 rounded-lg"
                                            type="text"
                                            value={brandCollectionTitleAr}
                                            onChange={(e) =>
                                                setBrandCollectionTitleAr(e.target.value)
                                            }
                                        />
                                    </div>
                                </div>
                            </div>

                            <form
                                onSubmit={handleSubmit(onSubmit)}
                                className=""
                            >
                                <ul className="mt-6">
                                    {fields.map((item, index) => {
                                        return (
                                            <li
                                                key={item.id}
                                                className="border-2 rounded-md border-gray-400 mt-2 py-6 px-3 relative"
                                            >
                                                <div className="grid grid-cols-3 gap-2">
                                                    <FormItem label="Title English">
                                                        <Controller
                                                            name={`fields.${index}.titleEn`}
                                                            control={control}
                                                            defaultValue=""
                                                            rules={{
                                                                required:
                                                                    'Field is required',
                                                            }}
                                                            render={({ field }) => (
                                                                <Input
                                                                    type="text"
                                                                    {...field}
                                                                />
                                                            )}
                                                        />
                                                        {errors.fields &&
                                                            (errors.fields as any)[
                                                                index
                                                            ]?.titleEn && (
                                                                <small className="text-red-600 py-3">
                                                                    {
                                                                        (
                                                                            errors.fields as any
                                                                        )[index]
                                                                            ?.titleEn
                                                                            .message as string
                                                                    }
                                                                </small>
                                                            )}
                                                    </FormItem>
                                                    <FormItem label="Title Arabic">
                                                        <Controller
                                                            name={`fields.${index}.titleAr`}
                                                            control={control}
                                                            defaultValue=""
                                                            rules={{
                                                                required:
                                                                    'Field is required',
                                                            }}
                                                            render={({ field }) => (
                                                                <Input
                                                                    dir="rtl"
                                                                    type="text"
                                                                    {...field}
                                                                />
                                                            )}
                                                        />
                                                        {errors.fields &&
                                                            (errors.fields as any)[
                                                                index
                                                            ]?.titleAr && (
                                                                <small className="text-red-600 py-3">
                                                                    {
                                                                        (
                                                                            errors.fields as any
                                                                        )[index]
                                                                            ?.titleAr
                                                                            .message as string
                                                                    }
                                                                </small>
                                                            )}
                                                    </FormItem>

                                                    <FormItem label="Button Text English">
                                                        <Controller
                                                            name={`fields.${index}.buttonTextEn`}
                                                            control={control}
                                                            defaultValue=""
                                                            rules={{
                                                                required:
                                                                    'Field is required',
                                                            }}
                                                            render={({ field }) => (
                                                                <Input
                                                                    type="text"
                                                                    {...field}
                                                                />
                                                            )}
                                                        />
                                                        {errors.fields &&
                                                            (errors.fields as any)[
                                                                index
                                                            ]?.buttonTextEn && (
                                                                <small className="text-red-600 py-3">
                                                                    {
                                                                        (
                                                                            errors.fields as any
                                                                        )[index]
                                                                            ?.buttonTextEn
                                                                            .message as string
                                                                    }
                                                                </small>
                                                            )}
                                                    </FormItem>
                                                    <FormItem label="Button Text Arabic">
                                                        <Controller
                                                            name={`fields.${index}.buttonTextAr`}
                                                            control={control}
                                                            defaultValue=""
                                                            rules={{
                                                                required:
                                                                    'Field is required',
                                                            }}
                                                            render={({ field }) => (
                                                                <Input
                                                                    dir="rtl"
                                                                    type="text"
                                                                    {...field}
                                                                />
                                                            )}
                                                        />
                                                        {errors.fields &&
                                                            (errors.fields as any)[
                                                                index
                                                            ]?.buttonTextAr && (
                                                                <small className="text-red-600 py-3">
                                                                    {
                                                                        (
                                                                            errors.fields as any
                                                                        )[index]
                                                                            ?.buttonTextAr
                                                                            .message as string
                                                                    }
                                                                </small>
                                                            )}
                                                    </FormItem>

                                                    <FormItem label="Link">
                                                        <Controller
                                                            name={`fields.${index}.link`}
                                                            control={control}
                                                            defaultValue=""
                                                            rules={{
                                                                required:
                                                                    'Field is required',
                                                            }}
                                                            render={({ field }) => (
                                                                <Input
                                                                    type="text"
                                                                    {...field}
                                                                />
                                                            )}
                                                        />
                                                        {errors.fields &&
                                                            (errors.fields as any)[
                                                                index
                                                            ]?.link && (
                                                                <small className="text-red-600 py-3">
                                                                    {
                                                                        (
                                                                            errors.fields as any
                                                                        )[index]
                                                                            ?.link
                                                                            .message as string
                                                                    }
                                                                </small>
                                                            )}
                                                    </FormItem>
                                                </div>

                                                <div>
                                                    <FormItem label="Image (Preferred size : 796*1020 px)">
                                                        <Controller
                                                            name={`fields.${index}.image`}
                                                            control={control}
                                                            defaultValue=""
                                                            render={({ field }) => (
                                                                <div>
                                                                    <Input
                                                                        type="file"
                                                                        {...field}
                                                                        onChange={(
                                                                            e
                                                                        ) => {
                                                                            field.onChange(
                                                                                e
                                                                            )
                                                                            handleImages(
                                                                                e,
                                                                                index,
                                                                                'fields'
                                                                            )
                                                                        }}
                                                                    />
                                                                    {imagePreviews[
                                                                        index
                                                                    ] && (
                                                                            <img
                                                                                className="mt-2 w-20 mx-auto object-cover object-top"
                                                                                src={
                                                                                    baseUrl +
                                                                                    imagePreviews[
                                                                                    index
                                                                                    ]
                                                                                }
                                                                                alt={`Image Preview ${index}`}
                                                                            />
                                                                        )}
                                                                </div>
                                                            )}
                                                        />
                                                    </FormItem>
                                                </div>

                                                <div className="absolute right-0 top-0">
                                                    <IoIosCloseCircleOutline
                                                        size={30}
                                                        color="red"
                                                        onClick={() => {
                                                            remove(index)
                                                        }}
                                                    />
                                                </div>
                                            </li>
                                        )
                                    })}
                                </ul>

                                <Button
                                    variant="solid"
                                    className="mt-4"
                                    onClick={(event) => {
                                        event.preventDefault()
                                        append({})
                                    }}
                                >
                                    Add More
                                </Button>

                                <Button
                                    variant="solid"
                                    className="mt-4 float-right"
                                    type="submit"
                                >
                                    Save
                                </Button>
                            </form>
                        </div>
                    )}

                    {widgetType == 'virtualTryBanner' && (
                        <div className=" max-h-full overflow-y-auto">
                            <h5 className="">Update Banner</h5>
                            <div className="grid grid-cols-2 gap-4">
                                <div>
                                    <FormItem
                                        className="mb-[0px]"
                                        label="Title English"
                                    >
                                        <Input
                                            type="text"
                                            value={virtualtryForm.title}
                                            onChange={(e) =>
                                                setVirtualtryForm({
                                                    ...virtualtryForm,
                                                    title: e.target.value,
                                                })
                                            }
                                        />
                                        <small className="text-red-500">
                                            {formErrors.title}
                                        </small>
                                    </FormItem>
                                    <FormItem
                                        className="mb-[0px] mt-2"
                                        label="Title Arabic"
                                    >
                                        <Input
                                            dir="rtl"
                                            type="text"
                                            value={virtualtryForm.titleAr}
                                            onChange={(e) =>
                                                setVirtualtryForm({
                                                    ...virtualtryForm,
                                                    titleAr: e.target.value,
                                                })
                                            }
                                        />
                                        <small className="text-red-500">
                                            {formErrors.titleAr}
                                        </small>
                                    </FormItem>

                                    <FormItem
                                        className="mb-[0px] mt-2"
                                        label="Button Text English"
                                    >
                                        <Input
                                            type="text"
                                            value={virtualtryForm.buttonText}
                                            onChange={(e) =>
                                                setVirtualtryForm({
                                                    ...virtualtryForm,
                                                    buttonText: e.target.value,
                                                })
                                            }
                                        />
                                        <small className="text-red-500">
                                            {formErrors.buttonText}
                                        </small>
                                    </FormItem>
                                    <FormItem
                                        className="mb-[0px] mt-2"
                                        label="Button Text Arabic"
                                    >
                                        <Input
                                            dir="rtl"
                                            type="text"
                                            value={virtualtryForm.buttonTextAr}
                                            onChange={(e) =>
                                                setVirtualtryForm({
                                                    ...virtualtryForm,
                                                    buttonTextAr: e.target.value,
                                                })
                                            }
                                        />
                                        <small className="text-red-500">
                                            {formErrors.buttonTextAr}
                                        </small>
                                    </FormItem>
                                </div>

                                <div className="flex flex-col gap-4">
                                    <FormItem
                                        className="mb-[0px]"
                                        label="Description English"
                                    >
                                        <Input
                                            type="text"
                                            textArea
                                            value={virtualtryForm.description}
                                            onChange={(e) =>
                                                setVirtualtryForm({
                                                    ...virtualtryForm,
                                                    description: e.target.value,
                                                })
                                            }
                                        />
                                        <small className="text-red-500">
                                            {formErrors.description}
                                        </small>
                                    </FormItem>
                                    <FormItem
                                        className="mb-[0px]"
                                        label="Description Arabic"
                                    >
                                        <Input
                                            dir="rtl"
                                            type="text"
                                            textArea
                                            value={virtualtryForm.descriptionAr}
                                            onChange={(e) =>
                                                setVirtualtryForm({
                                                    ...virtualtryForm,
                                                    descriptionAr: e.target.value,
                                                })
                                            }
                                        />
                                        <small className="text-red-500">
                                            {formErrors.descriptionAr}
                                        </small>
                                    </FormItem>
                                </div>
                            </div>

                            <FormItem label="Link">
                                <Input
                                    type="text"
                                    value={virtualtryForm.link}
                                    onChange={(e) =>
                                        setVirtualtryForm({
                                            ...virtualtryForm,
                                            link: e.target.value,
                                        })
                                    }
                                />
                                <small className="text-red-500">
                                    {formErrors.link}
                                </small>
                            </FormItem>

                            <div className="grid grid-cols-2 gap-4">
                                <FormItem label="Image(Preferred size : 1596*1040 px)">
                                    <Input
                                        type="file"
                                        onChange={(e: any) =>
                                            handleVirtualTryFile(e, 'image')
                                        }
                                        accept="image/*"
                                    />
                                    <small className="text-red-500">
                                        {formErrors.image}
                                    </small>
                                    {virtualtryForm.image && (
                                        <img
                                            src={
                                                baseUrl + virtualtryForm?.image
                                            }
                                            alt="insurance banner"
                                            className="w-28 h-20 object-contain"
                                        />
                                    )}
                                </FormItem>

                                <FormItem label="Second Image(Preferred size : 1800*1200 px)">
                                    <Input
                                        type="file"
                                        onChange={(e: any) =>
                                            handleVirtualTryFile(e, 'subImage')
                                        }
                                        accept="image/*"
                                    />
                                    <small className="text-red-500">
                                        {formErrors.image}
                                    </small>
                                    {virtualtryForm.subImage && (
                                        <img
                                            src={
                                                baseUrl +
                                                virtualtryForm?.subImage
                                            }
                                            alt="insurance banner"
                                            className="w-28 h-20 object-contain"
                                        />
                                    )}
                                </FormItem>
                            </div>

                            <Button
                                variant="solid"
                                color="green"
                                onClick={handleVirtualTryForm}
                            >
                                Save
                            </Button>
                        </div>
                    )}
                </Dialog>
            </div>
        </div>
    )
}

export default DashboardDnd

function CategoryDrag(props: any) {
    const {
        attributes,
        listeners,
        setNodeRef,
        transform,
        transition,
        isDragging
    } = useSortable({ id: props.id });
    const style = {
        transform: CSS.Transform.toString(transform),
        transition
    };

    return (
        <div
            style={style}
            className={isDragging ? "opacity-50" : ""}
            ref={setNodeRef} {...attributes} {...listeners}
        >
            {props.children}
        </div>

    )
}


const Item = (props: any) => {
    const { category, handleCategorySelection }: any = props
    return (
        <div className='h-full'>
            <input
                onChange={
                    handleCategorySelection
                }
                type="checkbox"
                id={
                    category.refid
                }
                value={
                    category.refid
                }
                className="hidden peer"
                checked={
                    category.inHome
                }
            />
            <label
                htmlFor={
                    category?.refid
                }
                className="inline-flex h-full items-center justify-between w-full p-2 text-gray-500 bg-white border-2 border-gray-200 rounded-lg cursor-pointer dark:hover:text-gray-300 dark:border-gray-700 peer-checked:border-blue-600 hover:text-gray-600 dark:peer-checked:text-gray-300 peer-checked:text-gray-600 hover:bg-gray-50 dark:text-gray-400 dark:bg-gray-800 dark:hover:bg-gray-700"
            >
                <div className="flex flex-col justify-center mx-auto">
                    <div className="w-full text-lg font-semibold">
                        {
                            category?.name?.en
                        }
                    </div>
                    <img
                        className="mt-2 w-20 mx-auto"
                        src={`${baseUrl}${category?.image}`}
                        alt="category image"
                    />
                </div>
            </label>
        </div>
    )
};
