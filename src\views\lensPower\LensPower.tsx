/*eslint-disable */
import { Button, FormItem, Input, toast } from '@/components/ui'
import Tabs from '@/components/ui/Tabs'
import { useEffect, useState } from 'react'
import List from './List'
import Breadcrumb from '../modals/BreadCrumb'
const { TabNav, TabList, TabContent } = Tabs

const LensPower = () => {
  const [activeTab, setActiveTab] = useState<string>(() => {
    return localStorage.getItem('lensPowerActiveTab') || 'Sph'
  })

  const handleTabChange = (value: string) => {
    setActiveTab(value)
    localStorage.setItem('lensPowerActiveTab', value)
  }

  const breadcrumbItems = [
    { title: 'Lens power', url: '' },
  ];


  useEffect(() => {

  }, []);

  return (
    <div>
      <h3 className='mb-4'> Lens Power</h3>
      <Breadcrumb items={breadcrumbItems} />

      <Tabs value={activeTab}  onChange={handleTabChange}>
        <TabList>
          <TabNav value="Sph">Sph</TabNav>
          <TabNav value="Cyl">Cyl</TabNav>
          <TabNav value="Axis">Axis</TabNav>
          <TabNav value="Add">Add</TabNav>
        </TabList>
        <div className="p-4">
          <TabContent value="Sph">
            <List type='Sph' />
          </TabContent>

          <TabContent value="Cyl">
            <List type='Cyl' />
          </TabContent>

          <TabContent value="Axis">
            <List type='Axis' />
          </TabContent>

          <TabContent value="Add">
            <List type='Add' />
          </TabContent>
        </div>
      </Tabs>
    </div>
  )
}

export default LensPower