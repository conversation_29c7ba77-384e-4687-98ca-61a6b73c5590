/* eslint-disable */
import BannerSelection from './BannerSelection'; // Import the BannerSelection component
import CategorySelection from './CategorySelection'; // Import the CategorySelection component
import BrandSelection from './BrandSelection'; // Import the BrandSelection component
import SelectionItem from './SelectionItem'; // Import the SelectionItem component
import React, { useEffect, useState } from 'react';
import { Button, Dialog, Select, toast } from '@/components/ui';
import api from '@/services/api.interceptor';
import endpoints from '@/endpoints';
import Notification from '@/components/ui/Notification';
import { DragDropContext, Draggable, Droppable } from 'react-beautiful-dnd';
import { FaPen } from 'react-icons/fa';

const Rearrange = () => {
    // Your existing state variables
    const [items, setItems] = React.useState([]);
    const [isUpdate, setIsUpdate] = React.useState(false);
    const [refid, setRefid] = React.useState('');
    const [formSubmitted, setFormSubmitted] = React.useState(false);
    const [dialogIsOpen, setIsOpen] = useState(false)
    const [editDialogOpen, setEditDialogOpen] = useState(false)
    const [collectionOptions, setCollectionOptions] = useState<any>([])
    const [collectionType, setCollectionType] = useState('single');
    const [selectedCollections, setSelectedCollections] = useState<any>([]);
    const [banners, setBanners] = useState<any>([])
    const [selectedBanner, setSelectedBanner] = useState<any>(null);
    const [widgetId, setWidgetId] = useState(null);
    const [widgetType, setWidgetType] = useState(null);
    const [categories, setCategories] = useState<any>([])
    const [brands, setBrands] = useState<any>([])
    const [collections, setCollections] = useState<any>([])
    const [singleCollection, setSingleCollection] = useState<any>('')

    // Your existing functions
    const handleRadioChange = (event: any) => {
        setSingleCollection(event.target.value);
    }

    const saveSingleCollection = () => {
        api.put(endpoints.setSingleCollection, { collection: singleCollection, widgetId: widgetId }).then((res) => {
            if (res.status == 200) {
                toast.push(
                    <Notification type="success" title="Home Page updated" />, {
                    placement: 'top-center'
                })
                setFormSubmitted(true)
                setEditDialogOpen(false)
            }
        }).catch((err) => {
            toast.push(
                <Notification type="warning" title={err.response.data.message} />, {
                placement: 'top-center'
            })
        })
    }

    const openDialog = () => {
        setIsOpen(true)
    }

    const openEditDialog = (id: any, type: any) => {
        setWidgetId(id)
        setWidgetType(type)
        setEditDialogOpen(true)
    }

    const handleCollectionSelection = (selectedOptions: any) => {
        setSelectedCollections(selectedOptions);
    };

    const handleCategorySelection = (event: any) => {
        console.log(" clicked ::", event.target.value, event.target.checked)
        const categoryId = event.target.value
        const isChecked = event.target.checked

        api.put(endpoints.updateInHome + categoryId, { inHome: isChecked }).then((res) => {
            if (res.status == 200) {
                toast.push(
                    <Notification type="success" title={`${res.data.result.name.en} is ${res.data.result.inHome ? 'added' : 'removed'} in home`} />,
                    { placement: 'top-center' }
                )
                setFormSubmitted(true)
            }
        })
    }

    const handleBrandSelection = (event: any) => {
        const brandId = event.target.value
        const isChecked = event.target.checked

        api.put(endpoints.brandInHome + brandId, { inHome: isChecked }).then((res) => {
            if (res.status == 200) {
                toast.push(
                    <Notification type="success" title={`${res.data.result.name.en} is ${res.data.result.inHome ? 'added' : 'removed'} in home`} />,
                    { placement: 'top-center' }
                )
                setFormSubmitted(true)
            }
        })
    }

    const onDialogClose = (e: any) => {
        let keywords: any = [];
        if (collectionType == 'single-collection') {
            keywords.push({
                title: selectedCollections.label,
                keyword: selectedCollections.slug
            })
        }
        else if (collectionType == 'multiple-collection') {
            keywords = selectedCollections.map((item: any) => {
                return {
                    title: item.label,
                    keyword: item.slug
                }
            })
        }

        const data = {
            type: collectionType,
            keyword: keywords,
        }
        api.put(endpoints.addWidget + refid, data).then((res) => {
            if (res.status == 200) {
                setIsOpen(false)
                toast.push(
                    <Notification
                        type="success"
                        title={res.data.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                );
            }
        })
    }

    const onEditDialogClose = (e: MouseEvent) => {
        api.post(endpoints.setBanner, { banner: selectedBanner, widgetId: widgetId }).then((res) => {
            console.log('res', res)
            if (res.status == 200) {
                setEditDialogOpen(false)
                toast.push(
                    <Notification type="success" title="Banner updated" />,
                    { placement: 'top-center' }
                )
                setFormSubmitted(true)
            }
        }).catch((err) => {
            console.log('err', err)
        })
    }

    const handleCollectionTypeChange = (event: any) => {
        const selectedType = event.target.value;
        setCollectionType(selectedType);
    };

    function saveOrder() {
        if (isUpdate) {
            api.put(endpoints.updateHomeOrder + refid, items).then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification
                            type="success"
                            title={res.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    );
                    setFormSubmitted(true);
                }
            })
        }
        else {
            api.post(endpoints.saveHomeOrder, { items: items }).then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification
                            type="success"
                            title={res.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    );
                }
            })
        }
    }

    function getCollections() {
        api.get(endpoints.collections).then((res) => {
            if (res?.status == 200) {
                const collections = res?.data?.result || []
                setCollections(collections)
                const newCollectionOptions = collections.map((collection: any) => ({
                    value: collection._id,
                    label: collection.title.en,
                    slug: collection.slug,
                }));
                setCollectionOptions(newCollectionOptions);
            }
        })
    }

    function getBanners() {
        api.get(endpoints.getBanners).then((res) => {
            if (res?.status == 200) {
                const banners = res?.data?.result || []
                setBanners(banners)
            }
        })
    }

    function getCategories() {
        api.get(endpoints.parentCategories).then((res) => {
            if (res?.status == 200) {
                const categories = res?.data?.result || []
                setCategories(categories)
            }
        }).catch((error) => {
            console.error('Error fetching data: ', error)
        })
    }

    function getBrands() {
        api.get(endpoints.brands).then((res) => {
            if (res?.status == 200) {
                const brands = res?.data?.result || []
                setBrands(brands)
            }
        }).catch((error) => {
            console.error('Error fetching data: ', error)
        })
    }

    useEffect(() => {
        api.get(endpoints.homeOrder).then((res) => {
            if (res.status == 200) {
                setItems(res?.data?.result[0].items);
                setIsUpdate(true);
                setRefid(res?.data?.result[0].refid);
            }
        })

        getCollections();
        getBanners();
        getCategories();
        getBrands();

    }, [formSubmitted])

    const handleDragEnd = (result: any) => {
        if (!result.destination) return;

        const newItems = Array.from(items);
        const [reorderedItem] = newItems.splice(result.source.index, 1);
        newItems.splice(result.destination.index, 0, reorderedItem);

        setItems(newItems);

    };

    return (
        <div className='flex'>
            {/* Existing Drag and Drop section */}
            {/* ... */}
            <DragDropContext onDragEnd={handleDragEnd}>
                <Droppable droppableId="droppable">
                    {(provided, snapshot) => (
                        <div
                            {...provided.droppableProps}
                            ref={provided.innerRef}
                            style={{
                                background: snapshot.isDraggingOver ? 'lightblue' : 'lightgrey',
                                padding: 10,
                                width: 600,
                                minHeight: 200,
                            }}
                        >
                            {items.map((item: any, index: any) => (
                                <Draggable key={item._id} draggableId={item._id} index={index}>
                                    {(provided, snapshot) => (
                                        <div
                                            ref={provided.innerRef}
                                            {...provided.draggableProps}
                                            {...provided.dragHandleProps}
                                            style={{
                                                position: 'relative',
                                                userSelect: 'none',
                                                padding: 16,
                                                margin: '0 0 8px 0',
                                                minHeight: '50px',
                                                backgroundColor: snapshot.isDragging ? '#263B4A' : '#456C86',
                                                color: 'white',
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'space-between',
                                                ...provided.draggableProps.style,
                                            }}
                                        >
                                            {item.type == 'mainBanner' || item.type == 'videoBanner' &&
                                                <img src={`/yateem_parts/mainBanner.png`} alt={`Item ${index + 1}`} />
                                            }

                                            {item.type == 'categories' &&
                                                <img src={`/yateem_parts/categories.png`} alt={`Item ${index + 1}`} />
                                            }

                                            {item.type == 'shopByBrands' &&
                                                <img src={`/yateem_parts/shopByBrands.png`} alt={`Item ${index + 1}`} />
                                            }

                                            {item.type == 'collectionsSingle' &&
                                                <img src="/yateem_parts/collectionsSingle.png" alt={`Item ${index + 1}`} />
                                            }

                                            {item.type == 'imageMap' &&
                                                <img src={`/yateem_parts/imageMap.png`} alt={`Item ${index + 1}`} />
                                            }

                                            {item.type == 'collectionsMultiple' &&
                                                <img src="/yateem_parts/collectionsMultiple.png" alt={`Item ${index + 1}`} />
                                            }

                                            {item.type == 'insuranceBanner' &&
                                                <img src="/yateem_parts/insuranceBanner.png" alt={`Item ${index + 1}`} />
                                            }

                                            {item.type == 'brandVideoBanner' &&
                                                <img src={`/yateem_parts/brandVideoBanner.png`} alt={`Item ${index + 1}`} />
                                            }

                                            {item.type == 'threeGridBanner' &&
                                                <img src={`/yateem_parts/threeGridBanner.png`} alt={`Item ${index + 1}`} />
                                            }

                                            {item.type == 'frameShape' &&
                                                <img src={`/yateem_parts/frameShape.png`} alt={`Item ${index + 1}`} />
                                            }

                                            {item.type == 'beforeAfter' &&
                                                <img src={`/yateem_parts/beforeAfter.png`} alt={`Item ${index + 1}`} />
                                            }

                                            {item.type == 'brands' &&
                                                <img src={`/yateem_parts/brands.png`} alt={`Item ${index + 1}`} />
                                            }

                                            {item.type == 'brandsCollection' &&
                                                <img src={`/yateem_parts/brandsCollection.png`} alt={`Item ${index + 1}`} />
                                            }

                                            {item.type == 'virtualTryBanner' &&
                                                <img src={`/yateem_parts/virtualTryBanner.png`} alt={`Item ${index + 1}`} />
                                            }

                                            <Button
                                                className='absolute right-0 top-0'
                                                variant="solid"
                                                size="sm"
                                                icon={<FaPen />}
                                                onClick={() => openEditDialog(item._id, item.type)}
                                            />

                                        </div>
                                    )}
                                </Draggable>
                            ))}
                            {provided.placeholder}
                        </div>
                    )}
                </Droppable>
            </DragDropContext>

            {/* Save Order Button */}
            <Button className='ml-14 mt-3' variant='solid' onClick={saveOrder}>
                Apply Changes
            </Button>

            {/* Add Widget Button */}
            <Button className='ml-14 mt-3' variant='solid' onClick={() => openDialog()}>
                Add Widget
            </Button>

            <Dialog
                width={700}
                isOpen={dialogIsOpen}
                onClose={() => {
                    setIsOpen(false);
                }}
            >
                <div className="mt-5">
                    <div className="flow-root">
                        <h5>Add New Widget</h5>
                        <h6 className='text-gray-500 mt-4'>Banner Widget</h6>
                        <div className="mt-2 flex space-x-4">
                            <div className='text-gray-500 font-semibold text-sm border  border-gray-400 p-2'>Full Banner</div>
                            <div className='text-gray-500 font-semibold text-sm border rounded-md border-gray-400 p-2'>3 Banner Layout</div>
                        </div>

                        <h6 className='text-gray-500 mt-4'>Collection  Widget</h6>

                        <div className='grid grid-cols-2 gap-3'>
                            <div className="flex items-center ps-4 border border-gray-200 rounded dark:border-gray-700">
                                <input
                                    checked={collectionType == 'single-collection'}
                                    id="single-collection"
                                    type="radio"
                                    value="single-collection"
                                    name="bordered-radio"
                                    onChange={handleCollectionTypeChange}
                                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600" />
                                <label
                                    htmlFor="single-collection"
                                    className="w-full py-4 ms-2 text-sm font-medium text-gray-900 dark:text-gray-300"
                                >
                                    Single Collection
                                </label>
                            </div>
                            <div className="flex items-center ps-4 border border-gray-200 rounded dark:border-gray-700">
                                <input
                                    checked={collectionType == 'multiple-collection'}
                                    id="multiple-collection"
                                    type="radio"
                                    value="multiple-collection"
                                    name="bordered-radio"
                                    onChange={handleCollectionTypeChange}
                                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600" />
                                <label
                                    htmlFor="multiple-collection"
                                    className="w-full py-4 ms-2 text-sm font-medium text-gray-900 dark:text-gray-300"
                                >
                                    Multiple Collection
                                </label>
                            </div>
                        </div>

                        <Select
                            className="mt-2"
                            options={collectionOptions}
                            isMulti={collectionType == 'multiple-collection'}
                            value={selectedCollections}
                            onChange={handleCollectionSelection}
                        />

                        <Button
                            variant='solid'
                            className='mt-4 float-right'
                            onClick={onDialogClose}
                        >
                            Save
                        </Button>
                    </div>
                </div>
            </Dialog>

            {/* Dialog for Adding Widget */}
            <Dialog width={700} isOpen={editDialogOpen} onClose={() => setEditDialogOpen(false)}>
                {/* Existing Dialog Content */}
                {/* ... */}

                {/* BannerSelection Component */}
                {widgetType === 'mainBanner' || widgetType === 'insuranceBanner' || widgetType === 'brandVideoBanner' && (
                    <BannerSelection
                        banners={banners}
                        selectedBanner={selectedBanner}
                        onBannerChange={(bannerId) => setSelectedBanner(bannerId)}
                        onSave={onEditDialogClose}
                    />
                )}

                {/* CategorySelection Component */}
                {widgetType === 'categories' && (
                    <CategorySelection categories={categories} onCategoryChange={handleCategorySelection} />
                )}

                {/* BrandSelection Component */}
                {widgetType === 'shopByBrands' && (
                    <BrandSelection brands={brands} onBrandChange={handleBrandSelection} />
                )}

                {/* CollectionsSingle Component */}
                {widgetType === 'collectionsSingle' && (
                    <div className="max-h-full overflow-y-auto flex flex-col items-center">
                        {/* Existing CollectionsSingle content */}
                        {/* ... */}

                        {/* Save Single Collection Button */}
                        <Button variant='solid' color='green' className="mt-5" onClick={saveSingleCollection}>
                            Save
                        </Button>
                    </div>
                )}

                {/* Existing Dialog Save Button */}
                {/* ... */}
            </Dialog>
        </div>
    );
};

export default Rearrange;
