
interface SelectionItemProps {
    id: string;
    label: string;
    image: string;
    onChange: () => void;
    checked: boolean;
}

const SelectionItem = ({ id, label, image, onChange, checked }: SelectionItemProps) => (
    <li key={id}>
        <input
            required
            type="radio"
            id={id}
            value={id}
            className="hidden peer"
            checked={checked}
            onChange={onChange}
        />
        <label
            htmlFor={id}
            className="inline-flex items-center justify-between w-full p-5 text-gray-500 bg-white border border-gray-200 rounded-lg cursor-pointer dark:hover:text-gray-300 dark:border-gray-700 dark:peer-checked:text-blue-500 peer-checked:border-blue-600 peer-checked:text-blue-600 hover:text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:bg-gray-800 dark:hover:bg-gray-700"
        >
            <div className="block">
                <div className="w-full text-lg font-semibold">{label}</div>
                <img className="mt-2 w-28 h-18 mx-auto" src={image} alt={`${label} image`} />
            </div>
        </label>
    </li>
);

export default SelectionItem;