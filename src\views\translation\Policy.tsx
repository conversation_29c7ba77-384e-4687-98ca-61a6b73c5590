import { FormItem, Input } from '@/components/ui'
import { Controller } from 'react-hook-form'

const policySection: { label: string, value: string, db: string }[] = [
    {
        label: "Privacy Policy",
        value: "PrivacyPolicy",
        db: "privacyPolicy",
    },
    {
        label: "Terms And Condition",
        value: "TermsAndCondition",
        db: "termsAndCondition",
    },
    {
        label: "Shipping Policy",
        value: "ShippingPolicy",
        db: "shippingPolicy",
    },
    {
        label: "Return Policy",
        value: "ReturnPolicy",
        db: "returnPolicy",
    },
    {
        label: "Cookie Policy",
        value: "CookiePolicy",
        db: "cookiePolicy",
    },
    {
        label: "Refund Policy",
        value: "RefundPolicy",
        db: "refundPolicy",
    },
]

export const policy: { label: string, value: string, db: string }[] = [
    ...policySection
]

function PolicyTab({ control, errors }: any) {
    return (
        <>
            <h3>Policy Section</h3>
            {policySection.map((item) => (
                <Forms key={item.value} control={control} errors={errors} item={item} />
            ))}
        </>
    )
}

function Forms({ item, control, errors }: any) {
    return (
        <div className="mt-2">
            <div className="grid grid-cols-2 gap-4">
                <FormItem label={`${item.label} English`}>
                    <Controller
                        control={control}
                        name={`policy${item.value}En`}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {errors[`policy${item.value}En`] && (
                        <small className="text-red-600 py-3">
                            {errors[`policy${item.value}En`].message as string}{' '}
                        </small>
                    )}
                </FormItem>
                <FormItem label={`${item.label} Arabic`}>
                    <Controller
                        control={control}
                        name={`policy${item.value}Ar`}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => <Input dir='rtl' type="text" {...field} />}
                    />
                    {errors[`policy${item.value}Ar`] && (
                        <small className="text-red-600 py-3">
                            {errors[`policy${item.value}Ar`].message as string}{' '}
                        </small>
                    )}
                </FormItem>
            </div>
        </div>
    )
}

export default PolicyTab