import { Button, FormItem, toast, Select, Tag, Upload } from "@/components/ui";
import endpoints from "@/endpoints";
import api from "@/services/api.interceptor";
import { Controller, useForm } from "react-hook-form";
import { AiOutlineSave } from "react-icons/ai";
import Notification from '@/components/ui/Notification'
import { useNavigate, useParams } from "react-router-dom";
import { useEffect, useRef, useState } from "react";
import Input from '@/components/ui/Input'
import { HiX } from "react-icons/hi";

/* eslint-disable */

export default function ManageAttribute() {
  // const [productOptions, setProductOptions] = useState([]);
  const isFirstRun = useRef(true);
  const navigate = useNavigate()
  const params = useParams();
  const [isText, setIsText] = useState(false);
  const [isColor, setIsColor] = useState(false);
  const [isFile, setIsFile] = useState(false);
  const [texts, setTexts] = useState<any>([]);
  const [colors, setColors] = useState<any>([]);
  const [files, setFiles] = useState([]);
  const [filePreview, setFilePreview] = useState<any>([]);

  const getAttributeValue = (type: any, value: any) => {
    switch (type) {
      case 'Text':
        if (!texts.includes(value) && value) {
          setTexts((prevTexts: any) => [...prevTexts, value]);
        }
        break;
      case 'Color':
        if (!colors.includes(value) && value) {
          setColors((prevColors: any) => [...prevColors, value]);
        }
        break;
      case 'File':
        break;
      default:
        break;
    }
  };

  const handleFileUpload = (files: any) => {
    setFiles(files);
  };

  useEffect(() => {
    if (isFirstRun.current) {
      isFirstRun.current = false;
      return;
    }

    // api.post(endpoints.products, { isActive: true }).then((res) => {
    //   if (res?.status == 200) {
    //     const products = res?.data?.result || []
    //     const newproductOptions = products.map((product: any) => ({
    //       value: product._id,
    //       label: product.name.en
    //     }));
    //     setProductOptions(newproductOptions);
    //   }
    // })

    if (params.id) {
      api.get(endpoints.attributeDetail + params.id)
        .then((res) => {
          console.log(res);
          if (res?.status === 200) {
            const attributeData = res?.data?.result;
            const attributeType = attributeData?.type || '';
            setValue('nameEn', attributeData?.name?.en || '');
            setValue('nameAr', attributeData?.name?.ar || '');
            setValue('type', { value: attributeType, label: attributeType });
            setValue('isFilterable', { value: attributeData?.isFiltered, label: attributeData?.isFiltered ? 'True' : 'False' });
            // setValue('products', attributeData?.products?.map((product: any) => ({ value: product?._id, label: product?.name?.en })) || []);
            setIsText(attributeType === 'Text');
            setIsColor(attributeType === 'Color');
            setIsFile(attributeType === 'File');
            if (attributeData?.values?.length > 0) {
              attributeData?.values?.map((value: any) => {
                getAttributeValue(attributeType, value?.value);
              });
            }
          }
        })
        .catch((error) => {
          console.error('Error fetching data: ', error);
        });
    }

  }, [])

  const options: any = [
    { value: "true", label: "True" },
    { value: "false", label: "False" },
  ]

  const attributeTypeOptions: any = [
    { value: "Text", label: "Text" },
    { value: "Color", label: "Color" },
    { value: "File", label: "File" }
  ]

  const {
    handleSubmit,
    control,
    setValue,
    formState: { errors },
  } = useForm<any>();

  const onSubmit = (value: any) => {

    const formData = new FormData();
    const data: any = {
      name: {
        en: value.nameEn,
        ar: value.nameAr
      },
      type: value.type.value,
      isFiltered: value.isFilterable.value,
      // products: value.products.map((product: any) => product.value),
      values: [],
    }
    if (value.type.value == 'Text') {
      data.values = texts;
    }
    if (value.type.value == 'Color') {
      data.values = colors;
    }
    if (value.type.value == 'File') {
      files.map((file: any) => {
        formData.append('files', file);
      })
    }
    formData.append('data', JSON.stringify(data));

    if (params.id) {
      api.put(endpoints.updateAttribute + params.id, formData).then((res) => {
        if (res.status == 200) {
          if (res?.data?.errorCode == 0) {
            toast.push(
              <Notification type="success" title={res.data.message} />, {
              placement: 'top-center',
            })
            navigate('/catalog/attributes');
          }
        } else {
          toast.push(
            <Notification type="warning" title={res.data.message} />, {
            placement: 'top-center',
          })
        }
      })
    }
    else {
      api.post(endpoints.createAttribute, formData).then((res) => {
        if (res.status == 200) {
          if (res?.data?.errorCode == 0) {
            toast.push(
              <Notification type="success" title={res.data.message} />, {
              placement: 'top-center',
            })
            navigate('/catalog/attributes');
          }
        } else {
          toast.push(
            <Notification type="warning" title={res.data.message} />, {
            placement: 'top-center',
          })
        }
      }).catch((err) => {
        console.log(err);
      })
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <h3 className="mb-2">{params.id ? 'Edit Attribute' : 'Add Attribute'}</h3>

      <h5>Name</h5>

      <div className="grid grid-cols-2 gap-4 mt-2">
        <FormItem label="English">
          <Controller
            name="nameEn"
            control={control}
            defaultValue=""
            rules={{ required: 'Field is required' }}
            render={({ field }) => (
              <Input
                type="text"
                className={`${errors.nameEn ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                {...field}
              />
            )}
          />
          {errors.nameEn && <small className="text-red-600 py-3">{errors.nameEn.message as string}</small>}
        </FormItem>

        <FormItem label="Arabic">
          <Controller
            name="nameAr"
            control={control}
            defaultValue=""
            rules={{ required: 'Field is required' }}
            render={({ field }) => (
              <Input
                dir="rtl"
                type="text"
                className={`${errors.nameAr ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                {...field}
              />
            )}
          />
          {errors.nameAr && <small className="text-red-600 py-3">{errors.nameAr.message as string}</small>}
        </FormItem>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <FormItem label="Attribute Type">
          <Controller
            name="type"
            control={control}
            defaultValue=""
            rules={{
              required: 'Field is required',
            }}
            render={({ field }) => (
              <Select
                {...field}
                options={attributeTypeOptions}
                onChange={(e: any) => {
                  console.log(e);
                  field.onChange(e);
                  setIsText(e.value === 'Text');
                  setIsColor(e.value === 'Color');
                  setIsFile(e.value === 'File');
                }}
              />
            )}
          />
          {errors.type && <small className="text-red-600 py-3">{errors.type.message as string}</small>}
        </FormItem>

        {isText &&
          <FormItem label="Attribute Text">
            <Controller
              name="textValues"
              control={control}
              defaultValue=""
              render={({ field }) => (
                <Input
                  type="text"
                  className="input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600"
                  {...field}
                  onKeyDown={(e: any) => {
                    console.log(e);
                    if (e.key === 'Enter') {
                      getAttributeValue('Text', e.target.value);
                      e.target.value = '';
                      setValue('textValues', '');
                    }
                  }}
                />
              )}
            />
            {texts.length > 0 && (
              <div className="flex flex-wrap mt-2 ">
                {texts.map((text: any, index: any) => (
                  <Tag
                    suffix={<HiX size={16} className="ml-1 rtl:mr-1 cursor-pointer" onClick={() => setTexts((prevTexts: any) => prevTexts.filter((item: any) => item !== text))} />}
                    key={index}
                    className="bg-emerald-100 text-emerald-600 dark:bg-emerald-500/20 dark:text-emerald-100 border-0 rounded mr-3 py-3 px-4">
                    {text}
                  </Tag>
                ))}
              </div>
            )}
          </FormItem>
        }

        {isColor &&
          <FormItem label="Attribute Color">
            <Controller
              name="colorValues"
              control={control}
              defaultValue=""
              render={({ field }) => (
                <Input
                  type="color"
                  className="input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600"
                  {...field}
                  onChange={(e) => {
                    console.log(e);
                    getAttributeValue('Color', e.target.value);
                  }}
                />
              )}
            />
            {colors.length > 0 && (
              <div className="flex flex-wrap mt-2 ">
                {colors.map((color: any, index: any) => (
                  <Tag
                    suffix={<HiX size={16} className="ml-1 rtl:mr-1 cursor-pointer"
                      onClick={() => {
                        const updatedColors = [...colors];
                        updatedColors.splice(index, 1);
                        setColors(updatedColors);
                      }}
                    />}
                    key={index}
                    style={{ backgroundColor: color, width: 90, height: 30 }}> </Tag>
                ))}
              </div>
            )}

          </FormItem>
        }

        {isFile &&
          <FormItem label="Attribute File">
            <Controller
              name="values"
              control={control}
              defaultValue=""
              render={({ field }) => (
                <Upload
                  multiple
                  {...field}
                  fileList={files}
                  onChange={handleFileUpload}
                // onChange={(files) => {
                //   handleFileUpload(files);
                //   setFilePreview(files.map(file => URL.createObjectURL(file)));
                // }}
                />
              )}
            />
            {filePreview.length > 0 && (
              <div className="mt-2">
                {filePreview.map((preview: any, index: any) => (
                  <div key={index}>
                    <img src={preview} alt={`Preview ${index}`} style={{ maxWidth: '100px' }} />
                  </div>
                ))}
              </div>
            )}

          </FormItem>
        }
      </div>

      <div className="grid grid-cols-2 gap-4">
        {/* <FormItem label="Product">
          <Controller
            name="products"
            control={control}
            defaultValue=""
            rules={{ required: 'Field is required' }}
            render={({ field }) => (
              <Select
                isMulti
                options={productOptions}
                {...field}
              />
            )}
          />
          {errors.isSearchable && <small className="text-red-600 py-3">{errors.isSearchable.message as string}</small>}

        </FormItem> */}

        <FormItem label="Is Filterable">
          <Controller
            name="isFilterable"
            control={control}
            defaultValue=""
            rules={{ required: 'Field is required' }}
            render={({ field }) => (
              <Select
                options={options}
                {...field}
              />
            )}
          />
          {errors.isFilterable && <small className="text-red-600 py-3">{errors.isFilterable.message as string}</small>}
        </FormItem>
      </div>

      <Button className='float-right mt-6' variant="solid" type="submit" icon={<AiOutlineSave />}>
        Save
      </Button>

    </form>
  )
}
