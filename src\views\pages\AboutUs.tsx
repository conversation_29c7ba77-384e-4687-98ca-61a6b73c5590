import { Button, <PERSON>I<PERSON>, toast, FormContainer, Upload } from '@/components/ui'
import endpoints from '@/endpoints'
import api from '@/services/api.interceptor'
import { Controller, useFieldArray, useForm } from 'react-hook-form'
import { AiOutlineMinus, AiOutlinePlus, AiOutlineSave } from 'react-icons/ai'
import Notification from '@/components/ui/Notification'
import { useNavigate } from 'react-router-dom'
import Input from '@/components/ui/Input'
import { useEffect, useState } from 'react'
import Breadcrumb from '../modals/BreadCrumb'

const baseUrl = import.meta.env.VITE_ASSET_URL

/* eslint-disable */
export default function AboutUs() {
    const [isUpdate, setIsUpdate] = useState(false)
    const [imageFile, setImageFile] = useState([])
    const [timelineFile, setTimelineFile]:any = useState([])
    const [bottomFile, setBottomFile]:any = useState([])
    const [timelineArray, setTimelineArray] = useState([])
    const [bottomArray, setBottomArray] = useState([])
    const [ogImageFile, setOgImageFile] = useState<string[]>([])
    const navigate = useNavigate()

    const breadcrumbItems = [{ title: 'About Us', url: '' }]

    useEffect(() => {
        api.get(endpoints.getAbouts)
            .then((res) => {
                if (res?.status == 200) {
                    const data = res?.data?.result[0]

                    setTimelineArray(data?.sectionThree?.timeline?.map((item:any)=> [baseUrl + item.image] ))
                    setTimelineFile(data?.sectionThree?.timeline?.map((item:any)=> [baseUrl + item.image] ))
                    
                    setBottomArray(data?.sectionFour?.data?.map((item:any)=> [baseUrl + item.image] ))
                    setBottomFile(data?.sectionFour?.data?.map((item:any)=> [baseUrl + item.image] ))

                    setImageFile([baseUrl + data?.sectionOne?.image] as any)
                    setValue('titleEn', data.sectionOne.title.en)
                    setValue('titleAr', data.sectionOne.title.ar)
                    setValue('descriptionEn', data.sectionOne.description.en)
                    setValue('descriptionAr', data.sectionOne.description.ar)
                    if (data.sectionTwo.length > 0) {
                        setValue(
                            'stats',
                            data?.sectionTwo.map((item: any) => ({
                                titleEn: item.title.en,
                                titleAr: item.title.ar,
                                count: item.count,
                                symbol: item?.symbol?? "",
                            }))
                        )
                    }
                    setValue('timelineTitleEn', data.sectionThree.title.en)
                    setValue('timelineTitleAr', data.sectionThree.title.ar)
                    setValue('timelineDescEn', data.sectionThree.description.en)
                    setValue('timelineDescAr', data.sectionThree.description.ar)
                    if (data.sectionThree.timeline.length > 0) {
                        setValue(
                            'timeline',
                            data?.sectionThree.timeline.map((item: any) => ({
                                titleEn: item.title.en,
                                titleAr: item.title.ar,
                                descEn: item.description.en,
                                descAr: item.description.ar,
                                year: item.year,
                            }))
                        )
                    }
                    setValue('headingEn', data.sectionFour.title.en)
                    setValue('headingAr', data.sectionFour.title.ar)
                    if (data?.sectionFour?.data.length > 0) {
                        setValue(
                            'bottom',
                            data?.sectionFour?.data.map((item: any) => ({
                                titleEn: item.title.en,
                                titleAr: item.title.ar,
                                descEn: item.description.en,
                                descAr: item.description.ar,
                            }))
                        )
                    }
                    setValue('metaTitleEn', data?.seoDetails?.title?.en)
                    setValue('metaTitleAr', data?.seoDetails?.title?.ar)
                    setValue('metaDescriptionEn', data?.seoDetails?.description?.en)
                    setValue('metaDescriptionAr', data?.seoDetails?.description?.ar)
                    setValue('metaKeywordsEn', data?.seoDetails?.keywords?.en)
                    setValue('metaKeywordsAr', data?.seoDetails?.keywords?.ar)
                    setValue('metaCanonicalUrl', data?.seoDetails?.canonical?.en)
                    setValue('metaCanonicalUrlAr', data?.seoDetails?.canonical?.ar)
                    setOgImageFile([baseUrl + data?.seoDetails?.ogImage])
                    setIsUpdate(true)
                }
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }, [])

    const handleImageUpload = (files: any) => {
        setImageFile(files)
    }

    const handleTimelineUpload = (files: any, index: any) => {
        const updatedTimelineFiles: any = [...timelineFile]
        updatedTimelineFiles[index] = files[0]
        setTimelineFile(updatedTimelineFiles)

        const updatedTimelineArray: any = [...timelineArray]
        updatedTimelineArray[index] = files
        setTimelineArray(updatedTimelineArray)
    }

    const handleBottomUpload = (files: any, index: any) => {
        const updatedBottomFiles: any = [...bottomFile]
        updatedBottomFiles[index] = files[0]
        setBottomFile(updatedBottomFiles)

        const updatedBottomArray: any = [...bottomArray]
        updatedBottomArray[index] = files
        setBottomArray(updatedBottomArray)
    }

    const {
        handleSubmit,
        control,
        setFocus,
        setError,
        setValue,
        formState: { errors },
    } = useForm<any>()

    const {
        fields: statsFields,
        append: statsAppend,
        remove: statsRemove,
    } = useFieldArray({
        control,
        name: 'stats',
    })

    const {
        fields: timelineFields,
        append: timelineAppend,
        remove: timelineRemove,
    } = useFieldArray({
        control,
        name: 'timeline',
    })

    const {
        fields: bottomFields,
        append: bottomAppend,
        remove: bottomRemove,
    } = useFieldArray({
        control,
        name: 'bottom',
    })

    const onSubmit = (value: any) => {
        console.log(value)
        
        const formData = new FormData()
        formData.append('sectionOne[title][en]', value.titleEn)
        formData.append('sectionOne[title][ar]', value.titleAr)
        formData.append('sectionOne[description][en]', value.descriptionEn)
        formData.append('sectionOne[description][ar]', value.descriptionAr)

        if(imageFile?.length < 1) {
            toast.push(
                <Notification
                    type="danger"
                    title="Image is required"
                />,
                {
                    placement: 'top-center',
                }
            )
            setFocus('image')
            return
        }
        let index = null
        for(let i in timelineFields){
            if(timelineFile[i]?.length < 1){
                index = i;
                toast.push(
                    <Notification
                        type="danger"
                        title="Image is required"
                    />,
                    {
                        placement: 'top-center',
                    }
                )
                break;
            }
        }

        if(index !== null){
            setError(`timeline.${index}.image`, {message: 'Image is required', type: 'required'}, {shouldFocus: true})
            return;
        }

        index = null
        for(let i in bottomFields){
            if(bottomFile[i]?.length < 1){
                index = i;
                toast.push(
                    <Notification
                        type="danger"
                        title="Image is required"
                    />,
                    {
                        placement: 'top-center',
                    }
                )
                break;
            }
        }

        if(index !== null){
            console.log('hi')
            setError(`bottom.${index}.image`, {message: 'Image is required', type: 'required'}, {shouldFocus: true})
            return;
        }

        if (imageFile) formData.append('topImage', imageFile[0])

        for (let i = 0; i < value.stats.length; i++) {
            formData.append(
                `sectionTwo[${i}][title][en]`,
                value.stats[i].titleEn
            )
            formData.append(
                `sectionTwo[${i}][title][ar]`,
                value.stats[i].titleAr
            )
            formData.append(`sectionTwo[${i}][count]`, value.stats[i].count)
            formData.append(`sectionTwo[${i}][symbol]`, value.stats[i]?.symbol)
        }

        formData.append('sectionThree[title][en]', value.timelineTitleEn)
        formData.append('sectionThree[title][ar]', value.timelineTitleAr)
        formData.append('sectionThree[description][en]', value.timelineDescEn)
        formData.append('sectionThree[description][ar]', value.timelineDescAr)

        for (let i = 0; i < value.timeline.length; i++) {
            formData.append(
                `sectionThree[timeline][${i}][title][en]`,
                value.timeline[i].titleEn
            )
            formData.append(
                `sectionThree[timeline][${i}][title][ar]`,
                value.timeline[i].titleAr
            )
            formData.append(
                `sectionThree[timeline][${i}][description][en]`,
                value.timeline[i].descEn
            )
            formData.append(
                `sectionThree[timeline][${i}][description][ar]`,
                value.timeline[i].descAr
            )
            formData.append(
                `sectionThree[timeline][${i}][year]`,
                value.timeline[i].year
            )
        }

        if (timelineFile?.length > 0) {
            for (let i = 0; i < timelineFile.length; i++) {
                if(typeof timelineFile[i][0] === "string"){
                    console.log("string")
                    formData.append('timelineImage', new File([], "empty", {type: "image/jpeg"}))
                }else{
                    formData.append('timelineImage', timelineFile[i])
                }
            }
        }

        formData.append('sectionFour[title][en]', value.headingEn)
        formData.append('sectionFour[title][ar]', value.headingAr)
        for (let i = 0; i < value.bottom.length; i++) {
            formData.append(
                `sectionFour[data][${i}][title][en]`,
                value.bottom[i].titleEn
            )
            formData.append(
                `sectionFour[data][${i}][title][ar]`,
                value.bottom[i].titleAr
            )
            formData.append(
                `sectionFour[data][${i}][description][en]`,
                value.bottom[i].descEn
            )
            formData.append(
                `sectionFour[data][${i}][description][ar]`,
                value.bottom[i].descAr
            )
        }

        if (bottomFile?.length > 0) {
            for (let i = 0; i < bottomFile.length; i++) {
                if(typeof bottomFile[i][0] === "string"){
                    formData.append('bottomImage', new File([], "empty", {type: "image/jpeg"}))
                }else{
                    formData.append('bottomImage', bottomFile[i])
                }
            }
        }

        formData.append('ogImage', ogImageFile[0])
        formData.append("seoDetails[title][en]", value.metaTitleEn)
        formData.append("seoDetails[title][ar]", value.metaTitleAr)
        formData.append("seoDetails[description][en]", value.metaDescriptionEn)
        formData.append("seoDetails[description][ar]", value.metaDescriptionAr)
        formData.append("seoDetails[keywords][en]", value.metaKeywordsEn)
        formData.append("seoDetails[keywords][ar]", value.metaKeywordsAr)
        formData.append("seoDetails[canonical][en]", value.metaCanonicalUrl)
        formData.append("seoDetails[canonical][ar]", value.metaCanonicalUrlAr)
  

        if (isUpdate) {
            api.post(endpoints.updateAbout, formData).then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification
                            type="success"
                            title={res.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    navigate('/pages/about-us')
                }
            })
        } else {
            api.post(endpoints.createAbout, formData)
                .then((res) => {
                    if (res.status == 200) {
                        toast.push(
                            <Notification
                                type="success"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                        navigate('/pages/about-us')
                    }
                })
                .catch((err) => {
                    console.log(err)
                })
        }
    }

    const handleOgImageUpload = (file: any) => {
        setOgImageFile(file)
    }

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <h3 className="mb-2">About Us</h3>
            <Breadcrumb items={breadcrumbItems} />
            <h5 className="mb-2">Title</h5>
            <div className="grid grid-cols-2 gap-4 mt-4">
                <FormItem label="English">
                    <Controller
                        name="titleEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Title is required' }}
                        render={({ field }) => (
                            <input
                                type="text"
                                className={`${
                                    errors.titleEn
                                        ? ' input input-md h-11 input-invalid'
                                        : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                }`}
                                {...field}
                            />
                        )}
                    />
                    {errors.titleEn && (
                        <small className="text-red-600 py-3">
                            {errors.titleEn.message as string}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Arabic">
                    <Controller
                        name="titleAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Title is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                // className={`${errors.titleAr
                                //     ? ' input input-md h-11 input-invalid'
                                //     : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                //     }`}
                                {...field}
                                dir="rtl"
                            />
                        )}
                    />
                    {/* {errors.titleAr && (
                        <small className="text-red-600 py-3">
                            {errors.titleAr.message as string}
                        </small>
                    )} */}
                </FormItem>
            </div>

            <h5 className="mb-2">Description</h5>
            <div className="grid grid-cols-2 gap-4">
                <FormItem label="English">
                    <Controller
                        name="descriptionEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'description is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                className={`${
                                    errors.descriptionEn
                                        ? 'input input-md h-11 input-invalid'
                                        : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                }`}
                                {...field}
                            />
                        )}
                    />
                    {errors.descriptionEn && (
                        <small className="text-red-600 py-3 ms-24">
                            {errors.descriptionEn.message as string}{' '}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Arabic">
                    <Controller
                        name="descriptionAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'description is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                // className={`${errors.descriptionAr
                                //     ? ' input input-md h-11 input-invalid'
                                //     : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                //     }`}
                                {...field}
                            />
                        )}
                    />
                    {/* {errors.descriptionAr && (
                        <small className="text-red-600 py-3">
                            {errors.descriptionAr.message as string}
                        </small>
                    )} */}
                </FormItem>
            </div>

            <div>
                <FormItem label="Image">
                    <Upload
                        draggable
                        uploadLimit={1}
                        accept="image/*"
                        fileList={imageFile}
                        onChange={handleImageUpload}
                        ratio={[1125, 426]}
                        onFileRemove={() => {
                            setImageFile([])
                        }}
                    />
                </FormItem>
                <input type="file" className='sr-only' {...control.register("image")} />
            </div>

            <Button
                variant="solid"
                type="button"
                icon={<AiOutlinePlus />}
                onClick={() => {
                    if (statsFields.length < 4) {
                        statsAppend({})
                    } else {
                        toast.push(
                            <Notification
                                type="warning"
                                title="Maximum 4 allowed"
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                    }
                }}
            >
                Add Stats
            </Button>

            <ul className="mt-4">
                {statsFields.map((item, index) => {
                    return (
                        <li key={item.id}>
                            <div className="flex space-x-2">
                                <FormContainer layout="inline">
                                    <FormItem label="Title(En)">
                                        <Controller
                                            name={`stats.${index}.titleEn`}
                                            control={control}
                                            defaultValue={''}
                                            render={({ field }) => (
                                                <input
                                                    type="text"
                                                    className={`${
                                                        errors.stats
                                                            ? ' input input-md h-11 input-invalid'
                                                            : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                                    }`}
                                                    {...field}
                                                />
                                            )}
                                        />
                                    </FormItem>

                                    <FormItem label="Title(Ar)">
                                        <Controller
                                            defaultValue={''}
                                            render={({ field }) => (
                                                <Input type="text" {...field} />
                                            )}
                                            name={`stats.${index}.titleAr`}
                                            control={control}
                                        />
                                    </FormItem>

                                    <FormItem label="count">
                                        <Controller
                                            defaultValue={''}
                                            render={({ field }) => (
                                                <Input type="text" {...field} />
                                            )}
                                            name={`stats.${index}.count`}
                                            control={control}
                                        />
                                    </FormItem>
                                    <FormItem label="symbol">
                                        <Controller
                                            defaultValue={''}
                                            render={({ field }) => (
                                                <Input type="text" {...field} />
                                            )}
                                            name={`stats.${index}.symbol`}
                                            control={control}
                                        />
                                    </FormItem>
                                </FormContainer>

                                <Button
                                    size="sm"
                                    shape="circle"
                                    type="button"
                                    icon={<AiOutlineMinus />}
                                    onClick={() => statsRemove(index)}
                                ></Button>
                            </div>
                        </li>
                    )
                })}
            </ul>

            <h4 className="py-4">Timeline Details</h4>
            <h5 className="mb-2">Title</h5>
            <div className="grid grid-cols-2 gap-4">
                <FormItem label="English">
                    <Controller
                        name="timelineTitleEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Title is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                className={`${
                                    errors.timelineTitleEn
                                        ? 'input input-md h-11 input-invalid'
                                        : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                }`}
                                {...field}
                            />
                        )}
                    />
                    {errors.timelineTitleEn && (
                        <small className="text-red-600 py-3">
                            {errors.timelineTitleEn.message as string}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Arabic">
                    <Controller
                        name="timelineTitleAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Title is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                // className={`${errors.timelineTitleAr
                                //     ? ' input input-md h-11 input-invalid'
                                //     : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                //     }`}
                                {...field}
                            />
                        )}
                    />
                    {/* {errors.timelineTitleAr && (
                        <small className="text-red-600 py-3">
                            {errors.timelineTitleAr.message as string}
                        </small>
                    )} */}
                </FormItem>
            </div>

            <h5 className="mb-2">Description</h5>
            <div className="grid grid-cols-2 gap-4">
                <FormItem label="English">
                    <Controller
                        name="timelineDescEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Description is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                className={`${
                                    errors.timelineDescEn
                                        ? 'input input-md h-11 input-invalid'
                                        : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                }`}
                                {...field}
                            />
                        )}
                    />
                    {errors.timelineDescEn && (
                        <small className="text-red-600 py-3">
                            {errors.timelineDescEn.message as string}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Arabic">
                    <Controller
                        name="timelineDescAr"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Description is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                // className={`${errors.timelineDescAr
                                //     ? ' input input-md h-11 input-invalid'
                                //     : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                //     }`}
                                {...field}
                            />
                        )}
                    />
                    {/* {errors.timelineDescAr && (
                        <small className="text-red-600 py-3">
                            {errors.timelineDescAr.message as string}
                        </small>
                    )} */}
                </FormItem>
            </div>

            <ul className="mt-4">
                {timelineFields.map((field, index) => {
                    return (
                        <li key={field.id}>
                            <h5> Title</h5>
                            <FormContainer className="grid grid-cols-2 gap-4">
                                <FormItem label="English">
                                    <Controller
                                        defaultValue={''}
                                        render={({ field }) => (
                                            <Input type="text" {...field} />
                                        )}
                                        name={`timeline.${index}.titleEn`}
                                        control={control}
                                    />
                                </FormItem>
                                <FormItem label="Arabic">
                                    <Controller
                                        defaultValue={''}
                                        render={({ field }) => (
                                            <Input type="text" {...field} />
                                        )}
                                        name={`timeline.${index}.titleAr`}
                                        control={control}
                                    />
                                </FormItem>
                            </FormContainer>

                            <h5>Description</h5>
                            <FormContainer className="grid grid-cols-2 gap-4">
                                <FormItem label="English">
                                    <Controller
                                        defaultValue={''}
                                        render={({ field }) => (
                                            <Input type="text" {...field} />
                                        )}
                                        name={`timeline.${index}.descEn`}
                                        control={control}
                                    />
                                </FormItem>
                                <FormItem label="Arabic">
                                    <Controller
                                        defaultValue={''}
                                        render={({ field }) => (
                                            <Input type="text" {...field} />
                                        )}
                                        name={`timeline.${index}.descAr`}
                                        control={control}
                                    />
                                </FormItem>
                            </FormContainer>

                            <FormContainer className="grid grid-cols-2 gap-4">
                                <FormItem label="Year">
                                    <Controller
                                        defaultValue={''}
                                        render={({ field }) => (
                                            <Input type="text" {...field} />
                                        )}
                                        name={`timeline.${index}.year`}
                                        control={control}
                                    />
                                </FormItem>

                                <FormItem className={errors[`timeline.${index}.image`] ? 'outline outline-1 outline-red-600' : ''} label="Image(Preferred size: 376x165 px)">
                                    <Upload
                                        uploadLimit={1}
                                        fileList={timelineArray[index]}
                                        onChange={(files) =>
                                            handleTimelineUpload(files, index)
                                        }
                                        onFileRemove={()=>{
                                            let newTimelineArray:any = [...timelineArray];
                                            newTimelineArray[index] = [];
                                            setTimelineArray(newTimelineArray)

                                            let newTimelineFiles:any = [...timelineFile];
                                            newTimelineFiles[index] = [];
                                            setTimelineFile(newTimelineFiles)
                                        }}
                                    />
                                    <input type="file" className='sr-only' {...control.register(`timeline.${index}.image`)} />
                                </FormItem>
                            </FormContainer>

                            <Button
                                className="mb-3"
                                type="button"
                                onClick={() => timelineRemove(index)}
                            >
                                Delete Timeline
                            </Button>
                        </li>
                    )
                })}
            </ul>
            <Button
                className="mb-2"
                variant="solid"
                type="button"
                icon={<AiOutlinePlus />}
                onClick={() => timelineAppend({})}
            >
                Add Timelines
            </Button>

            <h4>Bottom Section</h4>
            <h5 className="mt-2">Heading</h5>
            <div className="grid grid-cols-2 gap-4">
                <FormItem label="English">
                    <Controller
                        control={control}
                        defaultValue=""
                        name="headingEn"
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                </FormItem>

                <FormItem label="Arabic">
                    <Controller
                        control={control}
                        defaultValue=""
                        name="headingAr"
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                </FormItem>
            </div>

            <Button
                variant="solid"
                type="button"
                icon={<AiOutlinePlus />}
                onClick={() => bottomAppend({})}
            >
                Add Bottom Section Data
            </Button>

            <ul className="mt-4">
                {bottomFields.map((field, index) => {
                    return (
                        <li key={field.id}>
                            <h5> Title</h5>
                            <FormContainer className="grid grid-cols-2 gap-4">
                                <FormItem label="English">
                                    <Controller
                                        defaultValue={''}
                                        render={({ field }) => (
                                            <Input type="text" {...field} />
                                        )}
                                        name={`bottom.${index}.titleEn`}
                                        control={control}
                                    />
                                </FormItem>

                                <FormItem label="Arabic">
                                    <Controller
                                        defaultValue={''}
                                        render={({ field }) => (
                                            <Input
                                                dir="rtl"
                                                type="text"
                                                {...field}
                                            />
                                        )}
                                        name={`bottom.${index}.titleAr`}
                                        control={control}
                                    />
                                </FormItem>
                            </FormContainer>

                            <h5>Description</h5>
                            <FormContainer className="grid grid-cols-2 gap-4">
                                <FormItem label="English">
                                    <Controller
                                        defaultValue={''}
                                        render={({ field }) => (
                                            <Input type="text" {...field} />
                                        )}
                                        name={`bottom.${index}.descEn`}
                                        control={control}
                                    />
                                </FormItem>
                                <FormItem label="Arabic">
                                    <Controller
                                        defaultValue={''}
                                        render={({ field }) => (
                                            <Input
                                                dir="rtl"
                                                type="text"
                                                {...field}
                                            />
                                        )}
                                        name={`bottom.${index}.descAr`}
                                        control={control}
                                    />
                                </FormItem>
                            </FormContainer>

                            <FormContainer className="grid grid-cols-2 gap-4">
                                <FormItem label="Image">
                                    <Upload
                                        draggable
                                        uploadLimit={1}
                                        fileList={bottomArray[index]}
                                        accept="image/*"
                                        onChange={(files) =>
                                            handleBottomUpload(files, index)
                                        }
                                        onFileRemove={()=>{
                                            let newBottomArray:any = [...bottomArray];
                                            newBottomArray[index] = [];
                                            setBottomArray(newBottomArray)

                                            let newBottomFiles:any = [...bottomFile];
                                            newBottomFiles[index] = [];
                                            setBottomFile(newBottomFiles)
                                        }}
                                        ratio={[525, 379]}
                                    />
                                    <input type="file" className='sr-only' {...control.register(`bottom.${index}.image`)} />
                                </FormItem>

                                <Button
                                    className="w-28 my-auto mx-auto "
                                    type="button"
                                    onClick={() => bottomRemove(index)}
                                >
                                    Delete
                                </Button>
                            </FormContainer>
                        </li>
                    )
                })}
            </ul>

            <h5>Seo Section</h5>
            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Title English">
                    <Controller
                        name="metaTitleEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaTitleEn && (
                        <small className="text-red-600 py-3">
                            {errors.metaTitleEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Meta Title Arabic">
                    <Controller
                        name="metaTitleAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaTitleAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaTitleAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Description English">
                    <Controller
                        name="metaDescriptionEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaDescriptionEn && (
                        <small className="text-red-600 py-3">
                            {errors.metaDescriptionEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Meta Description Arabic">
                    <Controller
                        name="metaDescriptionAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaDescriptionAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaDescriptionAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Keywords English">
                    <Controller
                        name="metaKeywordsEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaKeywordsEn && (
                        <small className="text-red-600 py-3">
                            {errors.metaKeywordsEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Meta Keywords Arabic">
                    <Controller
                        name="metaKeywordsAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaKeywordsAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaKeywordsAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Canonical URL English">
                    <Controller
                        name="metaCanonicalUrl"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaCanonicalUrl && (
                        <small className="text-red-600 py-3">
                            {errors.metaCanonicalUrl.message as string}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Meta Canonical URL Arabic">
                    <Controller
                        name="metaCanonicalUrlAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaCanonicalUrlAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaCanonicalUrlAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
               <FormItem label="Meta OG Image">
                    <Upload
                        draggable
                        uploadLimit={1}
                        accept="image/*"
                        fileList={ogImageFile}
                        onChange={handleOgImageUpload}
                        ratio={[1126, 1200]}
                    />
                </FormItem>
                
            </div>

            <Button
                className="float-right mt-4"
                variant="solid"
                type="submit"
                icon={<AiOutlineSave />}
            >
                Save
            </Button>
        </form>
    )
}
