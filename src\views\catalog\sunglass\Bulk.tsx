// ImportForm.jsx
import React, { useState } from 'react'
import Button from '@/components/ui/Button'
import api from '@/services/api.interceptor'
import endpoints from '@/endpoints'
import toast from '@/components/ui/toast'
import Notification from '@/components/ui/Notification'
import FileStructure from './FileStructure'
import { Dropzone } from '@/components/shared/ZipDrop'

interface BulkProps {
    onClose: () => void
    getProducts: () => void
    type: 'Products' | 'Categories' | 'Customers' | 'Contact Lens' | 'Sunglasses';
    controller: any
}

const Bulk = ({ onClose, getProducts, type, controller }: BulkProps) => {
    const [file, setFile] = useState(null)
    const [isImporting, setIsImporting] = useState(false)
    const [progress, setProgress] = useState(0)
    const [progressTxt, setProgressTxt] = useState('')
    const [total, setTotal] = useState(0)
    const [progressPercent, setProgressPercent] = useState(0)

    const handleFileChange = (event: any) => {
        setFile(event)
    }

    const handleImport = async () => {
        if (!file) return

        try {
            setIsImporting(true)

            let endpoint =
                type == 'Products'
                    ? endpoints.importProducts
                    : type === 'Sunglasses'
                        ? endpoints.importSunglasses
                        : type === 'Categories'
                            ? endpoints.importCategories
                            : type === 'Contact Lens'
                                ? endpoints.contactLensImport
                                : endpoints.customerImport


            const formData = new FormData()
            formData.append('file', file)

            const response = await api
                .post(endpoint, formData, {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    },

                    onDownloadProgress: (progressEvent) => {
                        const xhr = progressEvent.event.target
                        const { responseText } = xhr
                        console.log('=====responseText======')
                        console.log(progressEvent)
                        console.log(responseText?.split('\n\n')?.slice(-2))
                        setProgressTxt(responseText?.split('\n\n')?.at(-2))
                        const [processed, total] = responseText
                            ?.split('\n\n')
                            ?.at(-2)
                            ?.split('/') || [0, 0]
                        setTotal(parseInt(total, 10))
                        setProgress(parseInt(processed, 10))
                        setProgressPercent(
                            (parseInt(processed, 10) / parseInt(total, 10)) *
                            100
                        )
                    },
                })
                .then((res) => {
                    if (res.data.includes('Error')) {
                        toast.push(
                            <Notification
                                type="danger"
                                title={
                                    res.data.message ||
                                    `${type} Imported Error`
                                }
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                        getProducts()
                        return
                    }
                    setIsImporting(false)
                    onClose()
                    setFile(null)
                    setProgress(0)
                    setProgressPercent(0)
                    setTotal(0)
                    setProgressTxt('')
                    console.log(res)
                    console.log(res.data)
                    toast.push(
                        <Notification
                            type="success"
                            title={
                                res.data.message ||
                                `${type} Imported successfully`
                            }
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    getProducts()
                })

        } catch (error: any) {
            controller.current = null;
            console.error('Error importing' + type + ': ', error)
            toast.push(
                <Notification
                    type="danger"
                    title={
                        error?.response?.data?.error_message ||
                        `${type} Imported Error`
                    }
                />,
                {
                    placement: 'top-center',
                }
            )
            setIsImporting(false)
        }
    }

    return (
        <div>
            <h2>Import {type}</h2>
            <FileStructure type={type} />
            <div>
                <Dropzone
                    accept={type === 'Customers' ? 'csv' : 'zip'}
                    onFileUrlChange={handleFileChange}
                />
                {/* <input type="file" onChange={handleFileChange} /> */}
            </div>
            {isImporting ? (
                <div>
                    <div className="mt-5 border border-dashed flex flex-col gap-5 items-center justify-center p-5">
                        <p>Importing {type}...</p>
                        <p>
                            {type === 'Sunglasses' || type === 'Contact Lens' ? <>
                                {progressTxt}
                            </> : <>
                                Processed {progress} out of {total} {type}
                            </>}
                        </p>
                        <div className="w-full bg-gray-200 rounded-full h-2.5 mb-4 dark:bg-gray-700">
                            <div
                                className="bg-blue-600 h-2.5 rounded-full dark:bg-blue-500"
                                style={{ width: `${progressPercent}%` }}
                            ></div>
                        </div>
                        {type != 'Sunglasses' && type != 'Contact Lens' ? (
                            <small className="italic">{progressTxt}</small>
                        ) : ""}
                        {/* <small className="italic">{progressTxt}</small> */}
                    </div>

                    {/* <progress value={progress} max={total} />

                    <span>{progressTxt} </span> */}
                </div>
            ) : (
                <Button
                    variant="solid"
                    className="mt-4"
                    onClick={handleImport}
                    disabled={!file}
                >
                    Import
                </Button>
            )}
        </div>
    )
}

export default Bulk
