/* eslint-disable @typescript-eslint/no-unused-vars */
import endpoints from '@/endpoints'
import api from '@/services/api.interceptor'
import { useState } from 'react'
import Previews from './Previews'
import { toast } from '../ui'
import Notification from '../ui/Notification'
import CloseButton from '../ui/CloseButton'

const defaultFileState = {
    name: '',
    size: 0,
    fileUrl: '',
}

export const Dropzone = ({
    onFileUrlChange,
    className,
    playgroundView = false,
    previews,
    accept = 'zip',
}: {
    onFileUrlChange: (fileUrl: any) => void
    className?: string
    playgroundView?: boolean
    previews?: string[]
    accept?: string
}) => {
    const [file, setFile] = useState(defaultFileState)
    const [isHoveredOnDropzone, setIsHoveredOnDropzone] = useState(false)
    const [hasNonPdfFile, setHasNonPdfFile] = useState(false)
    const maxFileSize = 5000 * 1024 * 1024 //  2000 MB in bytes
    const acceptedFileTypes = ['zip']

    const [hasFile, setHasFile] = useState(false)

    // const hasFile = Boolean(file.name);

    const triggerMessage = (msg: string) => {
        toast.push(
            <Notification type="danger" duration={2000}>
                {msg || 'Upload Failed!'}
            </Notification>,
            {
                placement: 'top-center',
            }
        )
    }

    const setNewFile = (newFile: File) => {
        setHasFile(true)

        if (newFile.size > maxFileSize) {
            triggerMessage('File size exceeds the maximum limit of 2GB.')
            return
        }

        if (file.fileUrl) {
            URL.revokeObjectURL(file.fileUrl)
        }

        const { name, size } = newFile
        const fileUrl = URL.createObjectURL(newFile)
        setFile({ name, size, fileUrl })

        onFileUrlChange(newFile)
    }

    const onDrop = (event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault()
        const newFile = event.dataTransfer.files[0]
        if (newFile.name.endsWith('.' + accept)) {
            setHasNonPdfFile(false)
            setNewFile(newFile)
        } else {
            setHasNonPdfFile(true)
        }
        setIsHoveredOnDropzone(false)
    }

    const onInputChange = async (
        event: React.ChangeEvent<HTMLInputElement>
    ) => {
        const files = event.target.files
        if (!files) return

        const newFile = files[0]
        setNewFile(newFile)
    }

    const onRemove = () => {
        setFile(defaultFileState)
        setHasFile(false)
        onFileUrlChange(undefined)
    }

    return (
        <>
            <div
                className={`
        flex justify-center rounded-md border-2 border-dashed border-gray-300 px-6 
        ${isHoveredOnDropzone && 'border-indigo-400'}
        ${playgroundView ? 'pb-6 pt-4' : 'py-4'}`}
                onDragOver={(event) => {
                    event.preventDefault()
                    setIsHoveredOnDropzone(true)
                }}
                onDragLeave={() => setIsHoveredOnDropzone(false)}
                onDrop={onDrop}
            >
                <div className={'text-center'}>
                    {!hasFile ? (
                        <>
                            <p className="mt-1 opacity-80 text-gray-800 dark:text-white">
                                Support: {accept}
                            </p>
                            <p className="mt-1 opacity-80 text-gray-800 dark:text-white">
                                Max file size: 2 GB
                            </p>
                            <p
                                className={
                                    'pt-3 text-gray-700 dark:text-gray-100'
                                }
                            >
                                Browse a file or drop it here
                            </p>
                        </>
                    ) : (
                        <div className="flex items-center justify-center gap-3 pt-3">
                            <div className="pl-7 font-semibold text-gray-900">
                                {file.name} - {getFileSizeString(file.size)}
                            </div>
                            <button
                                type="button"
                                className="outline-theme-blue rounded-md p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
                                title="Remove file"
                                onClick={onRemove}
                            >
                                <CloseButton />
                            </button>
                        </div>
                    )}
                    <div className="pt-4">
                        {!hasFile ? (
                            <>
                                <label
                                    className={
                                        'within-outline-theme-purple border hover:bg-indigo-400 hover:text-white cursor-pointer rounded-full px-6 pb-2.5 pt-2 font-semibold shadow-sm'
                                    }
                                >
                                    Browse file
                                    <input
                                        type="file"
                                        className="sr-only"
                                        accept={'.' + accept}
                                        onChange={onInputChange}
                                    />
                                </label>
                                {hasNonPdfFile && (
                                    <p className="mt-6 text-red-400">
                                        Only {accept} file is supported
                                    </p>
                                )}
                            </>
                        ) : (
                            <></>
                        )}
                    </div>
                </div>
            </div>
        </>
    )
}

const getFileSizeString = (fileSizeB: number) => {
    const fileSizeKB = fileSizeB / 1024
    const fileSizeMB = fileSizeKB / 1024
    if (fileSizeKB < 1000) {
        return fileSizeKB.toPrecision(3) + ' KB'
    } else if (fileSizeKB < 1000000) {
        return fileSizeMB.toPrecision(3) + ' MB'
    }else {
        return (fileSizeMB / 1024).toFixed(2) + ' GB'
    }
}
