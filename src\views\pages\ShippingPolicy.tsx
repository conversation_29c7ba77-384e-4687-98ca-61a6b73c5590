/*eslint-disable */
import { RichTextEditor } from '@/components/shared'
import Button from '@/components/ui/Button'
import endpoints from '@/endpoints'
import api from '@/services/api.interceptor'
import { use<PERSON><PERSON>, SubmitHandler, Controller } from 'react-hook-form'
import toast from '@/components/ui/toast'
import Notification from '@/components/ui/Notification'
import { useEffect, useState } from 'react'
import Breadcrumb from '../modals/BreadCrumb'
import { FormItem, Input } from '@/components/ui'
import { Dropzone } from '@/components/shared/Dropzone'

export default function CookiePolicy() {
    const [isUpdate, setIsUpdate] = useState(false)
    const [formSubmitted, setFormSubmitted] = useState(false)
    const [ogImageFile, setOgImageFile] = useState<string[]>([])

    const breadcrumbItems = [{ title: 'Shipping Policy', url: '' }]

    useEffect(() => {
        api.get(endpoints.shippingPolicy).then((res) => {
            if (res?.status == 200) {
                if (res?.data?.result?.length > 0) {
                    const data = res?.data?.result[0]
                    setValue('titleEn', data?.title?.en)
                    setValue('titleAr', data?.title?.ar)
                    setValue('contentEn', data?.content?.en)
                    setValue('contentAr', data?.content?.ar)
                    setValue('metaTitleEn', data?.seoDetails?.title?.en)
                    setValue('metaTitleAr', data?.seoDetails?.title?.ar)
                    setValue('metaDescriptionEn', data?.seoDetails?.description?.en)
                    setValue('metaDescriptionAr', data?.seoDetails?.description?.ar)
                    setValue('metaKeywordsEn', data?.seoDetails?.keywords?.en)
                    setValue('metaKeywordsAr', data?.seoDetails?.keywords?.ar)
                    setValue('metaCanonicalUrl', data?.seoDetails?.canonical?.en)
                    setValue('metaCanonicalUrlAr', data?.seoDetails?.canonical?.ar)
                    setOgImageFile([data?.seoDetails?.ogImage])
                    setIsUpdate(true)
                }
            }
        })
    }, [formSubmitted])

    const {
        handleSubmit,
        control,
        setValue,
        formState: { errors },
    } = useForm<any>()

    const onSubmit: SubmitHandler<any> = (data) => {
        const values = {
            title: {
                en: data.titleEn,
                ar: data.titleAr,
            },
            content: {
                en: data.contentEn,
                ar: data.contentAr,
            },
            seoDetails: {
                title: {
                    en: data.metaTitleEn,
                    ar: data.metaTitleAr
                },
                description: {
                    en: data.metaDescriptionEn,
                    ar: data.metaDescriptionAr
                },
                keywords: {
                    en: data.metaKeywordsEn,
                    ar: data.metaKeywordsAr
                },
                canonical: {
                    en: data.metaCanonicalUrl,
                    ar: data.metaCanonicalUrlAr
                },
                ogImage: ogImageFile[0]
            }
        }

        if (isUpdate) {
            api.put(endpoints.shippingPolicy, values)
                .then((res) => {
                    if (res.status == 200) {
                        toast.push(
                            <Notification
                                type="success"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                        setFormSubmitted(true)
                    }
                })
                .catch((err) => {
                    toast.push(
                        <Notification
                            type="warning"
                            title={err.response.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                })
        } else {
            api.post(endpoints.shippingPolicy, values)
                .then((res) => {
                    if (res.status == 200) {
                        toast.push(
                            <Notification
                                type="success"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                        setFormSubmitted(true)
                    }
                })
                .catch((err) => {
                    toast.push(
                        <Notification
                            type="warning"
                            title={err.response.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                })
        }
    }
    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <h4>Shipping Policy</h4>
            <Breadcrumb items={breadcrumbItems} />
            <h5>Title</h5>
            <div className="grid grid-cols-2 gap-4 mt-2">
                <FormItem label="English">
                    <Controller
                        name="titleEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is Required' }}
                        render={({ field }) => <Input {...field} />}
                    />
                    {errors.titleEn && (
                        <small className="text-red-600 py-3">
                            {errors.titleEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Arabic">
                    <Controller
                        name="titleAr"
                        control={control}
                        defaultValue=""
                        // rules={{required: "Field is Required"}}
                        render={({ field }) => <Input {...field} />}
                    />
                    {/* {errors.titleAr && <small className="text-red-600 py-3">{errors.titleAr.message as string}</small>} */}
                </FormItem>
            </div>
            <p className="mt-2 mb-2">English</p>
            <Controller
                name="contentEn"
                control={control}
                defaultValue=""
                rules={{
                    required: 'Field is Required',
                    minLength: { value: 12, message: 'Field is Required' },
                }}
                render={({ field }) => <RichTextEditor {...field} />}
            />
            {errors.contentEn && (
                <small className="text-red-600 py-3">
                    {errors.contentEn.message as string}
                </small>
            )}

            <p className=" mt-2 mb-2">Arabic</p>
            <Controller
                name="contentAr"
                control={control}
                defaultValue=""
                rules={
                    {
                        // required: "Field is Required",
                        // minLength: { value: 12, message: "Field is Required" },
                    }
                }
                render={({ field }) => <RichTextEditor {...field} />}
            />
            {/* {errors.contentEn && <small className="text-red-600 py-3">{errors?.contentAr?.message as string}</small>} */}

            <h5 className='mt-4'>Seo Section</h5>
            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Title English">
                    <Controller
                        name="metaTitleEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaTitleEn && (
                        <small className="text-red-600 py-3">
                            {errors.metaTitleEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Meta Title Arabic">
                    <Controller
                        name="metaTitleAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaTitleAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaTitleAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Description English">
                    <Controller
                        name="metaDescriptionEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaDescriptionEn && (
                        <small className="text-red-600 py-3">
                            {errors.metaDescriptionEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Meta Description Arabic">
                    <Controller
                        name="metaDescriptionAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaDescriptionAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaDescriptionAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Keywords English">
                    <Controller
                        name="metaKeywordsEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaKeywordsEn && (
                        <small className="text-red-600 py-3">
                            {errors.metaKeywordsEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Meta Keywords Arabic">
                    <Controller
                        name="metaKeywordsAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaKeywordsAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaKeywordsAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Canonical URL English">
                    <Controller
                        name="metaCanonicalUrl"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaCanonicalUrl && (
                        <small className="text-red-600 py-3">
                            {errors.metaCanonicalUrl.message as string}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Meta Canonical URL Arabic">
                    <Controller
                        name="metaCanonicalUrlAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaCanonicalUrlAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaCanonicalUrlAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <FormItem label="Meta OG Image">
                <Dropzone ratio={[1074, 616]} previews={ogImageFile} onFileUrlChange={(e) => { setOgImageFile([e]) }} />
            </FormItem>

            <Button className="float-right mt-4" variant="solid" type="submit">
                Submit
            </Button>
        </form>
    )
}
