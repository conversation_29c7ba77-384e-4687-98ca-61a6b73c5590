import { Dialog } from '@/components/ui'

interface LensDetailsModalProps {
    isOpen: boolean
    onClose: () => void
    lensDetails: any
}

const LensDetailsModal = ({
    isOpen,
    onClose,
    lensDetails,
}: LensDetailsModalProps) => {
    return (
        <Dialog
            className={'overflow-hidden'}
            width={600}
            height={400}
            isOpen={isOpen}
            onClose={onClose}
        >
            <div className="p-4">
                <h2 className="text-xl font-bold mb-4">Lens Details</h2>
                <div className="grid grid-cols-2 gap-4">
                    <div className="flex flex-col gap-1">
                        <p>Vision: {lensDetails?.vision || 'N/A'}</p>
                        <p>
                            Left Sph:{' '}
                            {lensDetails?.prescription?.leftSph || 'N/A'}
                        </p>
                        <p>
                            Left Cyl:{' '}
                            {lensDetails?.prescription?.leftCyl || 'N/A'}
                        </p>
                        <p>
                            Left Axis:{' '}
                            {lensDetails?.prescription?.leftAxis || 'N/A'}
                        </p>
                        <p>
                            Right Sph:{' '}
                            {lensDetails?.prescription?.rightSph || 'N/A'}
                        </p>
                        <p>
                            Right Cyl:{' '}
                            {lensDetails?.prescription?.rightCyl || 'N/A'}
                        </p>
                        <p>
                            Right Axis:{' '}
                            {lensDetails?.prescription?.rightAxis || 'N/A'}
                        </p>
                    </div>
                    <div className="flex flex-col gap-1">
                        <p>PD: {lensDetails?.prescription?.pd || 'N/A'}</p>
                        <p>Lens Type: {lensDetails?.lensType || 'N/A'}</p>
                        <p>Brand: {lensDetails?.brand?.name || 'N/A'}</p>
                        <p>Photocromic: {lensDetails?.photocromic || 'N/A'}</p>
                        <p>Index: {lensDetails?.index?.name || 'N/A'}</p>
                        <p>Coating: {lensDetails?.coating?.name || 'N/A'}</p>
                    </div>
                </div>
            </div>
        </Dialog>
    )
}

export default LensDetailsModal
