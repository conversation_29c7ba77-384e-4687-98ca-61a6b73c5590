import { Button, FormItem, Select, toast } from "@/components/ui";
import endpoints from "@/endpoints";
import api from "@/services/api.interceptor";
import { Controller, useForm } from "react-hook-form";
import { AiOutlineSave } from "react-icons/ai";
import Notification from '@/components/ui/Notification'
import { useNavigate, useParams } from "react-router-dom";
import { useEffect } from "react";
import Breadcrumb from "@/views/modals/BreadCrumb";

/* eslint-disable */
export default function ManageAgeGroup() {
  const navigate = useNavigate()
  const params = useParams()

  const breadcrumbItems = [
    { title: 'Age Group', url: '/age-group' },
    { title: 'Manage Age Group', url: '' },
  ];

  const options: any = [
    { value: 'true', label: 'True' },
    { value: 'false', label: 'False' },
  ]

  const {
    handleSubmit,
    control,
    setValue,
    formState: { errors },
  } = useForm<any>();

  useEffect(() => {
    if (params.id) {
      api.get(endpoints.ageGroupDetail + params.id).then((res) => {
        if (res.status == 200) {
          setValue('name', res.data.result.name)
          setValue('isActive', { value: res.data.result.isActive, label: res.data.result.isActive ? 'True' : 'False' })
        }
      }).catch((err) => {
        console.log(err);
      })
    }
  }, [params.id])


  const onSubmit = (data: any) => {
    const values: any = {
      name: data?.name,
      isActive: data?.isActive?.value
    }

    if (params.id) {
      values.refid = params.id

      api.post(endpoints.updateAgeGroup, values).then((res) => {
        if (res.status == 200) {
          toast.push(
            <Notification type="success" title={res.data.message} />, {
            placement: 'top-center',
          })
          navigate('/catalog/age-group');
        }
      }).catch((err) => {
        toast.push(
          <Notification type="warning" title={err.response.data.message} />, {
          placement: 'top-center',
        })
      })
    }
    else {
      api.post(endpoints.createAgeGroup, values).then((res) => {
        if (res.status == 200) {
          toast.push(
            <Notification type="success" title={res.data.message} />, {
            placement: 'top-center',
          })
          navigate('/catalog/age-group');
        }
      }).catch((err) => {
        toast.push(
          <Notification type="warning" title={err.response.data.message} />, {
          placement: 'top-center',
        })
      })
    };
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <h3 className="mb-2">{params.id ? 'Edit' : 'Add '} Age Group</h3>
      <Breadcrumb items={breadcrumbItems} />
      <div className="grid grid-cols-2 gap-4 mt-4">
        <FormItem label="Name">
          <Controller
            name="name"
            control={control}
            defaultValue=""
            rules={{ required: 'Name is required' }}
            render={({ field }) => (
              <input
                type="text"
                className={`${errors.name ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                {...field}
              />
            )}
          />
          {errors.name && <small className="text-red-600 py-3">{errors.name.message as string}</small>}
        </FormItem>


        <FormItem label="Is Active?">
          <Controller
            name="isActive"
            control={control}
            defaultValue={options[0]}
            render={({ field }) => (
              <Select options={options} {...field} />
            )}
          />
        </FormItem>
      </div>

      <Button className='float-right mt-4' variant="solid" type="submit" icon={<AiOutlineSave />}>
        Save
      </Button>

    </form>
  )
}
