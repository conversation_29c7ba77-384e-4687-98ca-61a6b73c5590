import { FormItem, Input } from '@/components/ui'
import React from 'react'
import { Controller } from 'react-hook-form'


const popup: { label: string, value: string, db: string }[] = [
    {
        label: "Logout Text",
        value: "LogoutTxt",
        db: "logoutTxt",
    },
    {
        label: "Please Login",
        value: "PleaseLogin",
        db: "pleaseLogin",
    },
    {
        label: "Logout Button",
        value: "LogoutBtn",
        db: "logoutBtn",
    },
    {
        label: "Select Account Type",
        value: "SelectAccountType",
        db: "selectAccountType",
    },
    {
        label: "Otp Sent Success",
        value: "OtpSentSuccess",
        db: "otpSentSuccess",
    },
    {
        label: "Something Went Wrong",
        value: "SomethingWentWrong",
        db: "somethingWentWrong",
    },
    {
        label: "Invalid OTP",
        value: "InvalidOTP",
        db: "invalidOTP",
    },
    {
        label: "Welcome Back",
        value: "WelcomeBack",
        db: "welcomeBack",
    },
    {
        label: "Logging Out",
        value: "LoggingOut",
        db: "loggingOut",
    },
    {
        label: "Logout Success",
        value: "LogoutSuccess",
        db: "logoutSuccess",
    },
    {
        label: "Cancel",
        value: "Cancel",
        db: "cancel",
    },
    {
        label: "Add to Wishlist Popup",
        value: "WishlistAdd",
        db: "wishlistAdd",
    },
    {
        label: "Remove from Wishlist Popup",
        value: "WishlistRemove",
        db: "wishlistRemove",
    },
    {
        label: "Add to cart Popup",
        value: "CartAdd",
        db: "cartAdd",
    },
    {
        label: "Remove from cart Popup",
        value: "CartRemove",
        db: "cartRemove",
    },
    {
        label: "Update cart Popup",
        value: "CartUpdate",
        db: "cartUpdate",
    },
    {
        label: "Basket Subtotal",
        value: "BasketSubtotal",
        db: "basketSubtotal",
    },
    {
        label: "View Cart",
        value: "ViewCart",
        db: "viewCart",
    },
    {
        label: "File Upload Success",
        value: "FileUploadSuccess",
        db: "fileUploadSuccess",
    },
]

const checkout: { label: string, value: string, db: string }[] = [
    {
        label: "Checkout Title",
        value: "CheckoutTitle",
        db: "checkoutTitle",
    },
    {
        label: "Checkout Description",
        value: "CheckoutDescription",
        db: "checkoutDescription",
    },
    {
        label: "Continue Shopping",
        value: "ContinueShopping",
        db: "continueShopping",
    },
    {
        label: "View Order",
        value: "ViewOrder",
        db: "viewOrder",
    },
    {
        label: "View Orders",
        value: "ViewOrders",
        db: "viewOrders",
    },
]

const newsLetter: { label: string, value: string, db: string }[] = [
    {
        label: "News Letter Subscribed Title",
        value: "NewsLetterSubscribedTitle",
        db: "newsLetterSubscribedTitle"
    },
    {
        label: "News Letter Subscribed Description",
        value: "NewsLetterSubscribedDescription",
        db: "newsLetterSubscribedDescription"
    },
    {
        label: "News Letter Subscribed Button",
        value: "NewsLetterSubscribedButton",
        db: "newsLetterSubscribedButton"
    },

]

const enquiry: { label: string, value: string, db: string }[] = [
    {
        label: "Enquiry Submit Title",
        value: "EnquirySubmitTitle",
        db: "enquirySubmitTitle"
    },
    {
        label: "Enquiry Submit Description",
        value: "EnquirySubmitDescription",
        db: "enquirySubmitDescription"
    },
    {
        label: "Enquiry Submit Button",
        value: "EnquirySubmitButton",
        db: "enquirySubmitButton"
    },

]


export const popups: { label: string, value: string, db: string }[] = [
    ...popup,
    ...checkout,
    ...newsLetter,
    ...enquiry
]

function PopupTab({ control, errors }: any) {
    return (
        <>
            <h3>Popups</h3>
            {popup.map((item) => (
                <Forms key={item.value} control={control} errors={errors} item={item} />
            ))}
            <h3>Checkout</h3>
            {checkout.map((item) => (
                <Forms key={item.value} control={control} errors={errors} item={item} />
            ))}
            <h3>News Letter</h3>
            {newsLetter.map((item) => (
                <Forms key={item.value} control={control} errors={errors} item={item} />
            ))}
            <h3>Enquiry Submitted</h3>
            {enquiry.map((item) => (
                <Forms key={item.value} control={control} errors={errors} item={item} />
            ))}
        </>
    )
}

function Forms({ item, control, errors }: any) {
    return (
        <div className="mt-2">
            <div className="grid grid-cols-2 gap-4">
                <FormItem label={`${item.label} English`}>
                    <Controller
                        control={control}
                        name={`popup${item.value}En`}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {errors[`popup${item.value}En`] && (
                        <small className="text-red-600 py-3">
                            {' '}
                            {errors[`popup${item.value}En`].message as string}{' '}
                        </small>
                    )}
                </FormItem>
                <FormItem label={`${item.label} Arabic`}>
                    <Controller
                        control={control}
                        name={`popup${item.value}Ar`}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => <Input dir='rtl' type="text" {...field} />}
                    />
                    {errors[`popup${item.value}Ar`] && (
                        <small className="text-red-600 py-3">
                            {' '}
                            {errors[`popup${item.value}Ar`].message as string}{' '}
                        </small>
                    )}
                </FormItem>
            </div>
        </div>
    )
}

export default PopupTab