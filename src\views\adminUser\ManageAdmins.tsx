/*eslint-disable */
import { Button, FormItem, Select, Upload, toast } from '@/components/ui'
import endpoints from '@/endpoints'
import api from '@/services/api.interceptor'
import { Controller, useForm } from 'react-hook-form'
import { AiOutlineSave } from 'react-icons/ai'
import Notification from '@/components/ui/Notification'
import { useNavigate, useParams } from 'react-router-dom'
import { useEffect, useState } from 'react'
import Breadcrumb from '../modals/BreadCrumb'
import { HiOutlineEye, HiOutlineEyeOff } from 'react-icons/hi'
import { countryList } from '@/constants/countries.constant'

const assetUrl = import.meta.env.VITE_ASSET_URL

export default function ManageAdmins() {
    const navigate = useNavigate()
    const params = useParams()
    const [imageFile, setimageFile] = useState<any>([])
    const [roleOptions, setRoleOptions] = useState<any>([])
    const [showPassword, setShowPassword] = useState(false)
    const [countryOptions, setCountryOptions] = useState<any>(countryList)

    const breadcrumbItems = [
        { title: 'Admin Users', url: '/admins' },
        {
            title: params?.id ? 'Update Admin User' : 'Create Admin User',
            url: '',
        },
    ]

    const options = [
        { value: 'true', label: 'True' },
        { value: 'false', label: 'False' },
    ]

    const getRoles = () => {
        api.post(endpoints.getRole, { isActive: true })
            .then((res) => {
                if (res.status == 200) {
                    const roles = res.data.result || []
                    const newRoleOptions = roles.map((role: any) => ({
                        value: role._id,
                        label: role.name,
                    }))
                    setRoleOptions(newRoleOptions)
                }
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }

    const handleImageUpload = (files: any) => {
        setimageFile(files)
    }

    const {
        handleSubmit,
        control,
        setValue,
        formState: { errors },
    } = useForm<any>()

    useEffect(() => {
        if (params.id)
            api.get(endpoints.adminDetail + params.id)
                .then((res) => {
                    if (res?.status == 200) {
                        console.log(res.data)
                        setValue('name', res.data.result.name)
                        setValue('email', res.data.result.email)
                        setValue('countryCode', {
                            value: res.data.result?.countryCode,
                            label: res.data.result.countryCodeLabel,
                        })
                        setValue('mobile', res.data.result.mobile)
                        setValue('role', {
                            value: res.data.result?.role._id,
                            label: res.data.result.role?.name,
                        })
                        setValue('isActive', {
                            value: res.data.result?.isActive,
                            label: res.data.result.isActive ? 'True' : 'False',
                        })
                        if (res?.data?.result?.image)
                            setimageFile([assetUrl + res?.data?.result?.image])
                    }
                })
                .catch((error) => {
                    console.error('Error fetching data: ', error)
                })

        getRoles()
    }, [])

    const onSubmit = (data: any) => {
        console.log(data)

        const formdata = new FormData()
        formdata.append('name', data.name)
        formdata.append('email', data.email)
        formdata.append('countryCode', data.countryCode?.value)
        formdata.append('countryCodeLabel', data?.countryCode?.label)
        formdata.append('mobile', data.mobile)
        formdata.append('role', data?.role?.value)
        if (!params?.id) formdata.append('password', data.password)
        if (imageFile.length > 0) formdata.append('image', imageFile[0])
        if (params?.id) formdata.append('refid', params?.id)
        if (params?.id) formdata.append('isActive', data?.isActive?.value)
        if (params.id) {
            api.post(endpoints.updateAdmin, formdata)
                .then((res) => {
                    if (res.status == 200) {
                        if (res?.data?.errorCode == 0) {
                            toast.push(
                                <Notification
                                    type="success"
                                    title={res.data.message}
                                />,
                                {
                                    placement: 'top-center',
                                }
                            )
                            navigate('/admins')
                        }
                    }
                })
                .catch((err) => {
                    toast.push(
                        <Notification
                            type="warning"
                            title={err.response.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                })
        } else {
            api.post(endpoints.creatAdmin, formdata)
                .then((res) => {
                    if (res.status == 200) {
                        toast.push(
                            <Notification
                                type="success"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                        navigate('/admins')
                    }
                })
                .catch((err) => {
                    toast.push(
                        <Notification
                            type="warning"
                            title={err.response.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                })
        }
    }

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <h3 className="mb-2">{params.id ? 'Edit' : 'Add'} Admin User</h3>
            <Breadcrumb items={breadcrumbItems} />
            <div className="grid grid-cols-3 gap-4 mt-4">
                <FormItem label="Name">
                    <Controller
                        name="name"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Name is required' }}
                        render={({ field }) => (
                            <input
                                type="text"
                                className={`${
                                    errors.name
                                        ? ' input input-md h-11 input-invalid'
                                        : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                }`}
                                {...field}
                            />
                        )}
                    />
                    {errors.name && (
                        <small className="text-red-600 py-3">
                            {errors.name.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Email">
                    <Controller
                        name="email"
                        control={control}
                        defaultValue=""
                        rules={{
                            required: 'Email is required',
                            pattern: {
                                value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                                message: 'Invalid email address',
                            },
                        }}
                        render={({ field }) => (
                            <input
                                type="text"
                                className={`${
                                    errors.email
                                        ? ' input input-md h-11 input-invalid'
                                        : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                }`}
                                {...field}
                            />
                        )}
                    />
                    {errors.email && (
                        <small className="text-red-600 py-3">
                            {errors.email.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Role">
                    <Controller
                        name="role"
                        control={control}
                        defaultValue=""
                        render={({ field }) => (
                            <Select options={roleOptions} {...field} />
                        )}
                    />
                    {errors.email && (
                        <small className="text-red-600 py-3">
                            {errors.email.message as string}
                        </small>
                    )}
                </FormItem>
            </div>
            <div className="grid grid-cols-3 gap-4">
                <FormItem label="Country Code">
                    <Controller
                        name="countryCode"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Country Code is required' }}
                        render={({ field }) => (
                            <Select options={countryOptions} {...field} />
                        )}
                    />
                    {errors.countryCode && (
                        <small className="text-red-600 py-3">
                            {errors.countryCode.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Mobile">
                    <Controller
                        name="mobile"
                        control={control}
                        defaultValue=""
                        rules={{
                            required: 'Mobile is required',
                            pattern: {
                                value: /^[0-9]*$/,
                                message: 'Invalid Mobile number',
                            },
                        }}
                        render={({ field }) => (
                            <input
                                type="text"
                                className={`${
                                    errors.mobile
                                        ? ' input input-md h-11 input-invalid'
                                        : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                }`}
                                {...field}
                            />
                        )}
                    />
                    {errors.mobile && (
                        <small className="text-red-600 py-3">
                            {errors.mobile.message as string}
                        </small>
                    )}
                </FormItem>
                {params.id && (
                    <FormItem label="Status">
                        <Controller
                            name="isActive"
                            control={control}
                            defaultValue=""
                            render={({ field }) => (
                                <Select options={options} {...field} />
                            )}
                        />
                    </FormItem>
                )}
            </div>
            <div className="grid grid-cols-2 gap-4">
                {!params.id && (
                    <FormItem label="Password">
                        <Controller
                            name="password"
                            control={control}
                            defaultValue=""
                            rules={{ required: 'Password is required' }}
                            render={({ field }) => (
                                <div className="relative">
                                    <input
                                        type={
                                            showPassword ? 'text' : 'password'
                                        }
                                        className={`${
                                            errors.password
                                                ? ' input input-md h-11 input-invalid'
                                                : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                        } pr-10`}
                                        {...field}
                                    />
                                    <button
                                        type="button"
                                        className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500"
                                        onClick={() =>
                                            setShowPassword(!showPassword)
                                        }
                                    >
                                        {showPassword ? (
                                            <HiOutlineEye size={20} />
                                        ) : (
                                            <HiOutlineEyeOff size={20} />
                                        )}
                                    </button>
                                </div>
                            )}
                        />
                        {errors.password && (
                            <small className="text-red-600 py-3">
                                {errors.password.message as string}
                            </small>
                        )}
                    </FormItem>
                    // <FormItem label="Password">
                    //     <Controller
                    //         name="password"
                    //         control={control}
                    //         defaultValue=""
                    //         rules={{ required: 'Password is required' }}
                    //         render={({ field }) => (
                    //             <input
                    //                 type="password"
                    //                 className={`${errors.password
                    //                     ? ' input input-md h-11 input-invalid'
                    //                     : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                    //                     }`}
                    //                 {...field}
                    //             />
                    //         )}
                    //     />
                    //     {errors.password && (
                    //         <small className="text-red-600 py-3">
                    //             {errors.password.message as string}
                    //         </small>
                    //     )}
                    // </FormItem>
                )}
            </div>
            <div>
                <FormItem label="Image">
                    <Upload
                        draggable
                        uploadLimit={1}
                        accept="image/*"
                        fileList={imageFile}
                        onChange={handleImageUpload}
                    />
                </FormItem>
            </div>

            <Button
                className="float-right mt-4"
                variant="solid"
                type="submit"
                icon={<AiOutlineSave />}
            >
                {params.id ? 'Update' : 'Save'}
            </Button>
        </form>
    )
}
