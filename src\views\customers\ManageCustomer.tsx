import { Button, FormItem, toast, Select, Input, InputGroup, Dialog } from "@/components/ui";
import endpoints from "@/endpoints";
import api from "@/services/api.interceptor";
import { Controller, useForm } from "react-hook-form";
import { AiOutlineSave } from "react-icons/ai";
import Notification from '@/components/ui/Notification'
import { useNavigate, useParams } from "react-router-dom";
import { useEffect, useState } from "react";
import Breadcrumb from "../modals/BreadCrumb";
import { FaRegEdit } from "react-icons/fa";
import { MdDelete } from "react-icons/md";

/* eslint-disable */
export default function ManageCustomer() {
  const [showInsuranceId, setShowInsuranceId] = useState(false);
  const [showAddressDialog, setShowAddressDialog] = useState(false)
  const [currentAddress, setCurrentAddress] = useState<any>(null)
  const [addresses, setAddresses] = useState<any>([]);
  const navigate = useNavigate()
  const params = useParams()

  const breadcrumbItems = [
    { title: 'Customers', url: '/customers' },
    { title: 'Manage Customer', url: '' },
  ];

  const emiratesOptions: any = [
    { value: "Dubai", label: "Dubai" },
    { value: "Abu Dhabi", label: "Abu Dhabi" },
    { value: "Fujairah", label: "Fujairah" },
    { value: "Ras Al Khaimah", label: "Ras Al Khaimah" },
    { value: "Sharjah", label: "Sharjah" },
    { value: "Ajman", label: "Ajman" },
    { value: "Umm Al Quwain", label: "Umm Al Quwain" },
  ]

  const options: any = [
    { value: "true", label: "True" },
    { value: "false", label: "False" },
  ]

  const countryCodes = [
    { value: "+91", label: "+91" },
    { value: "+971", label: "+971" },
  ]

  const {
    handleSubmit,
    control,
    setValue,
    formState: { errors },
  } = useForm<any>();

  useEffect(() => {
    if (params.id) {
      api.get(endpoints.customerDetail + params.id).then((res) => {
        if (res.status == 200) {
          setAddresses(res.data?.result?.addresses)
          setValue('name', res.data.result?.customerDetails?.name)
          setValue('email', res.data.result?.customerDetails?.email)
          setValue('countryCode', { value: res.data.result?.customerDetails?.countryCode, label: res.data.result?.customerDetails?.countryCode })
          setValue('mobile', res.data.result?.customerDetails?.mobile)
          setValue('emirates', { value: res.data.result?.customerDetails?.emirates, label: res.data.result?.customerDetails?.emirates })
          setValue('haveInsurance', { value: res?.data?.result?.customerDetails?.isInsurance, label: res?.data?.result?.customerDetails?.isInsurance == true ? "True" : "False" })
          setShowInsuranceId(res?.data?.result?.customerDetails?.isInsurance);
          if (res?.data?.result?.customerDetails?.isInsurance) setValue('insuranceId', res?.data?.result?.customerDetails?.insuranceId);
          setValue('isActive', { value: res.data.result?.customerDetails?.isActive, label: res.data.result?.customerDetails?.isActive ? "True" : "False" })
        }
      }).catch((err) => {
        console.log(err);
      })
    }
  }, [params.id])

  const {
    handleSubmit: handleAddressSubmit,
    control: addressControl,
    setValue: setAddressValue,
    formState: { errors: addressErrors },
  } = useForm<any>();

  const showAddress = (address: any) => {
    setAddressValue('name', address?.name)
    setAddressValue('countryCode', address?.countryCode)
    setAddressValue('countryCode', { value: address?.countryCode, label: address?.countryCode })
    setAddressValue('mobile', address?.mobile)
    setAddressValue('country', address?.country)
    setAddressValue('emirates', address?.emirates)
    setAddressValue('street', address?.street)
    setAddressValue('suiteUnit', address?.suiteUnit)
    setAddressValue('city', address?.city)
    setAddressValue('postalCode', address?.postalCode)
    setAddressValue('type', address?.type)
    setAddressValue('isDefault', { value: address?.isDefault, label: address?.isDefault? "True": "False" })
    setShowAddressDialog(true)
  }

  const onAddressSubmit = (data: any) => {

  }

  const onSubmit = (data: any) => {
    console.log(data);
    const values: any = {
      name: data?.name,
      email: data?.email,
      countryCode: data?.countryCode ? data?.countryCode.value : countryCodes[0].value,
      mobile: data?.mobile,
      emirates: data?.emirates.value,
      isInsurance: data?.haveInsurance?.value,
      insuranceId: data?.insuranceId,
    }
    if (params.id) {

      values.refid = params.id
      values.isActive = data?.isActive?.value;
      data.refid = params.id
      api.post(endpoints.updateCustomer, values).then((res) => {
        if (res.status == 200) {
          if (res?.data?.errorCode == 0) {
            toast.push(
              <Notification type="success" title={res.data.message} />, {
              placement: 'top-center',
            })
            navigate('/customers');
          } else {
            toast.push(
              <Notification type="warning" title={res.data.message} />, {
              placement: 'top-center',
            })
          }
        }
      })
    }
    else {
      api.post(endpoints.createCustomer, values).then((res) => {
        if (res.status == 200) {
          if (res?.data?.errorCode == 0) {
            toast.push(
              <Notification type="success" title={res.data.message} />, {
              placement: 'top-center',
            })
            navigate('/customers');
          } else {
            toast.push(
              <Notification type="warning" title={res.data.message} />, {
              placement: 'top-center',
            }
            )
          }
        }
      }).catch((err) => {
        console.log(err);
      })
    };
  }

  const handleHaveInsuranceChange = (value: any) => {
    setShowInsuranceId(value === "true");
  };

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)}>
        <h3 className="mb-2">{params.id ? 'Edit' : 'Add '} Customer</h3>
        <Breadcrumb items={breadcrumbItems} />
        <div className="grid grid-cols-2 gap-4 mt-4">
          <FormItem label="Name">
            <Controller
              name="name"
              control={control}
              defaultValue=""
              rules={{ required: 'Name is required' }}
              render={({ field }) => (
                <input
                  type="text"
                  className={`${errors.name ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                  {...field}
                />
              )}
            />
            {errors.name && <small className="text-red-600 py-3">{errors.name.message as string}</small>}
          </FormItem>
          <FormItem label="Email">
            <Controller
              name="email"
              control={control}
              defaultValue=""
              rules={{ required: 'Email is required', pattern: { value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i, message: 'Invalid email address' } }}
              render={({ field }) => (
                <input
                  type="text"
                  className={`${errors.email ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                  {...field}
                />
              )}
            />
            {errors.email && <small className="text-red-600 py-3">{errors.email.message as string}</small>}
          </FormItem>
        </div>

        <div className="grid grid-cols-3 gap-4">
          <FormItem label="Mobile">
            <Controller
              name="mobile"
              control={control}
              defaultValue=""
              rules={{ required: 'Mobile is required' }}
              render={({ field }) => (
                <InputGroup>
                  <Controller
                    control={control}
                    name="countryCode"
                    render={({ field }) => (
                      <Select
                        className="w-32"
                        defaultValue={countryCodes[0]}
                        placeholder="Code"
                        options={countryCodes}
                        isSearchable={false}
                        {...field}
                      />
                    )}
                  />
                  <Input
                    type="number"
                    className={`${errors.mobile ? 'input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                    {...field}
                  />
                </InputGroup>
              )}
            />
            {errors.mobile && (
              <small className="text-red-600 py-3 ms-24">
                {errors.mobile.message as string}
              </small>
            )}
          </FormItem>

          <FormItem label="Emirates">
            <Controller
              name="emirates"
              defaultValue=""
              control={control}
              render={({ field }) => (
                <Select
                  defaultValue=""
                  options={emiratesOptions}
                  {...field}
                />
              )}
            />
          </FormItem>
          <FormItem label="Have Insurance? ">
            <Controller
              name="haveInsurance"
              defaultValue=""
              control={control}
              render={({ field }) => (
                <Select
                  defaultValue=""
                  options={options}
                  {...field}
                  onChange={(value: any) => {
                    field.onChange(value);
                    handleHaveInsuranceChange(value?.value);
                  }}
                />
              )}
            />
          </FormItem>
        </div>
        <div className="grid grid-cols-2 gap-4">
          {showInsuranceId && (
            <FormItem label="Insurance Id">
              <Controller
                name="insuranceId"
                control={control}
                defaultValue=""
                render={({ field }) => (
                  <input
                    type="text"
                    className="input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600"
                    {...field}
                  />
                )}
              />
            </FormItem>
          )}
          {params.id && (
            <FormItem label="isActive? ">
              <Controller
                name="isActive"
                control={control}
                defaultValue=""
                render={({ field }) => (
                  <Select
                    defaultValue={""}
                    {...field}
                    options={options}
                  />
                )}
              />
            </FormItem>
          )}
        </div>
        <Button className='float-right mt-4' variant="solid" type="submit" icon={<AiOutlineSave />}>
          Save
        </Button>


      </form>
      {addresses.length > 0 && <div className="">
        <h4>Addresses</h4>
        <div className="grid grid-cols-2 gap-2 mt-4">
          {addresses?.map((address: any) => (
            <div className="p-4 flex gap-1 bg-slate-200 shadow-sm rounded-md">
              <div className="flex flex-col gap-1 flex-1">
                <p>{address?.name}, {address?.countryCode + '' + address?.mobile}</p>
                <p>{address?.street}, {address?.suiteUnit}, {address?.postalCode}</p>
                <p>{address?.city}, {address?.emirates}, {address?.country}</p>
              </div>
              {/* <div className="flex gap-2 items-start">
                <button onClick={() => showAddress(address)}>
                  <FaRegEdit size={20} color="blue" />
                </button>
                <button>
                  <MdDelete size={20} color="red" />
                </button>
              </div> */}
            </div>
          ))}
        </div>
      </div>}
      {showAddressDialog && (
        <Dialog width={1000} height={550} isOpen={showAddressDialog} onClose={() => setShowAddressDialog(false)}>
          <form onSubmit={handleSubmit(onAddressSubmit)}>
          <div className="max-h-full overflow-y-auto">
            <h3 className="">Edit Address</h3>
            <div className="grid grid-cols-2 gap-2 p-4">
              <FormItem label="Name">
                <Controller
                  name="name"
                  control={addressControl}
                  defaultValue=""
                  rules={{ required: 'Name is required' }}
                  render={({ field }) => (
                    <input
                      type="text"
                      className={`${addressErrors.name ? 'input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                      {...field}
                    />
                  )}
                />
                {addressErrors.name && <small className="text-red-600 py-3">{addressErrors.name.message as string}</small>}
              </FormItem>

              <FormItem label="Mobile">
                <Controller
                  name="mobile"
                  control={addressControl}
                  defaultValue=""
                  rules={{ required: 'Mobile is required' }}
                  render={({ field }) => (
                    <InputGroup>
                      <Controller
                        control={addressControl}
                        name="countryCode"
                        render={({ field }) => (
                          <Select
                            className="w-32"
                            defaultValue={countryCodes[0]}
                            placeholder="Code"
                            options={countryCodes}
                            isSearchable={false}
                            {...field}
                          />
                        )}
                      />
                      <Input
                        type="number"
                        className={`${addressErrors.mobile ? 'input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                        {...field}
                      />
                    </InputGroup>
                  )}
                />
                {addressErrors.mobile && (
                  <small className="text-red-600 py-3 ms-24">
                    {addressErrors.mobile.message as string}
                  </small>
                )}
              </FormItem>

              <FormItem label="Country">
                <Controller
                  name="country"
                  control={addressControl}
                  defaultValue=""
                  rules={{ required: 'Country is required' }}
                  render={({ field }) => (
                    <input
                      type="text"
                      className={`${addressErrors.country ? 'input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                      {...field}
                    />
                  )}
                />
                {addressErrors.country && <small className="text-red-600 py-3">{addressErrors.country.message as string}</small>}
              </FormItem>

              <FormItem label="Emirates">
                <Controller
                  name="emirates"
                  control={addressControl}
                  defaultValue=""
                  rules={{ required: 'Emirates is required' }}
                  render={({ field }) => (
                    <input
                      type="text"
                      className={`${addressErrors.emirates ? 'input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                      {...field}
                    />
                  )}
                />
                {addressErrors.emirates && <small className="text-red-600 py-3">{addressErrors.emirates.message as string}</small>}
              </FormItem>

              <FormItem label="Street">
                <Controller
                  name="street"
                  control={addressControl}
                  defaultValue=""
                  rules={{ required: 'Street is required' }}
                  render={({ field }) => (
                    <input
                      type="text"
                      className={`${addressErrors.street ? 'input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                      {...field}
                    />
                  )}
                />
                {addressErrors.street && <small className="text-red-600 py-3">{addressErrors.street.message as string}</small>}
              </FormItem>

              <FormItem label="Suite/Unit">
                <Controller
                  name="suiteUnit"
                  control={addressControl}
                  defaultValue=""
                  render={({ field }) => (
                    <input
                      type="text"
                      className={`${addressErrors.suiteUnit ? 'input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                      {...field}
                    />
                  )}
                />
                {addressErrors.suiteUnit && <small className="text-red-600 py-3">{addressErrors.suiteUnit.message as string}</small>}
              </FormItem>

              <FormItem label="City">
                <Controller
                  name="city"
                  control={addressControl}
                  defaultValue=""
                  rules={{ required: 'City is required' }}
                  render={({ field }) => (
                    <input
                      type="text"
                      className={`${addressErrors.city ? 'input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                      {...field}
                    />
                  )}
                />
                {addressErrors.city && <small className="text-red-600 py-3">{addressErrors.city.message as string}</small>}
              </FormItem>

              <FormItem label="Postal Code">
                <Controller
                  name="postalCode"
                  control={addressControl}
                  defaultValue=""
                  render={({ field }) => (
                    <input
                      type="text"
                      className={`${addressErrors.postalCode ? 'input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                      {...field}
                    />
                  )}
                />
                {addressErrors.postalCode && <small className="text-red-600 py-3">{addressErrors.postalCode.message as string}</small>}
              </FormItem>

              <FormItem label="Type">
                <Controller
                  name="type"
                  control={addressControl}
                  defaultValue=""
                  render={({ field }) => (
                    <input
                      type="text"
                      className={`${addressErrors.type ? 'input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                      {...field}
                    />
                  )}
                />
                {addressErrors.type && <small className="text-red-600 py-3">{addressErrors.type.message as string}</small>}
              </FormItem>

              <FormItem label="Is Default">
                <Controller

                  name="isDefault"
                  control={addressControl}
                  defaultValue={{ value: false, label: "False" }}
                  render={({ field }) => (
                    <Select
                      defaultValue={false}
                      options={[
                        { value: false, label: "False" },
                        { value: true, label: "True" },
                      ]}
                      isSearchable={false}
                      {...field}
                    />
                  )}
                />
                {addressErrors.isDefault && <small className="text-red-600 py-3">{addressErrors.isDefault.message as string}</small>}
              </FormItem>
            </div>
          </div>
          </form>
        </Dialog>
      )}
    </>
  )
}
