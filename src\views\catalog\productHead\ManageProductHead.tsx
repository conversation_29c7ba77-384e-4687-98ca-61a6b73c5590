/* eslint-disable */
import {
    <PERSON><PERSON>,
    FormItem,
    toast,
    Select,
    Upload,
    FormContainer,
    Switcher,
} from '@/components/ui'
import endpoints from '@/endpoints'
import api from '@/services/api.interceptor'
import { Controller, useFieldArray, useForm } from 'react-hook-form'
import { AiOutlineMinus, AiOutlinePlus, AiOutlineSave } from 'react-icons/ai'
import Notification from '@/components/ui/Notification'
import { Link, useLocation, useNavigate, useParams } from 'react-router-dom'
import { ChangeEvent, useEffect, useRef, useState } from 'react'
import { CiCircleRemove } from 'react-icons/ci'
import { HiPencilSquare } from 'react-icons/hi2'
import Radio from '@/components/ui/Radio/Radio'
import Breadcrumb from '@/views/modals/BreadCrumb'

const imageUrl = import.meta.env.VITE_ASSET_URL

const productTypes = [{ label: "Frame", value: "frame" }, { label: "Contact Lens", value: "contactLens" }, { label: "Accessory", value: "accessory" }]

const breadcrumbItems = [
    { title: 'Product Head', url: '/catalog/product-head' },
    { title: 'Manage Product Head', url: '' },
]

export function sortPower(a: any, b: any) {
    if (a?.name?.[0] === "+" && b?.name?.[0] === "-") return 1;
    if (a?.name?.[0] === "-" && b?.name?.[0] === "+") return -1;
    if (a?.name?.[0] === b?.name?.[0] && (a?.name?.[0] === "+" || a?.name?.[0] === "-")) {
        if (Number(a?.name?.slice(1)) > Number(b?.name?.slice(1))) {
            // if(a?.name?.[0] === "+") return -1;
            // if(a?.name?.[0] === "-") return 1;  
            return 1;
        } else {
            // if(a?.name?.[0] === "+") return 1;
            // if(a?.name?.[0] === "-") return -1;
            return -1;
        }
    }
    if (a?.name?.[0] !== "+" && a?.name?.[0] !== "-") return -1
    if (b?.name?.[0] !== "-" && b?.name?.[0] !== "+") return 1
}

export default function ManageProductHead() {

    const navigate = useNavigate()
    const location = useLocation()
    const params = useParams()

    const [productValue, setProductValue] = useState("")
    const [isLoading, setIsLoading] = useState(true)
    const [submitLoading, setSubmitLoading] = useState(false)
    const [productOptions, setProductOptions] = useState<any>([])
    const [allProducts, setAllProducts] = useState<any>([])
    const [headType, setHeadType] = useState("frame")

    useEffect(() => {
        const controller = new AbortController()
        const limit = productValue ? 60 : 30;
        setIsLoading(true)
        api.post(endpoints.products + `?page=1&limit=${limit}`, { isActive: true, keyword: productValue }, {
            signal: controller.signal
        }).then((res) => {
            if (res?.status == 200) {
                const products = res?.data?.result?.products || []
                setAllProducts(products)
                let newproductOptions: any = products.map((product: any) => ({
                    value: product._id,
                    label: product.name.en,
                }))
                console.log(newproductOptions)
                setProductOptions([...newproductOptions])
            }
            setIsLoading(false)
        }).catch((error) => {
            console.error('Error fetching data: ', error)
            // setIsLoading(false)
        })
        return () => {
            controller.abort()
        }
    }, [productValue])

    useEffect(() => {

        if (params.id) {
            api.get(endpoints.createProductHead + "/" + params.id)
                .then((res) => {
                    if (res?.status == 200) {
                        const data = res?.data?.result
                        setValue('name', data?.name)
                        setValue('sku', data?.sku)
                        let type: any = productTypes[0];
                        if (data?.type) {
                            type = productTypes.find((item: any) => item.value == data?.type)
                        }
                        setValue('type', type)
                        setHeadType(type?.value)
                        res?.data?.result?.products?.forEach((product: any, index: number) => {
                            productsUpdate(index, {
                                name: product?.name?.en,
                                sku: product?.sku,
                                value: product?._id,
                                isDefault: product?.isDefaultVariant,
                                refid: product?.refid

                            })
                        })
                    }
                }).catch((error) => {
                    if(error?.response?.status == 422) navigate('/access-denied');
                    console.error('Error fetching data: ', error)
                })

        }
        return () => {
            productsRemove()
        }
    }, [])

    const options: any = [
        { value: 'true', label: 'True' },
        { value: 'false', label: 'False' },
    ]

    // const productTypeOptions: any = [
    //   { value: "frame", label: "Frame" },
    //   { value: "contactLens", label: "Contact Lens" },
    // ]

    const {
        handleSubmit,
        control,
        setValue,
        getValues,
        watch,
        formState: { errors },
    } = useForm<any>()

    const {
        append: productsAppend,
        remove: productsRemove,
        fields: products,
        update: productsUpdate
    }: any = useFieldArray({
        control,
        name: "products"
    })

    const onSubmit = (value: any, e: any) => {
        if (products.length == 0) {
            toast.push(
                <Notification
                    type="warning"
                    title="At least one product is required"
                />,
                {
                    placement: 'top-center',
                }
            )
            return
        }
        setSubmitLoading(true)

        if (
            (params.id && params.type == 'Edit-Product')
        ) {

            api.put(endpoints.createProductHead + "/" + params.id, {
                ...value,
                type: value?.type?.value
            }).then((res) => {
                if (res.status == 200) {
                    if (res?.data?.errorCode == 0) {
                        toast.push(
                            <Notification
                                type="success"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )

                        if (e?.target?.dataset?.close == "true") {
                            navigate('/catalog/product-head')
                        }
                    }
                } else {
                    toast.push(
                        <Notification
                            type="warning"
                            title={res.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                }
            }).catch((err) => {
                console.log(err)
                toast.push(
                    <Notification
                        type="warning"
                        title={err?.response?.data?.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )
            }).finally(() => {
                setSubmitLoading(false)
            })
        } else if (params.type == 'Add-Product') {
            api.post(endpoints.createProductHead, {
                ...value,
                type: value?.type?.value
            })
                .then((res: any) => {
                    if (res.status == 200) {
                        if (res?.data?.errorCode == 0) {
                            toast.push(
                                <Notification
                                    type="success"
                                    title={res.data.message}
                                />,
                                {
                                    placement: 'top-center',
                                }
                            )
                            if (e?.target?.dataset?.close == "true") {
                                navigate('/catalog/product-head')
                            }
                        }
                    } else {
                        toast.push(
                            <Notification
                                type="warning"
                                title={res.data.message ?? res.response.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                    }
                })
                .catch((err) => {
                    toast.push(
                        <Notification
                            type="warning"
                            title={err.response.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                }).finally(() => {
                    setSubmitLoading(false)
                })
        }
    }

    const productSelect = async (option: any) => {
        const index = products.findIndex((item: any) => item?.value == option?.value)
        const product = allProducts.find((item: any) => item?._id == option?.value)
        let val = false
        if (products.length == 0) {
            val = true
        }
        if (index == -1) {
            try {
                const res = await api.post(endpoints.isVariantExist, {
                    productId: product?._id
                })
                const type = getValues('type')?.value
                if (type != res.data?.result?.type) {
                    toast.push(
                        <Notification
                            type="warning"
                            title="Product type is not matched"
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    return
                }
                productsAppend({
                    value: option?.value,
                    name: option?.label,
                    sku: product?.sku,
                    isDefault: val,
                    refid: product?.refid
                })
            } catch (error: any) {
                console.log(error)
                toast.push(
                    <Notification
                        type="warning"
                        title={error.response.data.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )
            }
        } else {
            productsRemove(index)
        }
    }

    function onProductRemove(index: number) {
        if (products[index]?.isDefault && products.length > 1) {
            if (index == 0) {
                productsUpdate(1, {
                    ...products[1],
                    isDefault: true
                })
            } else {
                productsUpdate(0, {
                    ...products[0],
                    isDefault: true
                })
            }
        }
        productsRemove(index)

    }


    function onSwitchChange(value: any, index: number) {
        if (value == false) {
            products.forEach((product: any, i: number) => {
                productsUpdate(i, {
                    ...product,
                    isDefault: false
                })
            })
            productsUpdate(index, {
                ...products[index],
                isDefault: true
            })
        }
    }

    function onRadioClick(value: any, index: number) {
        // if (value == "false") {
        products.forEach((product: any, i: number) => {
            productsUpdate(i, {
                ...product,
                isDefault: false
            })
        })
        productsUpdate(index, {
            ...products[index],
            isDefault: true
        })
        // }
    }

    return (
        <>
            <Breadcrumb items={breadcrumbItems} />
            <form className='flex flex-col h-full'>
                {params.type != 'Add-Product' && <Link
                    className="mr-2 mb-2 ml-auto"
                    to={`/catalog/manage-products/Add-Product?parent=${params?.id}&redirect=${location.pathname}`}
                >
                    <Button
                        variant="twoTone"
                        color="green-600"
                        icon={<AiOutlinePlus />}
                    >
                        Add Product
                    </Button>
                </Link>}
                <div className="grid grid-cols-2 gap-4 mt-2">
                    <FormItem label="Name">
                        <Controller
                            name="name"
                            control={control}
                            defaultValue=""
                            rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <input
                                    {...field}
                                    type="text"
                                    className={`${errors.name
                                        ? ' input input-md h-11 input-invalid'
                                        : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                        }`}
                                />
                            )}
                        />
                        {errors.nameEn && (
                            <small className="text-red-600 py-3">
                                {errors.nameEn.message as string}
                            </small>
                        )}
                    </FormItem>
                    <FormItem label="SKU">
                        <Controller
                            name="sku"
                            control={control}
                            defaultValue=""
                            rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <input
                                    type="text"
                                    className={`${errors.sku
                                        ? ' input input-md h-11 input-invalid'
                                        : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                        }`}
                                    {...field}
                                />
                            )}
                        />
                        {errors.sku && (
                            <small className="text-red-600 py-3">
                                {errors.sku.message as string}
                            </small>
                        )}
                    </FormItem>
                </div>

                <div className="grid grid-cols-2 gap-4">
                    <FormItem label="Product Type">
                        <Controller
                            name="type"
                            control={control}
                            defaultValue={{ label: "Frame", value: "frame" }}
                            rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <Select
                                    options={productTypes}
                                    isOptionDisabled={(option) => option.isDisabled}
                                    {...field}
                                />
                            )}
                        />
                    </FormItem>
                    <FormItem label="Products">
                        <Controller
                            name="products"
                            control={control}
                            defaultValue=""
                            rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <Select
                                    onInputChange={(value) => setProductValue(value)}
                                    options={productOptions}
                                    isLoading={isLoading}
                                    {...field}
                                    onChange={productSelect}
                                    placeholder="Select Products"

                                />
                            )}
                        />
                        {errors.products && (
                            <small className="text-red-600 py-3">
                                {errors?.products?.root?.message as string}
                            </small>
                        )}
                    </FormItem>

                </div>

                {products.length > 0 && <div className="flex flex-col my-2 bg-slate-100 [&>*:nth-child(odd)]:bg-slate-50">
                    <div className="w-full p-3 flex justify-between">
                        <p style={{ fontWeight: "600" }}>SKU</p>
                        <p style={{ fontWeight: "600" }}>Name</p>
                        <div style={{ fontWeight: "600" }} className="flex gap-2">
                            <p>Actions</p>
                        </div>
                    </div>
                    {products.map((product: any, index: number) => (
                        <div key={product.id} className="w-full p-3 flex justify-between">
                            <p>{product?.sku}</p>
                            <p>{product?.name}</p>
                            <div className="flex gap-4">
                                <div className="flex flex-col gap-2 items-center">
                                    Default variant
                                    {/* <Switcher checked={product?.isDefault} onChange={(val) => onSwitchChange(val, index)} /> */}
                                    <Radio checked={product?.isDefault} value={product?.isDefault} onClick={(e: any) => onRadioClick(e.target.value, index)} />
                                </div>
                                <div className="flex flex-col gap-2 items-center">
                                    Edit
                                    <Link
                                        to={`/catalog/${headType == "contactLens" ? "manage-contact-lenses" : headType == "accessory" ? "manage-accessory" : "manage-sunglasses"}/Edit-Product/${product?.refid}`}
                                    >
                                        <HiPencilSquare size={25} />
                                    </Link>
                                </div>
                                <div className="flex flex-col gap-2 items-center">
                                    Remove
                                    <button>
                                        <CiCircleRemove size={30} color='red' onClick={() => onProductRemove(index)} />
                                    </button>
                                </div>
                            </div>
                        </div>
                    ))}
                </div>}

                <div className="flex gap-4 mt-auto ml-auto">
                    <Button
                        className="float-right mt-6 mx-4 [&_*]:pointer-events-none"
                        variant="solid"
                        type="button"
                        data-close={true}
                        loading={submitLoading}
                        onClick={handleSubmit(onSubmit)}
                        icon={<AiOutlineSave />}
                    >
                        Save & close
                    </Button>
                    <Button
                        className="float-right mt-6 [&_*]:pointer-events-none"
                        variant="solid"
                        type="button"
                        data-close={false}
                        loading={submitLoading}
                        onClick={handleSubmit(onSubmit)}
                        icon={<AiOutlineSave />}
                    >
                        Save & Continue
                    </Button>
                </div>
            </form>
        </>
    )
}
