/*eslint-disable */
import { Button, FormItem, Select, Upload, toast } from '@/components/ui'
import endpoints from '@/endpoints'
import api from '@/services/api.interceptor'
import { Controller, useForm, set } from 'react-hook-form'
import { AiOutlineSave } from 'react-icons/ai'
import Notification from '@/components/ui/Notification'
import { useNavigate, useParams } from 'react-router-dom'
import { useEffect, useState } from 'react'
import Breadcrumb from '../modals/BreadCrumb'
import { permissions } from './permissions'

export default function ManageRoles() {
    const navigate = useNavigate()
    const params = useParams()

    const breadcrumbItems = [
        { title: 'Roles', url: '/roles' },
        { title: params?.id ? 'Update Role' : 'Create Role', url: '' },
    ]

    const options = [
        { value: 'true', label: 'True' },
        { value: 'false', label: 'False' },
    ]

    const {
        handleSubmit,
        control,
        setValue,
        formState: { errors },
    } = useForm<any>()

    useEffect(() => {
        if (params.id)
            api.get(endpoints.roles + params.id)
                .then((res) => {
                    if (res?.status == 200) {
                        console.log(res.data)
                        setValue('name', res.data.result.name)
                        setValue('isActive', { value: res.data.result.isActive, label: res.data.result.isActive ? 'True' : 'False' })
                        const selectedPermissions = res.data.result.permissions
                        console.log('selectedPermissions', selectedPermissions)
                        selectedPermissions.forEach((permission: any) => {
                            console.log('permission', permission)
                            setValue(`permissions.${permission}`, true)
                        })
                    }
                })
                .catch((error) => {
                    console.error('Error fetching data: ', error)
                })
    }, [])

    const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
        const isChecked = event.target.checked
        permissions.forEach((permission) => {
            setValue(`permissions.${permission}`, isChecked)
        })
    }

    const onSubmit = (data: any) => {
        console.log("data ::", data)
        const checkedPermissions = Object.keys(data.permissions).filter(
            (key) => data.permissions[key] === true
        )

        let payload :any = {
            name: data.name,
            permissions: checkedPermissions,
        }

        if (params.id) {
            payload.isActive = data?.isActive?.value
            api.put(endpoints.roles + params.id, payload)
                .then((res) => {
                    if (res.status == 200) {
                            toast.push(
                                <Notification
                                    type="success"
                                    title={res.data.message}
                                />,
                                {
                                    placement: 'top-center',
                                }
                            )
                            navigate('/roles')
                    } 
                })
                .catch((err) => {
                    toast.push(
                        <Notification
                            type="warning"
                            title={err.response.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                })
        } else {
            api.post(endpoints.roles, payload)
                .then((res) => {
                    if (res.status == 200) {
                        toast.push(
                            <Notification
                                type="success"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                        navigate('/roles')
                    }
                })
                .catch((err) => {
                    toast.push(
                        <Notification
                            type="warning"
                            title={err.response.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                })
        }
    }

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <h3 className="mb-2">{params.id ? 'Edit' : 'Add'} Role</h3>
            <Breadcrumb items={breadcrumbItems} />
            <div className="grid grid-cols-2 gap-4 mt-4">
                <FormItem label="Name">
                    <Controller
                        name="name"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Name is required' }}
                        render={({ field }) => (
                            <input
                                type="text"
                                className={`${errors.name
                                    ? ' input input-md h-11 input-invalid'
                                    : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                    }`}
                                {...field}
                            />
                        )}
                    />
                    {errors.name && (
                        <small className="text-red-600 py-3">
                            {errors.name.message as string}
                        </small>
                    )}
                </FormItem>

                {params.id &&
                    <FormItem label="Is Active ?">
                        <Controller
                            name="isActive"
                            control={control}
                            defaultValue={true}
                            render={({ field }) => (
                                <Select
                                    options={options}
                                    {...field}
                                />
                            )}
                        />
                    </FormItem>
                }
            </div>

            <h5>Permissions</h5>
            <div className="flex items-center mb-2">
                <input
                    type="checkbox"
                    className="form-checkbox"
                    onChange={handleSelectAll}
                />
                <label className="ml-2">Select All</label>
            </div>

            <div className="grid grid-cols-6 gap-4 mt-2">
                {permissions.map((permission, index) => (
                    <div key={index} className="flex items-center">
                        <Controller
                            name={`permissions.${permission}`}
                            control={control}
                            defaultValue={false}
                            render={({ field }) => (
                                <label className="flex items-center cursor-pointer">
                                <input
                                    type="checkbox"
                                    className="form-checkbox"
                                    checked={field.value}
                                    {...field}
                                />
                                <span className="ml-2">{permission}</span>
                                </label>    
                            )}
                        />
                        {/* <label className="ml-2">{permission}</label> */}
                    </div>
                ))}
            </div>

            <Button
                className="float-right mt-4"
                variant="solid"
                type="submit"
                icon={<AiOutlineSave />}
            >
                {params.id ? 'Update' : 'Save'}
            </Button>
        </form>
    )
}
