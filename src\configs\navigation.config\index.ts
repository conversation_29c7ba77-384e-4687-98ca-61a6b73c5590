import {
    // NAV_ITEM_TYPE_TITLE,
    NAV_ITEM_TYPE_ITEM,
    NAV_ITEM_TYPE_COLLAPSE
} from '@/constants/navigation.constant'
import type { NavigationTree } from '@/@types/navigation'

const navigationConfig: NavigationTree[] = [
    {
        key: 'home',
        path: '/home',
        title: 'Dashboard',
        translateKey: 'nav.home.dzg',
        icon: 'home',
        type: NAV_ITEM_TYPE_ITEM,
        authority: [],
        subMenu: [],
    },
    {
        key: 'admins',
        path: '',
        title: 'Admins',
        translateKey: '',
        icon: 'admins',
        type: NAV_ITEM_TYPE_COLLAPSE,
        authority: [],
        subMenu: [
            {
                key: 'admins',
                path: '/admins',
                title: 'Admin Users',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'roles',
                path: '/roles',
                title: 'Roles',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            }
        ],
    },
    {
        key: 'dashboadDnd',
        path: '/dashboard-rearrange',
        title: 'Home Page Customization',
        translateKey: '',
        icon: 'customize',
        type: NAV_ITEM_TYPE_ITEM,
        authority: [],
        subMenu: [],
    },
    {
        key: 'catalog',
        path: '',
        title: 'Catalog',
        translateKey: '',
        icon: 'catalog',
        type: NAV_ITEM_TYPE_COLLAPSE,
        authority: [],
        subMenu: [
            {
                key: 'brands',
                path: '/catalog/brands',
                title: 'Brands',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'categories',
                path: '/catalog/categories',
                title: 'Categories',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'product-head',
                path: '/catalog/product-head',
                title: 'Product Head',
                translateKey: '',
                icon: 'products',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'collections',
                path: '/catalog/collections',
                title: 'Collections',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'nonlist',
                path: '/non-list',
                title: 'Non-Listed Items',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            }
        ],
    },
    {
        key: 'Products',
        path: '',
        title: 'Product Types',
        translateKey: '',
        icon: 'catalog',
        type: NAV_ITEM_TYPE_COLLAPSE,
        authority: [],
        subMenu: [
            {
                key: 'sunglasses',
                path: '/catalog/sunglasses',
                title: 'Sunglasses',
                translateKey: '',
                icon: 'products',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'contactLenses',
                path: '/catalog/contact-lenses',
                title: 'Contact Lens',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'accessory',
                path: '/catalog/accessory',
                title: 'Accessories',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
        ],
    },
    {
        key: 'attribute',
        path: '',
        title: 'Attributes',
        translateKey: '',
        icon: 'attribute',
        type: NAV_ITEM_TYPE_COLLAPSE,
        authority: [],
        subMenu: [
            {
                key: 'colors',
                path: '/colors',
                title: 'Colors',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'sizes',
                path: '/sizes',
                title: 'Sizes',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'contact-lens-sizes',
                path: '/contact-lens-sizes',
                title: 'Contact Lens Sizes',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'frameShapes',
                path: '/frame-shapes',
                title: 'Frame Shape',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'frameTypes',
                path: '/frame-types',
                title: 'Frame Type',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            // {
            //     key: 'ageGroup',
            //     path: '/catalog/age-group',
            //     title: 'Age Group',
            //     translateKey: '',
            //     icon: '',
            //     type: NAV_ITEM_TYPE_ITEM,
            //     authority: [],
            //     subMenu: [],
            // },
            {
                key: 'label',
                path: '/label',
                title: 'Label Text',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'frontMaterial',
                path: '/front-material',
                title: 'Front Material',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'type',
                path: '/types',
                title: 'Types',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'lensMaterial',
                path: '/lens-material',
                title: 'Lens Material',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'contactLens',
                path: '/contact-lens-power',
                title: 'Contact Lens Power',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            }
        ],
    },
    {
        key: 'cutomers',
        path: '',
        title: 'Customers',
        translateKey: '',
        icon: 'customers',
        type: NAV_ITEM_TYPE_COLLAPSE,
        authority: [],
        subMenu: [
            {
                key: 'customers',
                path: '/customers',
                title: 'All Customers',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'cart',
                path: '/cart',
                title: 'Cart',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
        ],
    },
    {
        key: 'orders',
        path: '',
        title: 'Orders',
        translateKey: '',
        icon: 'orders',
        type: NAV_ITEM_TYPE_COLLAPSE,
        authority: [],
        subMenu: [
            {
                key: 'orders',
                path: '/orders',
                title: 'Orders',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'trycartorder',
                path: '/try-cart-order',
                title: 'Try Cart Orders',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
                hide: true
            }
        ],
    },
    {
        key: 'marketing',
        path: '',
        title: 'Marketing',
        translateKey: '',
        icon: 'marketing',
        type: NAV_ITEM_TYPE_COLLAPSE,
        authority: [],
        subMenu: [
            {
                key: 'blogs',
                path: '/blogs',
                title: 'Blogs',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'newsLetter',
                path: '/news-letter',
                title: 'News Letter',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'reviews',
                path: '/reviews',
                title: 'Reviews',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            }
            // {
            //     key: 'banners',
            //     path: '/banners',
            //     title: 'Banners',
            //     translateKey: '',
            //     icon: '',
            //     type: NAV_ITEM_TYPE_ITEM,
            //     authority: [],
            //     subMenu: [],
            // },
        ],
    },
    {
        key: 'enquiries',
        path: '',
        title: 'Enquiries',
        translateKey: '',
        icon: 'enquiries',
        type: NAV_ITEM_TYPE_COLLAPSE,
        authority: [],
        subMenu: [
            {
                key: 'insurance',
                path: '/insurance-enquiries',
                title: 'Insurance Enquiries',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'contactUs',
                path: '/contact-us-enquiries',
                title: 'Contact Enquiries',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'productEnquiries',
                path: '/product-enquiries',
                title: 'Product Enquiries',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'lensEnquiry',
                path: '/lens-enquiries',
                title: 'Lens Enquiries',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            }
        ],
    },
    {
        key: 'lens',
        path: '',
        title: 'Lens',
        translateKey: '',
        icon: 'lens',
        type: NAV_ITEM_TYPE_COLLAPSE,
        authority: [],
        subMenu: [
            {
                key: 'lensBrand',
                path: '/lens-brand',
                title: 'Lens Brand',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'lensPower',
                path: '/lens-power',
                title: 'Lens Power',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'lensType',
                path: '/lens-type',
                title: 'Lens Type',
                translateKey: '',
                hide: true,
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'lensIndex',
                path: '/lens-index',
                title: 'Lens Index',
                translateKey: '',
                hide: true,
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'lensCoating',
                path: '/coating',
                title: 'Lens Coating',
                translateKey: '',
                hide: true,
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            }
        ],
    },
    {
        key: 'stores',
        path: '/stores',
        title: 'Stores',
        translateKey: '',
        icon: 'store',
        type: NAV_ITEM_TYPE_ITEM,
        authority: [],
        subMenu: [],
    },
    {
        key: 'insuranceProvider',
        path: '/insurance-provider',
        title: 'Insurance Providers',
        translateKey: '',
        icon: 'insurance',
        type: NAV_ITEM_TYPE_ITEM,
        authority: [],
        subMenu: [],
    },
    {
        key: 'pages',
        path: '',
        title: 'Pages',
        translateKey: '',
        icon: 'pages',
        type: NAV_ITEM_TYPE_COLLAPSE,
        authority: [],
        subMenu: [
            {
                key: 'contactUsBanner',
                path: '/contact-banner',
                title: 'Contact Us Page',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'insurance.content',
                path: '/insurance-contents',
                title: 'Insurance Page',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'contactLens',
                path: '/contact-lens',
                title: 'Contact Lens Page',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'faq',
                path: '/faq',
                title: 'FAQ',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'terms',
                path: '/pages/terms',
                title: 'Terms and Conditions',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'privacyPolicy',
                path: '/pages/privacy-policy',
                title: 'Privacy Policy',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'cookiePolicy',
                path: '/pages/cookie-policy',
                title: 'Cookie Policy',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'refundPolicy',
                path: '/pages/refund-policy',
                title: 'Refund Policy',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'shippingPolicy',
                path: '/pages/shipping-policy',
                title: 'Shipping Policy',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'returnPolicy',
                path: '/pages/return-policy',
                title: 'Return Policy',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'aboutUs',
                path: '/pages/about-us',
                title: 'About Us',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
        ],
    },
    {
        key: 'subscription',
        path: '',
        title: 'Subscription',
        translateKey: '',
        icon: 'subscription',
        type: NAV_ITEM_TYPE_COLLAPSE,
        authority: [],
        subMenu: [
            {
                key: 'subscription.plans',
                path: '/subscription-plans',
                title: 'Subscription Plans',
                translateKey: '',
                icon: 'subscription',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'subscription',
                path: '/subscriptions',
                title: 'Subscribers',
                translateKey: '',
                icon: 'subscription',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
        ]
    },
    {
        key: 'reports',
        path: '',
        title: 'Reports',
        translateKey: '',
        icon: 'reports',
        type: NAV_ITEM_TYPE_COLLAPSE,
        authority: [],
        subMenu: [
            {
                key: 'sales',
                path: '/sales-report',
                title: 'Sales Report',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'revenue',
                path: '/revenue-report',
                title: 'Revenue Report',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'topSelling',
                path: '/top-selling-report',
                title: 'Top Selling Report',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'customer-report',
                path: '/customer-report',
                title: 'Customer Report',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'guest-report',
                path: '/guest-report',
                title: 'Guest Report',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
        ]
    },
    {
        key: 'settings',
        path: '',
        title: 'Settings',
        translateKey: '',
        icon: 'settings',
        type: NAV_ITEM_TYPE_COLLAPSE,
        authority: [],
        subMenu: [
            {
                key: 'headerMenu',
                path: '/header-menu',
                title: 'Header Menu',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'translation',
                path: '/translation',
                title: 'Translation',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'footer',
                path: '/footer',
                title: 'Footer',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'generalSettings',
                path: '/general-settings',
                title: 'General Settings',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'trycart.settings',
                path: '/try-cart',
                title: 'Try Cart Settings',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            // {
            //     key: 'metaTags',
            //     path: '/meta-tags',
            //     title: 'Meta Tags',
            //     translateKey: '',
            //     icon: '',
            //     type: NAV_ITEM_TYPE_ITEM,
            //     authority: [],
            //     subMenu: [],
            // },
            {
                key: 'additionalFees',
                path: '/additional-fees',
                title: 'Additional Fees',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
            {
                key: 'vm-policy',
                path: '/vm-policy',
                title: 'VM Policy',
                translateKey: '',
                icon: '',
                type: NAV_ITEM_TYPE_ITEM,
                authority: [],
                subMenu: [],
            },
        ],
    },
    {
        key: 'multi-store',
        path: '/multi-store',
        title: 'Multi Store',
        translateKey: '',
        icon: 'multiStore',
        type: NAV_ITEM_TYPE_ITEM,
        authority: [],
        subMenu: [],
    },
]

export default navigationConfig
