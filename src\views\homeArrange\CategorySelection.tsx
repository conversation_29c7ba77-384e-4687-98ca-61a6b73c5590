
/* eslint-disable */
import SelectionItem from './SelectionItem'; // Import the SelectionItem component

interface CategorySelectionProps {
    categories: any[];
    onCategoryChange: (categoryId: string) => void;
}

const baseUrl = import.meta.env.VITE_ASSET_URL;

const CategorySelection = ({ categories, onCategoryChange }: CategorySelectionProps) => (
    <div className="mt-5 max-h-full overflow-y-auto">
        <div className="flow-root">
            <h5>Select Categories</h5>
            <ul className="grid w-full gap-4 md:grid-cols-4 mb-4">
                {categories.map((category, index) => (
                    <SelectionItem
                        key={category.refid}
                        id={category.refid}
                        label={category.name.en}
                        image={`${baseUrl}${category.image}`}
                        onChange={() => onCategoryChange(category.refid)}
                        checked={category.inHome}
                    />
                ))}
            </ul>
        </div>
    </div>
);

export default CategorySelection;