import { useCallback } from 'react'
import Card from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Table from '@/components/ui/Table'
import Badge from '@/components/ui/Badge'
import useThemeClass from '@/utils/hooks/useThemeClass'
import {
    useReactTable,
    getCoreRowModel,
    flexRender,
    createColumnHelper,
} from '@tanstack/react-table'
import { Link, useNavigate } from 'react-router-dom'
import { NumericFormat } from 'react-number-format'
import dayjs from 'dayjs'

type Order = {
    id: string
    date: number
    customer: string
    status: number
    paymentMehod: string
    totalAmount: number
}

type LatestOrderProps = {
    data?: Order[]
    className?: string
}

type OrderColumnPros = {
    row: Order
}

const { Tr, Td, TBody, THead, Th } = Table

const orderStatusColor: Record<
    number,
    {
        label: string
        dotClass: string
        textClass: string
    }
> = {
    1: { label: 'PENDING', dotClass: 'bg-amber-500', textClass: 'text-amber-500' },
    2: { label: 'FAILED', dotClass: 'bg-red-500', textClass: 'text-red-500' },
    3: { label: 'PLACED', dotClass: 'bg-blue-500', textClass: 'text-blue-500' },
    4: { label: 'CONFIRMED', dotClass: 'bg-blue-500', textClass: 'text-blue-500' },
    5: { label: 'SHIPPED', dotClass: 'bg-emerald-500', textClass: 'text-emerald-500' },
    6: { label: 'OUT FOR DELIVERY', dotClass: 'bg-amber-500', textClass: 'text-amber-500' },
    7: { label: 'DELIVERED', dotClass: 'bg-emerald-500', textClass: 'text-emerald-500' },
    8: { label: 'CANCELLED', dotClass: 'bg-red-500', textClass: 'text-red-500' },
    9: { label: 'REFUNDED', dotClass: 'bg-red-500', textClass: 'text-red-500' },
    10: { label: 'RETURNED', dotClass: 'bg-red-500', textClass: 'text-red-500' },
}

const OrderColumn = ({ row }: OrderColumnPros) => {
    const { textTheme } = useThemeClass()
    const navigate = useNavigate()

    const onView = useCallback(() => {
        navigate(`/manage-orders/${row.id}`)
    }, [navigate, row])

    return (
        <span
            className={`cursor-pointer select-none font-semibold hover:${textTheme}`}
            onClick={onView}
        >
            #{row.id}
        </span>
    )
}

const columnHelper = createColumnHelper<Order>()

const currency = localStorage.getItem("currency") || "AED"

const columns = [
    columnHelper.accessor('id', {
        header: 'Order',
        cell: (props) => <OrderColumn row={props.row.original} />,
    }),
    columnHelper.accessor('status', {
        header: 'Status',
        cell: (props) => {
            const { status } = props.row.original
            return (
                <div className="flex items-center">
                    <Badge className={orderStatusColor[status]?.dotClass} />
                    <span
                        className={`ml-2 rtl:mr-2 capitalize font-semibold ${orderStatusColor[status]?.textClass}`}
                    >
                        {orderStatusColor[status]?.label}
                    </span>
                </div>
            )
        },
    }),
    columnHelper.accessor('date', {
        header: 'Date',
        cell: (props) => {
            const row = props.row.original
            return <span>{row.date ? dayjs(row.date).format('DD MMM YYYY') : ''}</span>
        },
    }),
    columnHelper.accessor('customer', {
        header: 'Customer',
    }),
    columnHelper.accessor('totalAmount', {
        header: 'Total',
        cell: (props) => {
            const { totalAmount } = props.row.original
            return (
                <NumericFormat
                    displayType="text"
                    value={(Math.round(totalAmount * 100) / 100).toFixed(2)}
                    prefix={`${currency} `}
                    thousandSeparator={true}
                />
            )
        },
    }),
]

const LatestOrder = ({ data = [], className }: LatestOrderProps) => {
    const table = useReactTable({
        data,
        columns,
        getCoreRowModel: getCoreRowModel(),
    })

    return (
        <Card className={className}>
            <div className="flex items-center justify-between mb-6">
                <h4>Latest Orders</h4>
                <Link to={'/orders'}><Button size="sm">View Orders</Button></Link>
            </div>
            <Table>
                <THead>
                    {table.getHeaderGroups().map((headerGroup) => (
                        <Tr key={headerGroup.id}>
                            {headerGroup.headers.map((header) => {
                                return (
                                    <Th
                                        key={header.id}
                                        colSpan={header.colSpan}
                                    >
                                        {flexRender(
                                            header.column.columnDef.header,
                                            header.getContext()
                                        )}
                                    </Th>
                                )
                            })}
                        </Tr>
                    ))}
                </THead>
                <TBody>
                    {table.getRowModel().rows.map((row) => {
                        return (
                            <Tr key={row.id}>
                                {row.getVisibleCells().map((cell) => {
                                    return (
                                        <Td key={cell.id}>
                                            {flexRender(
                                                cell.column.columnDef.cell,
                                                cell.getContext()
                                            )}
                                        </Td>
                                    )
                                })}
                            </Tr>
                        )
                    })}
                </TBody>
            </Table>
        </Card>
    )
}

export default LatestOrder
