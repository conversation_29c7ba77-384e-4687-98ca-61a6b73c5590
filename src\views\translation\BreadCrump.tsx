import { FormItem, Input } from '@/components/ui'
import React from 'react'
import { Controller } from 'react-hook-form'

const bread: { label: string, value: string, db: string }[] = [
    {
        label: "Brands",
        value: "Brands",
        db: "brands",
    },
    {
        label: "Home",
        value: "Home",
        db: "home",
    },
    {
        label: "Products",
        value: "Products",
        db: "products",
    },
    {
        label: "Insurance",
        value: "Insurance",
        db: "insurance",
    },
    {
        label: "Store Locator",
        value: "StoreLocator",
        db: "storeLocator",
    },
    {
        label: "My Account",
        value: "MyAccount",
        db: "myAccount",
    },
    {
        label: "My Cart",
        value: "MyCart",
        db: "myCart",
    },
    {
        label: "Contact Us",
        value: "ContactUs",
        db: "contactUs",
    },
    {
        label: "About Us",
        value: "AboutUs",
        db: "aboutUs",
    },
    {
        label: "Privacy Policy",
        value: "PrivacyPolicy",
        db: "privacyPolicy",
    },
    {
        label: "Terms And Condition",
        value: "TermsAndCondition",
        db: "termsAndCondition",
    },
    {
        label: "Shipping Policy",
        value: "ShippingPolicy",
        db: "shippingPolicy",
    },
    {
        label: "Return Policy",
        value: "ReturnPolicy",
        db: "returnPolicy",
    },
    {
        label: "Cookie Policy",
        value: "CookiePolicy",
        db: "cookiePolicy",
    },
    {
        label: "RefundPolicy",
        value: "RefundPolicy",
        db: "refundPolicy",
    },
    {
        label: "All Products",
        value: "AllProducts",
        db: "allProducts",
    },
    {
        label: "Blogs",
        value: "Blogs",
        db: "blogs",
    },
    {
        label: "Category",
        value: "Category",
        db: "category",
    },
]


export const breadCrump: { label: string, value: string, db: string }[] = [
    ...bread
]

function BreadCrumpTab({ control, errors }: any) {
    return (
        <>
            <h3>Bread Crumps</h3>
            {bread.map((item) => (
                <Forms key={item.value} control={control} errors={errors} item={item} />
            ))}
        </>
    )
}

function Forms({ item, control, errors }: any) {
    return (
        <div className="mt-2">
            <div className="grid grid-cols-2 gap-4">
                <FormItem label={`${item.label} English`}>
                    <Controller
                        control={control}
                        name={`breadCrump${item.value}En`}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {errors[`breadCrump${item.value}En`] && (
                        <small className="text-red-600 py-3">
                            {' '}
                            {errors[`breadCrump${item.value}En`].message as string}{' '}
                        </small>
                    )}
                </FormItem>
                <FormItem label={`${item.label} Arabic`}>
                    <Controller
                        control={control}
                        name={`breadCrump${item.value}Ar`}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => <Input dir='rtl' type="text" {...field} />}
                    />
                    {errors[`breadCrump${item.value}Ar`] && (
                        <small className="text-red-600 py-3">
                            {' '}
                            {errors[`breadCrump${item.value}Ar`].message as string}{' '}
                        </small>
                    )}
                </FormItem>
            </div>
        </div>
    )
}

export default BreadCrumpTab