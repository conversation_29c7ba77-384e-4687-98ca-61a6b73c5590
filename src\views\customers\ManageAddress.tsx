import { Button, FormItem, Select, toast } from "@/components/ui";
import endpoints from "@/endpoints";
import api from "@/services/api.interceptor";
import { useEffect } from "react";
import { Controller, useForm } from "react-hook-form"
import Notification from '@/components/ui/Notification'

interface addressProps {
    data: any
    closeModal: () => void
}

export default function ManageAddress( { data, closeModal }: addressProps ) {

    const {
        handleSubmit,
        control,
        setValue,
        formState: { errors },
    } = useForm()

    const setAddress = () => {
        console.log(data);
        setValue('name', data?.name)
        setValue('country', data?.country)
        setValue('emirates', data?.emirates)
        setValue('street', data?.street)
        setValue('suiteUnit', data?.suiteUnit)
        setValue('postalCode', data?.postalCode)
        setValue('countryCode', {value : data?.countryCode, label: data?.countryCode})
        setValue('mobile', data?.mobile)
        setValue('city', data?.city)
    }

    useEffect(() => {
        setAddress()
    }, [])
    

    const onSubmit = (value: any) => {
        console.log(value);

        const payload : any = {
            name: value.name,
            country: value.country,
            emirates: value.emirates,
            street: value.street,
            suiteUnit: value.suiteUnit,
            postalCode: value.postalCode,
            countryCode: value.countryCode.value,
            mobile: value.mobile,
            city: value.city,
        }

        api.put(endpoints.updateAddress + data.refid, payload ).then((res) => {
            if (res.status === 200) {
                toast.push(
                    <Notification type="success" title="Address Updated Successfully" />, {
                    placement: 'top-center',
                })
                closeModal()
            }    
        }).catch((err) => {
            toast.push(
                <Notification type="warning" title={err.response.data.message} />, {
                    placement: 'top-center',
                }
            )
        })
    }

    return (
        <div className="mb-4 max-h-[480px] overflow-y-auto my-2">
            <h5 className="mb-2">Address Details</h5>
            <form onSubmit={handleSubmit(onSubmit)}>
                <div className="grid grid-cols-2 gap-4">
                    <FormItem label="Name">
                        <Controller
                            control={control}
                            name="name"
                            defaultValue=""
                            rules={{ required: 'Name is required' }}
                            render={({ field }) => (
                                <input
                                    type="text"
                                    className={`${errors.name ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                                    {...field}
                                />
                            )}
                        />
                    </FormItem>
                </div>

                <div className="grid grid-cols-2 gap-4">
                    <FormItem label="Country/Region">
                        <Controller
                            control={control}
                            name="country"
                            defaultValue=""
                            rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <input
                                    type="text"
                                    className={`${errors.country ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                                    {...field}
                                />
                            )}
                        />
                    </FormItem>
                    <FormItem label="Emirates">
                        <Controller
                            control={control}
                            name="emirates"
                            defaultValue=""
                            rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <input
                                    type="text"
                                    className={`${errors.emirates ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                                    {...field}
                                />
                            )}
                        />
                    </FormItem>

                </div>

                <div className="grid grid-cols-2 gap-4">
                    <FormItem label="Street Address">
                        <Controller
                            control={control}
                            name="street"
                            defaultValue=""
                            rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <input
                                    type="text"
                                    className={`${errors.street ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                                    {...field}
                                />
                            )}
                        />
                    </FormItem>
                    <FormItem label="Apt, Suite unit">
                        <Controller
                            control={control}
                            name="suiteUnit"
                            defaultValue=""
                            rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <input
                                    type="text"
                                    className={`${errors.apt ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                                    {...field}
                                />
                            )}
                        />
                    </FormItem>

                </div>

                <div className="grid grid-cols-2 gap-4">
                    <FormItem label="City">
                        <Controller
                            control={control}
                            name="city"
                            defaultValue=""
                            rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <input
                                    type="text"
                                    className={`${errors.city ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                                    {...field}
                                />
                            )}
                        />
                    </FormItem>
                    <FormItem label="Postal Code">
                        <Controller
                            control={control}
                            name="postalCode"
                            defaultValue=""
                            rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <input
                                    type="text"
                                    className={`${errors.postal ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                                    {...field}
                                />
                            )}
                        />
                    </FormItem>
                </div>

                <div className="grid grid-cols-2 gap-4">
                    <FormItem label="Country Code">
                        <Controller 
                            control={control}
                            name="countryCode"
                            defaultValue={{ value: '', label: '' }} // Set a default value
                            render={({ field }) => (
                                <Select
                                    {...field}
                                    options={[
                                        { value: '+91', label: '+91' },
                                        { value: '+971', label: '+971' },
                                    ]}
                                    onChange={(option) => field.onChange(option)} // Update the value when an option is selected
                                />
                            )}
                        />
                    </FormItem>
                    <FormItem label="Mobile">
                        <Controller
                            control={control}
                            name="mobile"
                            rules={{ required: 'Field is required' }}
                            render={({ field }) => (
                                <input
                                    type="text"
                                    className={`${errors.mobile ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                                    {...field}
                                />
                            )}
                        />
                    </FormItem>
                </div>

                {/* <div className="grid grid-cols-2 gap-4">
                    <FormItem label="Address Type">
                        <Select
                            name="haveInsurance"
                            options={[
                                { value: 'office', label: 'Office' },
                                { value: 'home', label: 'Home' },
                                { value: 'other', label: 'Other' },
                            ]}
                        />
                    </FormItem>
                </div> */}

                <Button className='' variant="solid" type="submit">
                    Update
                </Button>
            </form>
        </div>
    )
}
