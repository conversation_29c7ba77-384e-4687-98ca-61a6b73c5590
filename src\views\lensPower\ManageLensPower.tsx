/* eslint-disable */
import { Button, FormItem, Select, toast } from '@/components/ui'
import endpoints from '@/endpoints'
import api from '@/services/api.interceptor'
import { Controller, useForm } from 'react-hook-form'
import { AiOutlineSave } from 'react-icons/ai'
import Notification from '@/components/ui/Notification'
import { useNavigate, useParams } from 'react-router-dom'
import Input from '@/components/ui/Input'
import { useEffect, useState } from 'react'
import Breadcrumb from '../modals/BreadCrumb'

export default function ManageLensPower() {
    const [brands, setBrands] = useState<any[]>([])
    const navigate = useNavigate()
    const params = useParams()

    const breadcrumbItems = [
        { title: 'Lens Power', url: '/lens-power' },
        { title: params?.id ? `Update ${params?.type}` : `Create ${params?.type}`, url: '' },
    ];

    const getLensPowerDetail = async () => {
        api.get(endpoints.lensPowerDetail + params.id).then((res) => {
            if (res?.status == 200) {
                setValue('brand', { value: res.data?.result?.brand?._id, label: res.data?.result?.brand?.name?.en })
                setValue('name', res.data?.result?.name)
                setValue('price', res.data?.result?.price)
            }
        }).catch((error) => {
            navigate('/access-denied')
            console.error('Error fetching data: ', error)
        })
    }

    const getBrands = () => {
        api.post(endpoints.lensBrands).then((res) => {
            if (res?.status == 200) {
                const lensBrands = res?.data?.result || []
                const newLensBrandOptions = lensBrands.map((lensBrand: any) => ({
                    value: lensBrand._id,
                    label: lensBrand.name?.en
                }));
                setBrands(newLensBrandOptions);
            }
        })
    }

    useEffect(() => {
        if (params.id) {
            getLensPowerDetail()
        }
        getBrands()
    }, [])

    const {
        handleSubmit,
        control,
        setValue,
        formState: { errors },
    } = useForm<any>()

    const onSubmit = (value: any) => {
        console.log(value)
        const data = {
            type: params.type,
            brand: value.brand.value,
            name: value.name,
            price: value.price,
        }

        console.log("data :: ", data)

        if (params?.id) {
            api.put(endpoints.updateLensPower + params.id, data).then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification type="success" title={res.data.message} />, {
                        placement: 'top-center',
                    })
                    navigate('/lens-power')
                }
            }).catch((err) => {
                toast.push(
                    <Notification type="warning" title={err.response.data.message} />, {
                    placement: 'top-center',
                })
            })
        } else {
            api.post(endpoints.createLensPower, data).then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification type="success" title={res.data.message} />, {
                        placement: 'top-center',
                    })
                    navigate('/lens-power')
                }
            }).catch((err) => {
                toast.push(
                    <Notification type="warning" title={err.response.data.message} />, {
                    placement: 'top-center',
                })
            })
        }
    }

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <h3 className="mb-2">{params?.id ? 'Update' : 'Create '} {params?.type} </h3>
            <Breadcrumb items={breadcrumbItems} />
            <div className="grid grid-cols-2 gap-4 mt-4">
                <FormItem label='Name'>
                    <Controller
                        control={control}
                        name="name"
                        defaultValue={''}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.name && (<small className="text-red-600 py-3"> {errors.name.message as string} </small>)}
                </FormItem>

                <FormItem label='Brand'>
                    <Controller
                        control={control}
                        name="brand"
                        defaultValue={''}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => (
                            <Select
                                options={brands}
                                {...field}
                            />
                        )}
                    />
                    {errors.name && (<small className="text-red-600 py-3"> {errors.name.message as string} </small>)}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label='Price'>
                    <Controller
                        control={control}
                        name="price"
                        defaultValue={''}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => (
                            <Input
                                min={0}
                                type="number"
                                {...field}
                            />
                        )}
                    />
                    {errors.name && (<small className="text-red-600 py-3"> {errors.name.message as string} </small>)}
                </FormItem>
            </div>

            <Button
                className="float-right mt-4"
                variant="solid"
                type="submit"
                icon={<AiOutlineSave />}
            >
                {params?.id ? 'Update' : 'Save'}
            </Button>
        </form>
    )
}