import { FormItem, Input } from '@/components/ui'
import { Controller } from 'react-hook-form'

export const formFields: { label: string, value: string, db: string }[] = [
    {
        label: 'Full Name', // for display
        value: 'FullName', // for form name
        db: 'fullName', // field in the db
    },
    {
        label: 'Full Name Required Error',
        value: 'FullNameRequiredError',
        db: 'fullNameRequiredError',
    },
    {
        label: 'Numeric Error',
        value: 'NumericError',
        db: 'numericError',
    },
    {
        label: 'Email Address',
        value: 'EmailAddress',
        db: 'emailAddress',
    },
    {
        label: 'Email Address Required Error',
        value: 'EmailAddressRequiredError',
        db: 'emailAddressRequiredError',
    },
    {
        label: 'Email Address Invalid Error',
        value: 'EmailAddressInvalidError',
        db: 'emailAddressInvalidError',
    },
    {
        label: 'Email Address Yahoo Error',
        value: 'EmailAddressYahooError',
        db: 'emailAddressYahooError',
    },
    {
        label: 'Phone Number',
        value: 'PhoneNumber',
        db: 'phoneNumber',
    },
    {
        label: 'Phone Number Required Error',
        value: 'PhoneNumberRequiredError',
        db: 'phoneNumberRequiredError',
    },
    {
        label: 'Phone Number Invalid Error',
        value: 'PhoneNumberInvalidError',
        db: 'phoneNumberInvalidError',
    },
    {
        label: 'Store Enquiry',
        value: 'StoreEnquiry',
        db: 'storeEnquiry',
    },
    {
        label: 'Store Enquiry Required Error',
        value: 'StoreEnquiryRequiredError',
        db: 'storeEnquiryRequiredError',
    },
    {
        label: 'Select Store',
        value: 'SelectStore',
        db: 'selectStore',
    },
    {
        label: 'Submit',
        value: 'Submit',
        db: 'submit',
    },
    {
        label: 'Cancel',
        value: 'Cancel',
        db: 'cancel',
    },
    {
        label: 'Message',
        value: 'Message',
        db: 'message',
    },
    {
        label: 'Message Required Error',
        value: 'MessageRequiredError',
        db: 'messageRequiredError',
    },
    {
        label: 'Name of Insurance',
        value: 'NameOfInsurance',
        db: 'nameOfInsurance',
    },
    {
        label: 'Select Insurance',
        value: 'SelectInsurance',
        db: 'selectInsurance',
    },
    {
        label: 'Name of Insurance Required Error',
        value: 'NameOfInsuranceRequiredError',
        db: 'nameOfInsuranceRequiredError',
    },
    {
        label: 'Nationality',
        value: 'Nationality',
        db: 'nationality',
    },
    {
        label: 'Nationality Required Error',
        value: 'NationalityRequired Error',
        db: 'nationalityRequiredError',
    },
    {
        label: 'Country',
        value: 'Country',
        db: 'country',
    },
    {
        label: 'Country Required Error',
        value: 'CountryRequiredError',
        db: 'countryRequiredError',
    },
    {
        label: 'Emirates',
        value: 'Emirates',
        db: 'emirates',
    },
    {
        label: 'Select Emirates',
        value: 'SelectEmirates',
        db: 'selectEmirates',
    },
    {
        label: 'Emirates Required Error',
        value: 'EmiratesRequiredError',
        db: 'emiratesRequiredError',
    },
    {
        label: 'Emirates Id',
        value: 'EmiratesId',
        db: 'emiratesId',
    },
    {
        label: 'Emirates Id Required Error',
        value: 'EmiratesIdRequiredError',
        db: 'emiratesIdRequiredError',
    },
    {
        label: 'Emirates Id Invalid Error',
        value: 'EmiratesIdInvalidError',
        db: 'emiratesIdInvalidError',
    },
    {
        label: 'Upload Emirates',
        value: 'UploadEmirates',
        db: 'uploadEmirates',
    },
    {
        label: 'Member Id',
        value: 'MemberId',
        db: 'memberId',
    },
    {
        label: 'Insurance ID',
        value: 'InsuranceId',
        db: 'insuranceId',
    },
    {
        label: "Streat",
        value: "Streat",
        db: "streat"
    },
    {
        label: "Streat Required Error",
        value: "StreatRequiredError",
        db: "streatRequiredError"
    },
    {
        label: "City",
        value: "City",
        db: "city"
    },
    {
        label: "City Required Error",
        value: "CityRequiredError",
        db: "cityRequiredError"
    },
    {
        label: "Apartment",
        value: "Apartment",
        db: "apartment"
    },
    {
        label: "Postal Code",
        value: "PostalCode",
        db: "postalCode"
    },
    {
        label: "Address",
        value: "Address",
        db: "address"
    },
    {
        label: "Address Required",
        value: "AddressRequired",
        db: "addressRequired"
    },
    {
        label: "Address Type",
        value: "AddressType",
        db: "addressType"
    },
    {
        label: "Address Type Required Error",
        value: "AddressTypeRequiredError",
        db: "addressTypeRequiredError"
    },
    
]

function FormFieldsTab({ control, errors }: any) {
    return (
        <>
            <h3>Form Fields</h3>
            {formFields.map((item) => (
                <div className="mt-2">
                    <div className="grid grid-cols-2 gap-4">
                        <FormItem label={`${item.label} English`}>
                            <Controller
                                control={control}
                                name={`formFields${item.value}En`}
                                rules={{ required: 'Field Required' }}
                                render={({ field }) => <Input type="text" {...field} />}
                            />
                            {errors[`formFields${item.value}En`] && (
                                <small className="text-red-600 py-3">
                                    {' '}
                                    {errors[`formFields${item.value}En`].message as string}{' '}
                                </small>
                            )}
                        </FormItem>
                        <FormItem label={`${item.label} Arabic`}>
                            <Controller
                                control={control}
                                name={`formFields${item.value}Ar`}
                                rules={{ required: 'Field Required' }}
                                render={({ field }) => <Input dir='rtl' type="text" {...field} />}
                            />
                            {errors[`formFields${item.value}Ar`] && (
                                <small className="text-red-600 py-3">
                                    {' '}
                                    {errors[`formFields${item.value}Ar`].message as string}{' '}
                                </small>
                            )}
                        </FormItem>
                    </div>
                </div>
            ))}
        </>
    )
}

export default FormFieldsTab