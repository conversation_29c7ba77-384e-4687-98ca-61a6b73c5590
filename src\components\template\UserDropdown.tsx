import Avatar from '@/components/ui/Avatar'
import Dropdown from '@/components/ui/Dropdown'
import withHeaderItem from '@/utils/hoc/withHeaderItem'
import useAuth from '@/utils/hooks/useAuth'
import { Link } from 'react-router-dom'
import classNames from 'classnames'
import { HiOutlineLogout, HiOutlineUser } from 'react-icons/hi'
import type { CommonProps } from '@/@types/common'
import { useEffect, useState } from 'react'
import api from '@/services/api.interceptor'
import endpoints from '@/endpoints'

type DropdownList = {
    label: string
    path: string
    icon: JSX.Element
}

type Admin = {
    name: string
    email: string
    avatar: string
    refid?: string
    role: { name: string }
}

const _UserDropdown = ({ className }: CommonProps) => {
    const [user, setUser] = useState<Admin>({
        name: 'User02',
        email: 'XXXXXXXXXXXXXXX',
        avatar: 'XXXXXXXXXXXXXXXXXXXXXXXXX',
        role: { name: 'admin' },
    })
    const dropdownItemList: DropdownList[] = [
        {
            icon: <HiOutlineUser />,
            label: 'Profile',
            path: `/manage-admins/${user?.refid}`,
        },
    ]
    useEffect(() => {
        api.get(endpoints.getUser).then((res) => {
            console.log(res.data?.result, 'userS')
            setUser(res.data?.result)
        })
    }, [])

    const { signOut } = useAuth()

    const UserAvatar = (
        <div className={classNames(className, 'flex items-center gap-2')}>
            <Avatar
                src={user?.avatar}
                size={32}
                shape="circle"
                icon={<HiOutlineUser />}
            />
            <div className="hidden md:block">
                <div className="text-xs capitalize">{user?.name}</div>
                <div className="font-bold">{user?.role?.name}</div>
            </div>
        </div>
    )

    return (
        <div>
            <Dropdown
                menuStyle={{ minWidth: 240 }}
                renderTitle={UserAvatar}
                placement="bottom-end"
            >
                <Dropdown.Item variant="header">
                    <div className="py-2 px-3 flex items-center gap-2">
                        <Avatar
                            src={user.avatar}
                            shape="circle"
                            icon={<HiOutlineUser />}
                        />
                        <div>
                            <div className="font-bold text-gray-900 dark:text-gray-100">
                                {user.name}
                            </div>
                            <div className="text-xs">{user.email}</div>
                        </div>
                    </div>
                </Dropdown.Item>

                <Dropdown.Item variant="divider" />
                {dropdownItemList.map((item) => (
                    <Dropdown.Item
                        key={item.label}
                        eventKey={item.label}
                        className="mb-1 px-0"
                    >
                        <Link
                            className="flex h-full w-full px-2"
                            to={item.path}
                        >
                            <span className="flex gap-2 items-center w-full">
                                <span className="text-xl opacity-50">
                                    {item.icon}
                                </span>
                                <span>{item.label}</span>
                            </span>
                        </Link>
                    </Dropdown.Item>
                ))}
                {/* <Dropdown.Item variant="divider" /> */}
                <Dropdown.Item
                    eventKey="Sign Out"
                    className="gap-2"
                    onClick={() => {
                        console.log('sign out')
                        localStorage.removeItem('admin')
                        window.location.href = '/login'
                    }}
                >
                    <span className="text-xl opacity-50">
                        <HiOutlineLogout />
                    </span>
                    <span>Sign Out</span>
                </Dropdown.Item>
            </Dropdown>
        </div>
    )
}

const UserDropdown = withHeaderItem(_UserDropdown)

export default UserDropdown
