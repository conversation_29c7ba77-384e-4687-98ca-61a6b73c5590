/* eslint-disable @typescript-eslint/no-unused-vars */
import endpoints from "@/endpoints";
import api from "@/services/api.interceptor";
import { useState } from "react";
import Previews from "./Previews";
import { toast } from "../ui";
import Notification from "../ui/Notification";

const defaultFileState = {
    name: "",
    size: 0,
    fileUrl: "",
};

export const Dropzone = ({
    onFileUrlChange,
    className,
    playgroundView = false,
    previews,
    ratio,
    multiple
}: {
    onFileUrlChange: (fileUrl: string) => void;
    className?: string;
    playgroundView?: boolean;
    previews?: string[];
    ratio?: [number, number];
    multiple?: boolean
}) => {
    const [file, setFile] = useState(defaultFileState);
    const [isHoveredOnDropzone, setIsHoveredOnDropzone] = useState(false);
    const [hasNonPdfFile, setHasNonPdfFile] = useState(false);
    const maxFileSize = 4 * 1024 * 1024; //  4 MB in bytes
    const acceptedFileTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];

    const [hasFile, setHasFile] = useState(false)

    // const hasFile = Boolean(file.name);

    const triggerMessage = (msg: string) => {
        toast.push(
            <Notification type="danger" duration={2000}>
                {msg || 'Upload Failed!'}
            </Notification>,
            {
                placement: 'top-center',
            }
        )
    }

    const setNewFile = (newFile: File) => {
        setHasFile(true)

        setTimeout(() => {
            setHasFile(false)
        }, 3000)
        if (!acceptedFileTypes.includes(newFile.type)) {
            triggerMessage("File type not supported. Only JPEG, PNG and WEBP files are allowed.");
            return;
        } else if (newFile.size > maxFileSize) {
            triggerMessage("File size exceeds the maximum limit of 4MB.");
            return;
        }

        if (file.fileUrl) {
            URL.revokeObjectURL(file.fileUrl);
        }

        const { name, size } = newFile;
        const fileUrl = URL.createObjectURL(newFile);
        setFile({ name, size, fileUrl });
        const formData = new FormData()

        formData.append('video', newFile)

        api.post(endpoints.videoUpload, formData).then((res) => {
            onFileUrlChange(res.data.videoUrl)
            console.log("SINGLE",res.data)
        })
        // onFileUrlChange(fileUrl);
    };

    const setFilesArray = (files: FileList) => {
        console.log(files)
        setHasFile(true)

        setTimeout(() => {
            setHasFile(false)
        }, 3000)
        const formData = new FormData()

        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            if (!acceptedFileTypes.includes(file.type)) {
                triggerMessage("File type not supported. Only JPEG, PNG and WEBP files are allowed.");
                return;
            } else if (file.size > maxFileSize) {
                triggerMessage("File size exceeds the maximum limit of 4MB.");
                return;
            }
            formData.append('image', file)
        }
        api.post(endpoints.imageUpload, formData).then((res:any) => {
           console.log(res.data)
           onFileUrlChange(res.data.imageUrl)
        })
    };

    const onDrop = (event: React.DragEvent<HTMLDivElement>) => {
        console.log(previews)
        event.preventDefault();
        console.log("e.nativeEvent")
        if(multiple){
            setFilesArray(event.dataTransfer.files);
            setIsHoveredOnDropzone(false);
            return;
        }
        const newFile = event.dataTransfer.files[0];
        // if (newFile.name.endsWith(".pdf")) {
        //     setHasNonPdfFile(false);
            setNewFile(newFile);
        // } else {
        //     setHasNonPdfFile(true);
        // }
        setIsHoveredOnDropzone(false);
    };

    const onInputChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
        const files = event.target.files;
        if (!files) return;

        if(multiple){
            setFilesArray(files);
        }
        else{
            const newFile = files[0];
            setNewFile(newFile);
        }
    };

    const onRemove = () => {
        setFile(defaultFileState);
        // onFileUrlChange("");
    };

    return (
        <>
            <div
                className={`
        flex justify-center rounded-md border-2 border-dashed border-gray-300 px-6 
        ${isHoveredOnDropzone && "border-sky-400"}
        ${playgroundView ? "pb-6 pt-4" : "py-4"}
        ${hasFile && "animate-pulse"} `

                }
                onDragOver={(event) => {
                    event.preventDefault();
                    setIsHoveredOnDropzone(true);
                }}
                onDragLeave={() => setIsHoveredOnDropzone(false)}
                onDrop={onDrop}
            >

                <div
                    className={
                        "text-center"}
                >
                    {!hasFile ? (
                        <>
                            <p className="mt-1 opacity-80 text-gray-800 dark:text-white">
                                Support: jpeg, png, webp
                            </p>
                            <p className="mt-1 opacity-80 text-gray-800 dark:text-white">
                                Max file size: 4 MB
                            </p>
                            <p className={"pt-3 text-gray-700 dark:text-gray-100"} >
                                Browse a file or drop it here
                            </p>
                            <p>Preferred Size {ratio ? `${ratio[0]}*${ratio[1]} px` : ''}</p>                        </>
                    ) : (
                        <div className="flex items-center justify-center gap-3 pt-3">

                            <div className="pl-7 font-semibold text-gray-900">
                                {file.name} - {getFileSizeString(file.size)}
                                <div className="w-full bg-gray-200 rounded-full h-1.5 mb-4 dark:bg-gray-700">
                                    <div className="bg-blue-600 h-1.5 rounded-full dark:bg-blue-500 autoProgress"></div>
                                </div>
                            </div>
                            <button
                                type="button"
                                className="outline-theme-blue rounded-md p-1 text-gray-400 hover:bg-gray-100 hover:text-gray-500"
                                title="Remove file"
                                onClick={onRemove}
                            >
                            </button>
                        </div>
                    )}
                    <div className="pt-4">
                        {!hasFile ? (
                            <>
                                <label
                                    className={
                                        "within-outline-theme-purple cursor-pointer rounded-full px-6 pb-2.5 pt-2 font-semibold shadow-sm"

                                    }
                                >
                                    Browse file
                                    <input
                                        type="file"
                                        className="sr-only"
                                        accept="image/*"
                                        onChange={onInputChange}
                                        multiple={multiple}
                                    />
                                </label>
                                {hasNonPdfFile && (
                                    <p className="mt-6 text-red-400">Only pdf file is supported</p>
                                )}
                            </>
                        ) : (
                            <></>
                        )}
                    </div>
                </div>
            </div>
            <Previews imageUrls={previews || []} />
        </>
    );
};

const getFileSizeString = (fileSizeB: number) => {
    const fileSizeKB = fileSizeB / 1024;
    const fileSizeMB = fileSizeKB / 1024;
    if (fileSizeKB < 1000) {
        return fileSizeKB.toPrecision(3) + " KB";
    } else {
        return fileSizeMB.toPrecision(3) + " MB";
    }
};