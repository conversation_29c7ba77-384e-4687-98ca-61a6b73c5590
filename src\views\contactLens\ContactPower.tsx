/*eslint-disable */
import { Button, FormItem, Input, toast } from '@/components/ui'
import Tabs from '@/components/ui/Tabs'
import { useEffect, useState } from 'react'
import Breadcrumb from '../modals/BreadCrumb'
import PowerList from './PowerList'
const { TabNav, TabList, TabContent } = Tabs

const ContactPower = () => {
  const [activeTab, setActiveTab] = useState<string>(() => {
    return localStorage.getItem('contactlensPowerActiveTab') || 'Sph'
  })

  const handleTabChange = (value: string) => {
    setActiveTab(value)
    localStorage.setItem('contactlensPowerActiveTab', value)
  }

  const breadcrumbItems = [
    { title: 'Contact Lens power', url: '' },
  ];

  return (
    <div>
      <h3 className='mb-4'>Contact Lens Power</h3>
      <Breadcrumb items={breadcrumbItems} />

      <Tabs value={activeTab} onChange={handleTabChange}>
        <TabList>
          <TabNav value="Sph">Sph</TabNav>
          <TabNav value="Cyl">Cyl</TabNav>
          <TabNav value="Axis">Axis</TabNav>
        </TabList>
        <div className="p-4">
          <TabContent value="Sph">
            <PowerList type='Sph' />
          </TabContent>

          <TabContent value="Cyl">
            <PowerList type='Cyl' />
          </TabContent>

          <TabContent value="Axis">
            <PowerList type='Axis' />
          </TabContent>
        </div>
      </Tabs>
    </div>
  )
}

export default ContactPower