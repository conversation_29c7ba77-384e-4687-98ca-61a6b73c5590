import { FormItem, Input } from '@/components/ui'
import React from 'react'
import { Controller } from 'react-hook-form'


const loginPage: { label: string, value: string, db: string }[] = [
    {
        label: "Welcome Title",
        value: "WelcomeTitle",
        db: "welcomeTitle",
    },
    {
        label: "Enter Phone Number",
        value: "EnterPhoneNumber",
        db: "enterPhoneNumber",
    },
    {
        label: "Please Wait",
        value: "PleaseWait",
        db: "pleaseWait",
    },
    {
        label: "Signup",
        value: "Signup",
        db: "signup",
    },
    {
        label: "Signup Text",
        value: "SignupTxt",
        db: "signupTxt",
    },
    {
        label: "Verify Title",
        value: "VerifyTitle",
        db: "verifyTitle",
    },
    {
        label: "Verify Text",
        value: "VerifyTxt",
        db: "verifyTxt",
    },
    {
        label: "Confirmation Code",
        value: "ConfirmationCode",
        db: "confirmationCode",
    },
    {
        label: "Didn't Receive Code?",
        value: "DidntReceiveCode",
        db: "didntReceiveCode",
    },
    {
        label: "Send Again",
        value: "SendAgain",
        db: "sendAgain",
    },
    {
        label: "Verify",
        value: "Verify",
        db: "verify",
    },
    {
        label: "Verifying",
        value: "Verifying",
        db: "verifying",
    },
    {
        label: "Account Text",
        value: "AccountTxt",
        db: "accountTxt",
    },
    {
        label: "Signup To Yateem",
        value: "SignupToYateem",
        db: "signupToYateem",
    },
    {
        label: "Continue As Guest",
        value: "ContinueAsGuest",
        db: "continueAsGuest",
    },
    {
        label: "Have Insurance?",
        value: "HaveInsurance",
        db: "haveInsurance",
    },
    {
        label: "Yes",
        value: "Yes",
        db: "yes",
    },
    {
        label: "No",
        value: "No",
        db: "no",
    },
]


export const login: { label: string, value: string, db: string }[] = [
    ...loginPage,
]

function LoginTab({ control, errors }: any) {
    return (
        <>
            <h3>Login</h3>
            {loginPage.map((item) => (
                <Forms key={item.value} control={control} errors={errors} item={item} />
            ))}
        </>
    )
}

function Forms({ item, control, errors }: any) {
    return (
        <div className="mt-2">
            <div className="grid grid-cols-2 gap-4">
                <FormItem label={`${item.label} English`}>
                    <Controller
                        control={control}
                        name={`login${item.value}En`}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {errors[`login${item.value}En`] && (
                        <small className="text-red-600 py-3">
                            {' '}
                            {errors[`login${item.value}En`].message as string}{' '}
                        </small>
                    )}
                </FormItem>
                <FormItem label={`${item.label} Arabic`}>
                    <Controller
                        control={control}
                        name={`login${item.value}Ar`}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => <Input dir='rtl' type="text" {...field} />}
                    />
                    {errors[`login${item.value}Ar`] && (
                        <small className="text-red-600 py-3">
                            {' '}
                            {errors[`login${item.value}Ar`].message as string}{' '}
                        </small>
                    )}
                </FormItem>
            </div>
        </div>
    )
}

export default LoginTab