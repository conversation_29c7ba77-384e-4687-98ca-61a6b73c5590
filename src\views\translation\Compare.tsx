import { FormItem, Input } from '@/components/ui'
import React from 'react'
import { Controller } from 'react-hook-form'

const comp: { label: string, value: string, db: string }[] = [
    {
        label: "Compare Btn",
        value: "CompareBtn",
        db: "compareBtn",
    },
    {
        label: "Remove All",
        value: "RemoveAll",
        db: "removeAll",
    },
    {
        label: "Title",
        value: "Title",
        db: "title",
    },
    {
        label: "Add Product",
        value: "AddProduct",
        db: "addProduct",
    },
    {
        label: "Sku",
        value: "Sku",
        db: "sku",
    },
    {
        label: "Price",
        value: "Price",
        db: "price",
    },
    {
        label: "Rating",
        value: "Rating",
        db: "rating"
    },
    {
        label: "Color",
        value: "Color",
        db: "color",
    },
    {
        label: "Sizes Available",
        value: "SizesAvailable",
        db: "sizesAvailable",
    },
    {
        label: "Description",
        value: "Description",
        db: "description",
    },
    // {
    //     label: "Add To Cart",
    //     value: "AddToCart",
    //     db: "addToCart",
    // },
]


export const compare: { label: string, value: string, db: string }[] = [
    ...comp
]

function Compare({ control, errors }: any) {
    return (
        <>
            <h3>Compare</h3>
            {comp.map((item) => (
                <Forms key={item.value} control={control} errors={errors} item={item} />
            ))}
        </>
    )
}

function Forms({ item, control, errors }: any) {
    return (
        <div className="mt-2">
            <div className="grid grid-cols-2 gap-4">
                <FormItem label={`${item.label} English`}>
                    <Controller
                        control={control}
                        name={`compare${item.value}En`}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {errors[`compare${item.value}En`] && (
                        <small className="text-red-600 py-3">
                            {' '}
                            {errors[`compare${item.value}En`].message as string}{' '}
                        </small>
                    )}
                </FormItem>
                <FormItem label={`${item.label} Arabic`}>
                    <Controller
                        control={control}
                        name={`compare${item.value}Ar`}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => <Input dir='rtl' type="text" {...field} />}
                    />
                    {errors[`compare${item.value}Ar`] && (
                        <small className="text-red-600 py-3">
                            {' '}
                            {errors[`compare${item.value}Ar`].message as string}{' '}
                        </small>
                    )}
                </FormItem>
            </div>
        </div>
    )
}

export default Compare