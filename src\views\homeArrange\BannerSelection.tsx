
/* eslint-disable */
import { Button } from '@/components/ui';
import SelectionItem from './SelectionItem'; // Import the SelectionItem component

interface BannerSelectionProps {
    banners: any[];
    selectedBanner: string;
    onBannerChange: (bannerId: string) => void;
    onSave: any;
}

const baseUrl = import.meta.env.VITE_ASSET_URL;

const BannerSelection = ({ banners, selectedBanner, onBannerChange, onSave }: BannerSelectionProps) => (
    <div className="mt-5 max-h-full overflow-y-auto">
        <div className="flow-root">
            <h5>Select Banner</h5>
            <div className="grid grid-cols-3 gap-2 mb-2">
                {banners.map((banner, index) => (
                    <SelectionItem
                        key={index}
                        id={banner._id}
                        label={banner.title}
                        image={`${baseUrl}${banner.files[0]?.file}`} // Adjust the image source based on your data structure
                        checked={selectedBanner === banner._id}
                        onChange={() => onBannerChange(banner._id)}
                    />
                ))}
            </div>
            <Button variant="solid" className="mt-4 float-right" onClick={onSave}>
                Save
            </Button>
        </div>
    </div>
);

export default BannerSelection;