import { FormItem, Input } from '@/components/ui'
import { Controller } from 'react-hook-form'

const home: { label: string, value: string, db: string }[] = [
    {
        label: "Coming Soon Title",
        value: "ComingSoonTitle",
        db: "comingSoonTitle"
    },
    {
        label: "Coming Soon Text",
        value: "ComingSoonText",
        db: "comingSoonText"
    },
    {
        label: "Coming Soon Btn",
        value: "ComingSoonBtn",
        db: "comingSoonBtn"
    },
]

export const homeTry: { label: string, value: string, db: string }[] = [
    ...home
]

function HomeTry({ control, errors }: any) {
    return (
        <>
            <h3>Home Try</h3>
            {home.map((item) => (
                <Forms key={item.value} control={control} errors={errors} item={item} />
            ))}
        </>
    )
}

function Forms({ item, control, errors }: any) {
    return (
        <div className="mt-2">
            <div className="grid grid-cols-2 gap-4">
                <FormItem label={`${item.label} English`}>
                    <Controller
                        control={control}
                        name={`homeTry${item.value}En`}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {errors[`homeTry${item.value}En`] && (
                        <small className="text-red-600 py-3">
                            {errors[`homeTry${item.value}En`].message as string}{' '}
                        </small>
                    )}
                </FormItem>
                <FormItem label={`${item.label} Arabic`}>
                    <Controller
                        control={control}
                        name={`homeTry${item.value}Ar`}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => <Input dir='rtl' type="text" {...field} />}
                    />
                    {errors[`homeTry${item.value}Ar`] && (
                        <small className="text-red-600 py-3">
                            {errors[`homeTry${item.value}Ar`].message as string}{' '}
                        </small>
                    )}
                </FormItem>
            </div>
        </div>
    )
}

export default HomeTry