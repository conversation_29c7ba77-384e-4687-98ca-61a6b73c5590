import type { ThemeConfiguratorProps } from '@/components/template/ThemeConfigurator'
import ModeSwitcher from '../ThemeConfigurator/ModeSwitcher'

export type SidePanelContentProps = ThemeConfiguratorProps

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const SidePanelContent = (props: SidePanelContentProps) => {
    return (
        <div>
            <ModeSwitcher />
        </div>
    )
}

export default SidePanelContent
