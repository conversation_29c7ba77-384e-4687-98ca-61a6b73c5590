import { RichTextEditor } from '@/components/shared'
import React from 'react'

function ManageContent({ pageSection, handleContentChange }: any) {
    function onChangeEn(value: any) {
        if (pageSection?.content?.en != value) handleContentChange(value, pageSection?.id, "content.en")
    }
    function onChangeAr(value: any) {
        if (pageSection?.content?.ar != value) handleContentChange(value, pageSection?.id, "content.ar")
    }
    return (
        <>
            <h5>Content Section</h5>
            <p className="mt-2 mb-2">English</p>
            <RichTextEditor
                key={pageSection?.id + "en"}
                value={pageSection?.content?.en}
                onChange={onChangeEn}
            />
            <p className="mt-2 mb-2">Arabic</p>
            <RichTextEditor
                key={pageSection?.id + "ar"}
                value={pageSection?.content?.ar}
                onChange={onChangeAr}
            />
        </>
    )
}

export default ManageContent