/*eslint-disable */
import { Button, FormItem, Input, toast } from '@/components/ui'
import Tabs from '@/components/ui/Tabs'
import endpoints from '@/endpoints'
import api from '@/services/api.interceptor'
import { useEffect, useState } from 'react'
import { Controller, useFieldArray, useForm } from 'react-hook-form'
import { AiOutlinePlus, AiOutlineSave } from 'react-icons/ai'
import { IoIosCloseCircleOutline } from 'react-icons/io'
import Notification from '@/components/ui/Notification'
import Breadcrumb from '../modals/BreadCrumb'

const { TabNav, TabList, TabContent } = Tabs

const Faq = () => {
    const [isUpdate, setIsUpdate] = useState(false)
    const [activeTab, setActiveTab] = useState<any>('insurance');
    const [refid, setRefid] = useState();

    const breadcrumbItems = [
        { title: 'Faq', url: '' },
    ]

    const {
        control: insuranceControl,
        handleSubmit: insuranceHandleSubmit,
        setValue: insuranceSetValue,
        formState: { errors: insuranceErrors },
    } = useForm<any>();

    const {
        control: contactLensControl,
        handleSubmit: contactLensHandleSubmit,
        setValue: contactLensSetValue,
        formState: { errors: contactLensErrors },
    } = useForm<any>()

    const {
        fields: insurancefaqFields,
        append: insurancefaqAppend,
        remove: insurancefaqRemove,
    } = useFieldArray({
        control: insuranceControl,
        name: 'insuranceFaq',
    })

    const {
        fields: contactLensfaqFields,
        append: contactLensfaqAppend,
        remove: contactLensfaqRemove,
    } = useFieldArray({
        control: contactLensControl,
        name: 'contactLensFaq',
    })

    const onSubmit = (data: any) => {
        if (activeTab === 'insurance') {
            handleSubmitForType('insurance', data, 'insuranceFaq');
        } else if (activeTab === 'contactLens') {
            handleSubmitForType('contactLens', data, 'contactLensFaq');
        }
    };

    const handleSubmitForType = (type: 'insurance' | 'contactLens', data: any, formArrayName: string) => {
        console.log(`Submitting ${type} data:`, data);
        const payload: any = {
            type: type,
            title: {
                en: data.titleEn,
                ar: data.titleAr
            },
            description: {
                en: data.descEn,
                ar: data.descAr
            },
            faq: data[formArrayName].map((faq: any) => {
                return {
                    question: {
                        en: faq.questionEn,
                        ar: faq.questionAr
                    },
                    answer: {
                        en: faq.answerEn,
                        ar: faq.answerAr
                    }
                }
            })
        }
        console.log('payload', payload);

        if (isUpdate) {
            payload['refid'] = refid
            api.post(endpoints.updateFaq, payload).then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification type="success" title={res.data.message} />, {
                        placement: 'top-center',
                    })
                    getFaq(type, insuranceSetValue, setIsUpdate, formArrayName)
                }
            }).catch((error) => {
                console.error('Error fetching data: ', error)
            })
        } else {
            api.post(endpoints.createFaq, payload).then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification type="success" title={res.data.message} />, {
                        placement: 'top-center',
                    })
                    getFaq(type, insuranceSetValue, setIsUpdate, formArrayName)
                }
            }).catch((error) => {
                console.error('Error fetching data: ', error)
            })
        }
    };

    const getFaq = (type: 'insurance' | 'contactLens', setValues: Function, setIsUpdate: Function, formArrayName: string) => {
        api.get(`${endpoints.faqs}${type}`).then((res) => {
            if (res?.status === 200 && res?.data?.result.length > 0) {
                const data = res?.data?.result[0];
                setRefid(data.refid);
                setValues('type', { value: data.type, label: data.type });
                setValues('titleEn', data.title.en);
                setValues('titleAr', data.title.ar);
                setValues('descEn', data.description.en || '');
                setValues('descAr', data.description.ar || '');
                setValues(formArrayName, data.faq.map((item: any) => ({
                    questionEn: item.question.en,
                    questionAr: item.question.ar,
                    answerEn: item.answer.en,
                    answerAr: item.answer.ar
                })));
                setIsUpdate(true);
            } else {
                setIsUpdate(false);
            }
        }).catch((error) => {
            console.error('Error fetching data: ', error);
        });
    };

    useEffect(() => {
        if (activeTab === 'insurance') {
            getFaq('insurance', insuranceSetValue, setIsUpdate, 'insuranceFaq');
        } else if (activeTab === 'contactLens') {
            getFaq('contactLens', contactLensSetValue, setIsUpdate, 'contactLensFaq');
        }
    }, [activeTab, insuranceSetValue, contactLensSetValue, setIsUpdate]);

    return (
        <div>
            <h3 className='mb-4'>{isUpdate ? 'Update' : 'Create'} Faq</h3>
            <Breadcrumb items={breadcrumbItems} />
            <Tabs defaultValue="insurance" onChange={(value) => setActiveTab(value)}>
                <TabList>
                    <TabNav value="insurance">Insurance FAQ</TabNav>
                    <TabNav value="contactLens">Contact Lens FAQ</TabNav>
                </TabList>
                <div className="p-4">
                    <TabContent value="insurance">
                        <form onSubmit={insuranceHandleSubmit(onSubmit)}>
                            <h4 className='mb-4'>Title</h4>
                            <div className='grid grid-cols-2 gap-4'>
                                <FormItem label='English'>
                                    <Controller
                                        control={insuranceControl}
                                        name="titleEn"
                                        defaultValue=""
                                        rules={{ required: 'Title is required' }}
                                        render={({ field }) => (
                                            <Input
                                                type="text"
                                                className={`${insuranceErrors.titleEn ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                                                {...field}
                                            />
                                        )}
                                    />
                                    {insuranceErrors.titleEn && (
                                        <small className="text-red-600 py-3">
                                            {insuranceErrors.titleEn.message as string}
                                        </small>
                                    )}
                                </FormItem>

                                <FormItem label='Arabic'>
                                    <Controller
                                        control={insuranceControl}
                                        name="titleAr"
                                        defaultValue=""
                                        // rules={{ required: 'Title is required' }}
                                        render={({ field }) => (
                                            <Input
                                                type="text"
                                                dir='rtl'
                                                // className={`${insuranceErrors.titleAr ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                                                {...field}
                                            />
                                        )}
                                    />
                                    {/* {insuranceErrors.titleAr && (
                                        <small className="text-red-600 py-3">
                                            {insuranceErrors.titleAr.message as string}
                                        </small>
                                    )} */}

                                </FormItem>
                            </div>

                            <h4 className='mb-4'>Description</h4>
                            <div className='grid grid-cols-2 gap-4'>
                                <FormItem label='English'>
                                    <Controller
                                        control={insuranceControl}
                                        name="descEn"
                                        defaultValue=""
                                        rules={{ required: 'Description is required' }}
                                        render={({ field }) => (
                                            <Input
                                                textArea
                                                className={`${insuranceErrors.descEn ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                                                {...field}
                                            />
                                        )}
                                    />
                                    {insuranceErrors.descEn && (
                                        <small className="text-red-600 py-3">
                                            {insuranceErrors.descEn.message as string}
                                        </small>
                                    )}
                                </FormItem>

                                <FormItem label='Arabic'>
                                    <Controller
                                        control={insuranceControl}
                                        name="descAr"
                                        defaultValue=""
                                        // rules={{ required: 'Description is required' }}
                                        render={({ field }) => (
                                            <Input
                                                type="text"
                                                dir='rtl'
                                                textArea
                                                // className={`${insuranceErrors.descAr ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                                                {...field}
                                            />
                                        )}
                                    />
                                    {/* {insuranceErrors.descAr && (
                                        <small className="text-red-600 py-3">
                                            {insuranceErrors.descAr.message as string}
                                        </small>
                                    )} */}
                                </FormItem>
                            </div>

                            <ul className="mt-4">
                                {insurancefaqFields.map((item, index) => {
                                    return (
                                        <li key={item.id} className="border-2 rounded-md border-gray-400 mt-2 py-6 px-3 relative">
                                            <div className="grid grid-cols-2 gap-4">
                                                <FormItem label="Question(En)">
                                                    <Controller
                                                        name={`insuranceFaq.${index}.questionEn`}
                                                        control={insuranceControl}
                                                        defaultValue={''}
                                                        rules={{ required: 'Question is required' }}
                                                        render={({ field }) => (
                                                            <Input
                                                                type="text"
                                                                className={`${insuranceErrors.questionEn
                                                                    ? ' input input-md h-11 input-invalid'
                                                                    : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                                                    }`}
                                                                {...field}
                                                            />
                                                        )}
                                                    />
                                                    {insuranceErrors.insuranceFaq && Array.isArray(insuranceErrors.insuranceFaq) && insuranceErrors.insuranceFaq[index]?.questionEn && (
                                                        <small className="text-red-600 py-3">
                                                            {insuranceErrors.insuranceFaq[index].questionEn.message}
                                                        </small>
                                                    )}
                                                </FormItem>

                                                <FormItem label="Question(Ar)">
                                                    <Controller
                                                        name={`insuranceFaq.${index}.questionAr`}
                                                        control={insuranceControl}
                                                        defaultValue={''}
                                                        // rules={{ required: 'Question is required' }}
                                                        render={({ field }) => (
                                                            <Input dir='rtl' type="text" {...field} />
                                                        )}
                                                    />
                                                    {/* {insuranceErrors.insuranceFaq && Array.isArray(insuranceErrors.insuranceFaq) && insuranceErrors.insuranceFaq[index]?.questionAr && (
                                                        <small className="text-red-600 py-3">
                                                            {insuranceErrors.insuranceFaq[index].questionAr.message}
                                                        </small>
                                                    )} */}
                                                </FormItem>
                                            </div>

                                            <div className="grid grid-cols-2 gap-4">
                                                <FormItem label="Answer(En)">
                                                    <Controller
                                                        name={`insuranceFaq.${index}.answerEn`}
                                                        control={insuranceControl}
                                                        defaultValue={''}
                                                        rules={{ required: 'Answer is required' }}
                                                        render={({ field }) => (
                                                            <Input type="text" {...field} />
                                                        )}
                                                    />
                                                    {insuranceErrors.insuranceFaq && Array.isArray(insuranceErrors.insuranceFaq) && insuranceErrors.insuranceFaq[index]?.answerEn && (
                                                        <small className="text-red-600 py-3">
                                                            {insuranceErrors.insuranceFaq[index].answerEn.message}
                                                        </small>
                                                    )}
                                                </FormItem>

                                                <FormItem label="Answer(Ar)">
                                                    <Controller
                                                        name={`insuranceFaq.${index}.answerAr`}
                                                        control={insuranceControl}
                                                        defaultValue={''}
                                                        // rules={{ required: 'Answer is required' }}
                                                        render={({ field }) => (
                                                            <Input dir='rtl' type="text" {...field} />
                                                        )}
                                                    />
                                                    {/* {insuranceErrors.insuranceFaq && Array.isArray(insuranceErrors.insuranceFaq) && insuranceErrors.insuranceFaq[index]?.answerAr && (
                                                        <small className="text-red-600 py-3">
                                                            {insuranceErrors.insuranceFaq[index].answerAr.message}
                                                        </small>
                                                    )} */}
                                                </FormItem>

                                            </div>

                                            <div className="absolute right-0 top-0">
                                                <IoIosCloseCircleOutline size={30} color="red" onClick={() => insurancefaqRemove(index)} />
                                            </div>
                                        </li>
                                    )
                                })}
                            </ul>
                            <Button
                                className='my-3'
                                variant="solid"
                                type="button"
                                icon={<AiOutlinePlus />}
                                onClick={() => {
                                    insurancefaqAppend({})
                                }
                                } >
                                Add Faq
                            </Button>

                            <Button
                                type="submit"
                                variant="solid"
                                icon={<AiOutlineSave />}
                                className="mt-14 block float-right "
                            >
                                {isUpdate ? 'Update' : 'Save'}
                            </Button>
                        </form>
                    </TabContent>

                    <TabContent value="contactLens">
                        <form onSubmit={contactLensHandleSubmit(onSubmit)}>
                            <h4 className='mb-4'>Title</h4>
                            <div className='grid grid-cols-2 gap-4'>
                                <FormItem label='English'>
                                    <Controller
                                        control={contactLensControl}
                                        name="titleEn"
                                        defaultValue=""
                                        rules={{ required: 'Title is required' }}
                                        render={({ field }) => (
                                            <Input
                                                type="text"
                                                className={`${contactLensErrors.titleEn ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                                                {...field}
                                            />
                                        )}
                                    />
                                    {contactLensErrors.titleEn && (
                                        <small className="text-red-600 py-3">
                                            {contactLensErrors.titleEn.message as string}
                                        </small>
                                    )}
                                </FormItem>

                                <FormItem label='Arabic'>
                                    <Controller
                                        control={contactLensControl}
                                        name="titleAr"
                                        defaultValue=""
                                        // rules={{ required: 'Title is required' }}
                                        render={({ field }) => (
                                            <Input
                                                type="text"
                                                dir='rtl'
                                                // className={`${contactLensErrors.titleAr ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                                                {...field}
                                            />
                                        )}
                                    />
                                    {contactLensErrors.titleAr && (
                                        <small className="text-red-600 py-3">
                                            {contactLensErrors.titleAr.message as string}
                                        </small>
                                    )}
                                </FormItem>
                            </div>

                            <h4 className='mb-4'>Description</h4>
                            <div className='grid grid-cols-2 gap-4'>
                                <FormItem label='English'>
                                    <Controller
                                        control={contactLensControl}
                                        name="descEn"
                                        defaultValue=""
                                        rules={{ required: 'Description is required' }}
                                        render={({ field }) => (
                                            <Input
                                                textArea
                                                className={`${contactLensErrors.descEn ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                                                {...field}
                                            />
                                        )}
                                    />
                                    {contactLensErrors.descEn && (
                                        <small className="text-red-600 py-3">
                                            {contactLensErrors.descEn.message as string}
                                        </small>
                                    )}
                                </FormItem>

                                <FormItem label='Arabic'>
                                    <Controller
                                        control={contactLensControl}
                                        name="descAr"
                                        defaultValue=""
                                        // rules={{ required: 'Description is required' }}
                                        render={({ field }) => (
                                            <Input
                                                type="text"
                                                dir='rtl'
                                                textArea
                                                // className={`${contactLensErrors.descAr ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                                                {...field}
                                            />
                                        )}
                                    />
                                    {/* {contactLensErrors.descAr && (
                                        <small className="text-red-600 py-3">
                                            {contactLensErrors.descAr.message as string}
                                        </small>
                                    )} */}
                                </FormItem>
                            </div>

                            <ul className="mt-4">
                                {contactLensfaqFields.map((item, index) => {
                                    return (
                                        <li key={item.id} className="border-2 rounded-md border-gray-400 mt-2 py-6 px-3 relative">
                                            <div className="grid grid-cols-2 gap-4">
                                                <FormItem label="Question(En)">
                                                    <Controller
                                                        name={`contactLensFaq.${index}.questionEn`}
                                                        control={contactLensControl}
                                                        defaultValue={''}
                                                        rules={{ required: 'Question is required' }}
                                                        render={({ field }) => (
                                                            <Input
                                                                type="text"
                                                                className={`${contactLensErrors.questionEn
                                                                    ? ' input input-md h-11 input-invalid'
                                                                    : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                                                    }`}
                                                                {...field}
                                                            />
                                                        )}
                                                    />
                                                    {contactLensErrors.contactLensFaq && Array.isArray(contactLensErrors.contactLensFaq) && contactLensErrors.contactLensFaq[index]?.questionEn && (
                                                        <small className="text-red-600 py-3">
                                                            {contactLensErrors.contactLensFaq[index].questionEn.message}
                                                        </small>
                                                    )}
                                                </FormItem>

                                                <FormItem label="Question(Ar)">
                                                    <Controller
                                                        name={`contactLensFaq.${index}.questionAr`}
                                                        control={contactLensControl}
                                                        defaultValue={''}
                                                        // rules={{ required: 'Question is required' }}
                                                        render={({ field }) => (
                                                            <Input dir='rtl' type="text" {...field} />
                                                        )}
                                                    />
                                                    {/* {contactLensErrors.contactLensFaq && Array.isArray(contactLensErrors.contactLensFaq) && contactLensErrors.contactLensFaq[index]?.questionAr && (
                                                        <small className="text-red-600 py-3">
                                                            {contactLensErrors.contactLensFaq[index].questionAr.message}
                                                        </small>
                                                    )} */}
                                                </FormItem>
                                            </div>

                                            <div className="grid grid-cols-2 gap-4">
                                                <FormItem label="Answer(En)">
                                                    <Controller
                                                        name={`contactLensFaq.${index}.answerEn`}
                                                        control={contactLensControl}
                                                        defaultValue={''}
                                                        rules={{ required: 'Answer is required' }}
                                                        render={({ field }) => (
                                                            <Input type="text" {...field} />
                                                        )}
                                                    />
                                                    {contactLensErrors.contactLensFaq && Array.isArray(contactLensErrors.contactLensFaq) && contactLensErrors.contactLensFaq[index]?.answerEn && (
                                                        <small className="text-red-600 py-3">
                                                            {contactLensErrors.contactLensFaq[index].answerEn.message}
                                                        </small>
                                                    )}
                                                </FormItem>

                                                <FormItem label="Answer(Ar)">
                                                    <Controller
                                                        name={`contactLensFaq.${index}.answerAr`}
                                                        control={contactLensControl}
                                                        defaultValue={''}
                                                        // rules={{ required: 'Answer is required' }}
                                                        render={({ field }) => (
                                                            <Input dir='rtl' type="text" {...field} />
                                                        )}
                                                    />
                                                    {/* {contactLensErrors.contactLensFaq && Array.isArray(contactLensErrors.contactLensFaq) && contactLensErrors.contactLensFaq[index]?.answerAr && (
                                                        <small className="text-red-600 py-3">
                                                            {contactLensErrors.contactLensFaq[index].answerAr.message}
                                                        </small>
                                                    )} */}
                                                </FormItem>

                                            </div>

                                            <div className="absolute right-0 top-0">
                                                <IoIosCloseCircleOutline size={30} color="red" onClick={() => contactLensfaqRemove(index)} />
                                            </div>
                                        </li>
                                    )
                                })}
                            </ul>
                            <Button
                                className='my-3'
                                variant="solid"
                                type="button"
                                icon={<AiOutlinePlus />}
                                onClick={() => {
                                    contactLensfaqAppend({})
                                }
                                } >
                                Add Faq
                            </Button>

                            <Button
                                type="submit"
                                variant="solid"
                                icon={<AiOutlineSave />}
                                className="mt-14 block float-right "
                            >
                                {isUpdate ? 'Update' : 'Save'}
                            </Button>
                        </form>
                    </TabContent>
                </div>
            </Tabs>
        </div>
    )
}

export default Faq