import { FormItem, Input } from '@/components/ui'
import React from 'react'
import { Controller } from 'react-hook-form'

const productPageSection: { label: string, value: string, db: string }[] = [
    {
        label: 'Compare',
        value: 'Compare',
        db: 'compare'
    },
    {
        label: 'Inc',
        value: 'Inc',
        db: 'inc'
    },
    {
        label: 'Color',
        value: 'Color',
        db: 'color'
    },
    {
        label: 'Name',
        value: 'Name',
        db: 'name'
    },
    {
        label: 'Brand',
        value: 'Brand',
        db: 'brand'
    },
    {
        label: 'Gender',
        value: 'Gender',
        db: 'gender'
    },
    {
        label: 'Men',
        value: 'Men',
        db: 'men'
    },
    {
        label: 'Women',
        value: 'Women',
        db: 'women'
    },
    {
        label: 'Kids',
        value: 'Kids',
        db: 'kids'
    },
    {
        label: 'Unisex',
        value: 'Unisex',
        db: 'unisex'
    },
    {
        label: 'Size',
        value: 'Size',
        db: 'size'
    },
    {
        label: 'Add To Cart',
        value: 'AddToCart',
        db: 'addToCart'
    },
    {
        label: 'Out of stock',
        value: 'OutOfStock',
        db: 'outOfStock'
    },
    {
        label: 'Product Description',
        value: 'ProductDescription',
        db: 'productDescription'
    },
    {
        label: 'Technical Info',
        value: 'TechnicalInfo',
        db: 'technicalInfo'
    },
    {
        label: 'Contact Lens Power Title',
        value: 'ContactLensPowerTitle',
        db: 'contactLensPowerTitle'
    },
    {
        label: 'Contact Lens Check Box',
        value: 'ContactLensCheckBox',
        db: 'contactLensCheckBox'
    },
    {
        label: 'Sphere',
        value: 'Sphere',
        db: 'sphere'
    },
    {
        label: 'Axis',
        value: 'Axis',
        db: 'axis'
    },
    {
        label: 'Cyl',
        value: 'Cyl',
        db: 'cyl'
    },
    {
        label: 'Addition',
        value: 'Addition',
        db: 'addition'
    },
    {
        label: 'Subscription',
        value: 'Subscription',
        db: 'subscription'
    },
    {
        label: 'Frequently Bought With',
        value: 'FrequentlyBoughtWith',
        db: 'frequentlyBoughtWith'
    },
    {
        label: 'Recommended Products',
        value: 'RecommendedProducts',
        db: 'recommendedProducts'
    },
    {
        label: 'Left Eye',
        value: 'LeftEye',
        db: 'leftEye'
    },
    {
        label: 'Right Eye',
        value: 'RightEye',
        db: 'rightEye'
    },
    {
        label: 'Saving',
        value: 'Saving',
        db: 'saving'
    },
    {
        label: 'Total Price Including Power',
        value: 'TotalPriceIncludingPower',
        db: 'totalPriceIncludingPower'
    },
    {
        label: 'Select',
        value: 'Select',
        db: 'select'
    },
    {
        label: 'Choose Lens',
        value: 'ChooseLens',
        db: 'chooseLens'
    },
    {
        label: "Pd",
        value: "Pd",
        db: "pd",
    },
    {
        label: "I Know Pd",
        value: "IKnowPd",
        db: "iKnowPd",
    },
    {
        label: "I Dont Know Pd",
        value: "IDontKnowPd",
        db: "iDontKnowPd",
    },
]

const buyWithSection: { label: string, value: string, db: string }[] = [
    {
        label: 'Buy with Lens Title',
        value: 'BuyWithLensTitle',
        db: 'buyWithLensTitle'
    },
    {
        label: 'Buy With Lens Text',
        value: 'BuyWithLensText',
        db: 'buyWithLensText'
    },
    {
        label: 'Do You Want Your Brand',
        value: 'DoYouWantYourBrand',
        db: 'doYouWantYourBrand'
    },
    {
        label: 'Single Vision Title',
        value: 'SingleVisionTitle',
        db: 'singleVisionTitle'
    },
    {
        label: 'Single Vision Description',
        value: 'SingleVisionDescription',
        db: 'singleVisionDescription'
    },
    {
        label: 'Progressive Title',
        value: 'ProgressiveTitle',
        db: 'progressiveTitle'
    },
    {
        label: 'Progressive Description',
        value: 'ProgressiveDescription',
        db: 'progressiveDescription'
    },
    {
        label: 'Frame Only Title',
        value: 'FrameOnlyTitle',
        db: 'frameOnlyTitle'
    },
    {
        label: 'Frame Only Description',
        value: 'FrameOnlyDescription',
        db: 'frameOnlyDescription'
    },
    {
        label: 'Next',
        value: 'Next',
        db: 'next'
    },
    {
        label: "Upload Prescription",
        value: "UploadPrescription",
        db: "uploadPrescription",
    },
    {
        label: "Upload Photo",
        value: "UploadPhoto",
        db: "uploadPhoto",
    },
    {
        label: "Upload Photo Text",
        value: "UploadPhotoTxt",
        db: "uploadPhotoTxt",
    },
    {
        label: "Enter Manually",
        value: "EnterManually",
        db: "enterManually",
    },
    {
        label: "Enter Manually Text",
        value: "EnterManuallyTxt",
        db: "enterManuallyTxt",
    },
    {
        label: "Check Prescription",
        value: "CheckPrescription",
        db: "checkPrescription",
    },
    {
        label: "File Size",
        value: "FileSize",
        db: "fileSize",
    },
    {
        label: "File Type",
        value: "FileType",
        db: "fileType",
    },
    {
        label: "User Details",
        value: "UserDetails",
        db: "userDetails",
    },
    {
        label: "Enquiry Success",
        value: "EnquirySuccess",
        db: "enquirySuccess",
    },
    {
        label: "Continue",
        value: "Continue",
        db: "continue",
    },
]

export const productPage: { label: string, value: string, db: string }[] = [
    ...productPageSection,
    ...buyWithSection
]


function ProductPageTab({ control, errors }: any) {
    return (
        <>
            <h3>Product Page</h3>
            {productPageSection.map((item, index) => (
                <Forms key={index + item.value} item={item} control={control} errors={errors} />
            ))}
            <h4>Buy with lens</h4>
            {buyWithSection.map((item, index) => (
                <Forms key={index + item.value} item={item} control={control} errors={errors} />
            ))}
        </>
    )
}

function Forms({ item, control, errors }: any) {
    return (
        <div className="mt-2">
            <div className="grid grid-cols-2 gap-4">
                <FormItem label={`${item.label} English`}>
                    <Controller
                        control={control}
                        name={`productPage${item.value}En`}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {errors[`productPage${item.value}En`] && (
                        <small className="text-red-600 py-3">
                            {' '}
                            {errors[`productPage${item.value}En`].message as string}{' '}
                        </small>
                    )}
                </FormItem>
                <FormItem label={`${item.label} Arabic`}>
                    <Controller
                        control={control}
                        name={`productPage${item.value}Ar`}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => <Input dir='rtl' type="text" {...field} />}
                    />
                    {errors[`productPage${item.value}Ar`] && (
                        <small className="text-red-600 py-3">
                            {' '}
                            {errors[`productPage${item.value}Ar`].message as string}{' '}
                        </small>
                    )}
                </FormItem>
            </div>
        </div>
    )
}
export default ProductPageTab