import { But<PERSON>, <PERSON>I<PERSON>, toast, Select, FormContainer, Upload, Dialog } from "@/components/ui";
import endpoints from "@/endpoints";
import api from "@/services/api.interceptor";
import { Controller, useFieldArray, useForm } from "react-hook-form";
import { AiOutlineMinus, AiOutlinePlus, AiOutlineSave } from "react-icons/ai";
import Notification from '@/components/ui/Notification'
import { useNavigate, useParams } from "react-router-dom";
import Input from '@/components/ui/Input'
import InputGroup from '@/components/ui/InputGroup'
import TimeInput from '@/components/ui/TimeInput'
import { useEffect, useState } from "react";
import Breadcrumb from "../modals/BreadCrumb";
import { PiPlusBold } from "react-icons/pi";

const imageUrl = import.meta.env.VITE_ASSET_URL

/* eslint-disable */
export default function AddStore() {
    const navigate = useNavigate()
    const params = useParams()
    const [imageFile, setImageFile] = useState<any>([])
    const [loading, setLoading] = useState(false)
    const [showPopup, setShowPopup] = useState(false)
    const [filter, setFilter] = useState('')
    const [services, setServices] = useState<any>([])
    const [facilities, setFacilities] = useState<any>([])
    const [stores, setStores] = useState<any>([])
    const [brands, setBrands] = useState<any>([])
    const [insurance, setInsurance] = useState<any>([])
    const [filterValue, setFilterValue] = useState('')

    const breadcrumbItems = [
        { title: 'Stores', url: '/stores' },
        { title: params?.id ? 'Update Store' : 'Create Store', url: '' },
    ];

    const options: any = [
        { value: "+91", label: "+91" },
        { value: "+971", label: "+971" },
        { value: "+966", label: "+966" },
        { value: "+968", label: "+968" },
        { value: "+974", label: "+974" },
        { value: "+965", label: "+965" },
    ]

    const activeOptions: any = [
        { value: "true", label: "True" },
        { value: "false", label: "False" },
    ]

    const storeOptions: any = [
        { value: "Open", label: "Open" },
        { value: "Closed", label: "Closed" },
        { value: "Temporarily Closed", label: "Temporarily Closed" },
    ]

    const countries: any = [
        { value: 'UAE', label: 'UAE' },
        { value: 'Qatar', label: 'Qatar' },
        { value: 'Oman', label: 'Oman' },
        { value: 'Bahrain', label: 'Bahrain' },
        { value: 'Saudi Arabia', label: 'Saudi Arabia' },
    ]

    // const services: any = [
    //     { value: 'Abu Dhabi Driving License Services', label: 'Abu Dhabi Driving License Services' },
    //     { value: 'Eye Test', label: 'Eye Test' },
    //     { value: 'RTA Driving License Services', label: 'RTA Driving License Services' },
    //     { value: 'Sharjah Driving License Services', label: 'Sharjah Driving License Services' },
    // ]

    // const stores: any = [
    //     { value: 'Occhiali Optics', label: 'Occhiali Optics' },
    //     { value: 'Sun Eye Optics', label: 'Sun Eye Optics' },
    //     { value: 'Yateem Optician', label: 'Yateem Optician' },
    //     { value: 'Yateem Optics', label: 'Yateem Optics' },
    // ]

    const {
        handleSubmit,
        setValue,
        control,
        formState: { errors },
        getValues,
    } = useForm<any>();

    const {
        fields,
        append,
        remove,
    } = useFieldArray({
        control,
        name: 'officeHours',
    })

    const fetchBrands = () => {
        api.get(endpoints.brands).then((res) => {
            if (res?.status == 200) {
                const brandOptions = res.data.result.map((brand: any) => ({
                    value: brand._id,
                    label: brand.name.en
                }))
                setBrands(brandOptions)
            }
        }).catch((error) => {
            console.error('Error fetching brands: ', error)
        })
    }

    const fetchInsurance = () => {
        api.get(endpoints.providers).then((res) => {
            if (res?.status == 200) {
                const insuranceOptions = res.data.result.map((provider: any) => ({
                    value: provider._id,
                    label: provider.name.en
                }))
                setInsurance(insuranceOptions)
            }
        }).catch((error) => {
            console.error('Error fetching insurance providers: ', error)
        })
    }

    const fetchStoreFilters = () => {
        api.get(endpoints.storeFilters).then((res) => {
            if (res?.status == 200) {
                const data = res.data.result

                // Update services options
                if (data.services) {
                    const serviceOptions = data.services.map((service: any) => ({
                        value: service,
                        label: service
                    }))
                    setServices(serviceOptions)
                }

                // Update facilities options
                if (data.facilities) {
                    const facilityOptions = data.facilities.map((facility: any) => ({
                        value: facility,
                        label: facility
                    }))
                    setFacilities(facilityOptions)
                }

                // Update stores options
                if (data.store) {
                    const storeOptions = data.store.map((store: any) => ({
                        value: store,
                        label: store
                    }))
                    setStores(storeOptions)
                }
            }
        }).catch((error) => {
            console.error('Error fetching store filters: ', error)
        })
    }

    useEffect(() => {
        fetchBrands()
        fetchInsurance()
        fetchStoreFilters()

        if (params.id)
            api.get(endpoints.storeDetail + params.id).then((res) => {
                if (res?.status == 200) {
                    console.log(res.data);
                    setValue('nameEn', res.data.result.name.en)
                    setValue('nameAr', res.data.result.name.ar)
                    setValue('email', res.data.result.email)
                    setValue('location', res?.data?.result?.location)
                    setValue('mobile', res.data.result.mobile)
                    setValue('addressEn', res.data.result.address.en)
                    setValue('addressAr', res.data.result.address.ar)
                    if (res.data?.result?.country) setValue('country', { value: res.data?.result?.country, label: res.data?.result?.country })
                    if (res.data?.result?.store) setValue('store', { value: res.data?.result?.store, label: res.data?.result?.store })
                    if (res.data.result.services?.length > 0) setValue('services', res.data.result.services.map((item: any) => ({ value: item, label: item })))
                    if (res.data.result.facilities?.length > 0) setValue('facilities', res.data.result.facilities.map((item: any) => ({ value: item, label: item })))
                    if (res.data.result.brands?.length > 0) setValue('brands', res.data.result.brands.map((item: any) => ({ value: item._id || item, label: item.name?.en || item })))
                    if (res.data.result.insurance?.length > 0) setValue('insurance', res.data.result.insurance.map((item: any) => ({ value: item._id || item, label: item.name?.en || item })))
                    if (res?.data?.result?.status) setValue('status', { value: res.data.result.status, label: res.data.result.status })
                    setValue('latitude', res.data.result.coordinates.lat)
                    setValue('longitude', res.data.result.coordinates.long)
                    setValue('countryCode', { value: res.data.result.countryCode, label: res.data.result.countryCode })
                    setValue('isActive', { value: res.data.result.isActive, label: res.data.result.isActive == true ? 'True' : 'False' })
                    setValue('isClickAndCollect', res.data?.result?.isClickAndCollect ? activeOptions[0] : activeOptions[1])
                    // if (res?.data?.result?.officeHours && res?.data?.result?.officeHours.length > 0) {
                    //     const updatedOfficeHours = res.data.result.officeHours.map((item: any) => ({
                    //         open: new Date(item.open),
                    //         close: new Date(item.close),
                    //         day: item.day
                    //     }));
                    //     setValue('officeHours', updatedOfficeHours);
                    // }
                    setImageFile([imageUrl + res.data?.result?.thumbnail])
                    // setServices([
                    //     { value: 'Abu Dhabi Driving License Services', label: 'Abu Dhabi Driving License Services' },
                    //     { value: 'Eye Test', label: 'Eye Test' },
                    //     { value: 'RTA Driving License Services', label: 'RTA Driving License Services' },
                    //     { value: 'Sharjah Driving License Services', label: 'Sharjah Driving License Services' },
                    // ])
                    // setFacilities([
                    //     { value: 'Disabled access', label: 'Disabled access' },
                    //     { value: 'Elevator', label: 'Elevator' },
                    //     { value: 'Free Parking', label: 'Free Parking' },
                    //     { value: 'Free WiFi', label: 'Free WiFi' },
                    // ])
                    // setStores([
                    //     { value: 'Occhiali Optics', label: 'Occhiali Optics' },
                    //     { value: 'Sun Eye Optics', label: 'Sun Eye Optics' },
                    //     { value: 'Yateem Optician', label: 'Yateem Optician' },
                    //     { value: 'Yateem Optics', label: 'Yateem Optics' },
                    // ])
                }
            }).catch((error) => {
                navigate('/access-denied')
                console.error('Error fetching data: ', error)
            })
    }, [])

    function timeFormat(value: any) {
        const date = new Date(value);
        const hours = date.getHours()
        const minutes = date.getMinutes()
        const ampm = hours >= 12 ? 'am' : 'pm'
        const formattedHours = hours % 12 === 0 ? 12 : hours % 12;
        const formattedMinutes = minutes < 10 ? `0${minutes}` : minutes
        const formattedTime = `${formattedHours}:${formattedMinutes} ${ampm}`;
        return formattedTime;
    }

    const onSubmit = (value: any) => {
        setLoading(true)
        console.log(value);
        // let officeHours = []
        // for (let item of value.officeHours) {
        //     officeHours.push({
        //         open: item.open,
        //         close: item.close,
        //         day: item.day
        //     })
        // }
        const data: any = {
            email: value.email,
            countryCode: value?.countryCode?.value ? value.countryCode.value : options[0].value,
            country: value?.country?.value ?? "",
            services: value?.services?.map((item: any) => item.value) ?? "",
            facilities: value?.facilities?.map((item: any) => item.value) ?? "",
            brands: value?.brands?.map((item: any) => item.value) ?? "",
            insurance: value?.insurance?.map((item: any) => item.value) ?? "",
            store: value?.store?.value ?? "",
            mobile: value.mobile,
            // officeHours: officeHours,
            location: value?.location,
            isClickAndCollect: value?.isClickAndCollect?.value
        }
        console.log(data);

        const formData = new FormData()
        formData.append('email', value.email)
        formData.append('countryCode', value?.countryCode?.value ? value.countryCode.value : options[0].value)
        formData.append('name[en]', value.nameEn)
        formData.append('name[ar]', value.nameAr)
        formData.append('address[en]', value.addressEn)
        formData.append('address[ar]', value.addressAr)
        formData.append('coordinates[lat]', value.latitude)
        formData.append('coordinates[long]', value.longitude);
        formData.append('country', value?.country?.value ?? "");
        if (value?.services?.length > 0) value?.services?.forEach((item: any) => formData.append('services', item.value))
        // formData.append('services', value?.services?.map((item: any) => item.value) ?? "");
        if (value?.facilities?.length > 0) value?.facilities?.forEach((item: any) => formData.append('facilities', item.value))
        if (value?.brands?.length > 0) value?.brands?.forEach((item: any) => formData.append('brands', item.value))
        if (value?.insurance?.length > 0) value?.insurance?.forEach((item: any) => formData.append('insurance', item.value))
        formData.append('store', value?.store?.value ?? "");
        formData.append('mobile', value.mobile);
        formData.append('location', value?.location);
        formData.append('isClickAndCollect', value?.isClickAndCollect?.value ?? "")

        if (imageFile.length > 0) formData.append('image', imageFile[0]);

        if (params.id) {
            formData.append('refid', params.id);
            formData.append('isActive', value?.isActive?.value);
            formData.append('status', value?.status?.value);

            api.post(endpoints.updateStore, formData).then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification type="success" title={res.data.message} />, {
                        placement: 'top-center',
                    })
                    navigate('/stores');
                }
                setLoading(false)
            }).catch((err) => {
                console.log(err);
                setLoading(false)
            })
        }
        else {
            api.post(endpoints.createStore, formData).then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification type="success" title={res.data.message} />, {
                        placement: 'top-center',
                    })
                    navigate('/stores');
                }
                setLoading(false)
            }).catch((err) => {
                console.log(err);
                setLoading(false)
            })
        };
    }

    const handleImageUpload = (files: any) => {
        setImageFile(files)
    }

    const addFilterValues = () => {
        if (filter == 'services') {
            setServices([...services, { value: filterValue, label: filterValue }])
            const services = getVa
        }
        if (filter == 'facilities') {
            setFacilities([...facilities, { value: filterValue, label: filterValue }])
        }
        if (filter == 'store') {
            setStores([...stores, { value: filterValue, label: filterValue }])
        }
        setShowPopup(false)
        setFilterValue('')
    }

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <h3 className="mb-2">{params.id ? 'Edit' : 'Add'} Store</h3>
            <Breadcrumb items={breadcrumbItems} />
            <h5 className="mb-2">Name</h5>
            <div className="grid grid-cols-2 gap-4 mt-4">
                <FormItem label="English">
                    <Controller
                        name="nameEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Name is required' }}
                        render={({ field }) => (
                            <input
                                type="text"
                                className={`${errors.nameEn ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                                {...field}
                            />
                        )}
                    />
                    {errors.nameEn && <small className="text-red-600 py-3">{errors.nameEn.message as string}</small>}
                </FormItem>
                <FormItem label="Arabic">
                    <Controller
                        name="nameAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Name is required' }}
                        render={({ field }) => (
                            <input
                                type="text"
                                className={`${errors.nameAr ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                                {...field}
                                dir="rtl"
                            />
                        )}
                    />
                    {/* {errors.nameAr && <small className="text-red-600 py-3">{errors.nameAr.message as string}</small>} */}
                </FormItem>
            </div>

            <div className="grid grid-cols-2 gap-4">
                <FormItem label="Mobile">
                    <Controller
                        name="mobile"
                        control={control}
                        defaultValue=""
                        rules={{
                            required: 'Mobile is required',
                            minLength: { value: 5, message: 'Enter valid mobile number' },
                            maxLength: { value: 13, message: 'Enter valid mobile number' },
                        }}
                        render={({ field }) => (
                            <InputGroup>
                                <Controller
                                    control={control}
                                    name="countryCode"
                                    render={({ field }) => (
                                        <Select
                                            className="w-32"
                                            defaultValue={options[1]}
                                            placeholder="Code"
                                            options={options}
                                            isSearchable={false}
                                            {...field}
                                        />
                                    )}
                                />
                                <Input
                                    type="number"
                                    className={`[appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none ${errors.mobile ? 'input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                                    {...field}
                                />
                            </InputGroup>
                        )}
                    />
                    {errors.mobile && (
                        <small className="text-red-600 py-3 ms-24">
                            {errors.mobile.message as string}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Email">
                    <Controller
                        name="email"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Email is required', pattern: { value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i, message: 'Invalid email address' } }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                className={`${errors.email ? 'input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                                {...field}
                            />
                        )}
                    />
                    {errors.email && <small className="text-red-600 py-3">{errors.email.message as string}</small>}
                </FormItem>
            </div>

            <h5 className="mb-1">Address</h5>
            <div className="grid grid-cols-2 gap-4">
                <FormItem label="English">
                    <Controller
                        name="addressEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Address is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                className={`${errors.addressEn ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                                {...field}
                                textArea
                            />
                        )}
                    />
                    {errors.addressEn && <small className="text-red-600 py-3">{errors.addressEn.message as string}</small>}
                </FormItem>
                <FormItem label="Arabic">
                    <Controller
                        name="addressAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Address is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                className={`${errors.addressAr ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                                {...field}
                                textArea
                                dir="rtl"
                            />
                        )}
                    />
                    {/* {errors.addressAr && <small className="text-red-600 py-3">{errors.addressAr.message as string}</small>} */}
                </FormItem>
            </div>

            <div className="grid grid-cols-2 gap-4">
                <FormItem label="Latitude">
                    <Controller
                        name="latitude"
                        control={control}
                        defaultValue=""
                        rules={{
                            required: 'Latitude is required',
                            validate: value => (value >= -90 && value <= 90) || 'Latitude must be between -90 and 90'
                        }}
                        render={({ field }) => (
                            <Input type="number"
                                className={`${errors.latitude ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                                {...field}
                            />
                        )}
                    />
                    {errors.latitude && <small className="text-red-600 py-3">{errors.latitude.message as string}</small>}
                </FormItem>

                <FormItem label="Longitude">
                    <Controller
                        name="longitude"
                        control={control}
                        defaultValue=""
                        rules={{
                            required: 'Longitude is required',
                            validate: value => (value >= -180 && value <= 180) || 'Longitude must be between -180 and 180'
                        }}
                        render={({ field }) => (
                            <Input type="number"
                                className={`${errors.longitude ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                                {...field}
                            />
                        )}
                    />
                    {errors.longitude && <small className="text-red-600 py-3">{errors.longitude.message as string}</small>}
                </FormItem>
            </div>

            {/* <ul className="mt-4">
                {fields.map((item, index) => {
                    return (
                        <li key={item.id}>
                            <div className="flex space-x-2">
                                <FormContainer layout="inline">
                                    <FormItem label="Day">
                                        <Controller
                                            name={`officeHours.${index}.day`}
                                            control={control}
                                            defaultValue={""}
                                            rules={{ required: 'Day is required' }}
                                            render={({ field }) => (
                                                <Input
                                                    type="text"
                                                    placeholder="Monday"
                                                    className={`${errors.officeHours && (errors.officeHours as any)[index]?.day ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                                                    {...field}
                                                />
                                            )}
                                        />
                                        {errors.officeHours && (errors.officeHours as any)[index]?.day && <small className="text-red-600 py-3">{(errors.officeHours as any)[index]?.day.message as string}</small>}
                                    </FormItem>

                                    <FormItem label="Opening">
                                        <Controller
                                            name={`officeHours.${index}.open`}
                                            control={control}
                                            defaultValue={""}
                                            rules={{ required: 'Opening is required' }}
                                            render={({ field }) => (
                                                <TimeInput
                                                    format="12"
                                                    {...field}
                                                />
                                            )}
                                        />
                                        {errors.officeHours && (errors.officeHours as any)[index]?.open && <small className="text-red-600 py-3">{(errors.officeHours as any)[index]?.open.message as string}</small>}
                                    </FormItem>

                                    <FormItem label="Close">
                                        <Controller
                                            name={`officeHours.${index}.close`}
                                            control={control}
                                            defaultValue={""}
                                            rules={{ required: 'Close is required' }}
                                            render={({ field }) => (
                                                <TimeInput
                                                    format="12"
                                                    {...field}
                                                />
                                            )}
                                        />
                                        {errors.officeHours && (errors.officeHours as any)[index]?.close && <small className="text-red-600 py-3">{(errors.officeHours as any)[index]?.close.message as string}</small>}
                                    </FormItem>

                                </FormContainer>

                                <Button size="sm" shape="circle" type="button" icon={<AiOutlineMinus />} onClick={() => remove(index)}></Button>

                            </div>

                        </li>
                    );
                })}
            </ul>

            <Button variant="solid" type="button" icon={<AiOutlinePlus />} onClick={() => {
                append({});
            }}>
                Add Working Hours
            </Button> */}

            {params.id &&
                <div className="grid grid-cols-2 gap-4 mt-4">
                    <FormItem label="isActive? ">
                        <Controller
                            name="isActive"
                            control={control}
                            defaultValue=""
                            render={({ field }) => (
                                <Select
                                    defaultValue={""}
                                    {...field}
                                    options={activeOptions}
                                />
                            )}
                        />
                    </FormItem>

                    <FormItem label="Store Status">
                        <Controller
                            name="status"
                            control={control}
                            defaultValue=""
                            render={({ field }) => (
                                <Select
                                    defaultValue={""}
                                    {...field}
                                    options={storeOptions}
                                />
                            )}
                        />
                    </FormItem>
                </div>
            }

            <FormItem label="Location">
                <Controller
                    name="location"
                    control={control}
                    defaultValue=""
                    // rules={{ required: 'Name is required' }}
                    render={({ field }) => (
                        <input
                            {...field}
                            type="text"
                            className={`${errors.nameAr ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                        />
                    )}
                />
                {/* {errors.nameAr && <small className="text-red-600 py-3">{errors.nameAr.message as string}</small>} */}
            </FormItem>

            <div className="grid grid-cols-2 gap-4">
                <FormItem label="Country">
                    <Controller
                        name="country"
                        control={control}
                        // defaultValue="ae"
                        render={({ field }) => (
                            <Select
                                {...field}
                                defaultValue={{ value: 'UAE', label: 'UAE' }}
                                options={countries}
                            />
                        )}
                    />
                </FormItem>
                <FormItem label="Services">
                    <div className="flex gap-2">
                        <Controller

                            name="services"
                            control={control}
                            // defaultValue="ae"
                            render={({ field }) => (
                                <Select
                                    {...field}
                                    isMulti
                                    className="w-full"
                                    options={services}
                                />
                            )}
                        />
                        <button type="button" onClick={() => { setFilter("services"); setShowPopup(true); }} className="py-2 px-3 bg-indigo-600 text-white rounded">
                            <PiPlusBold size={20} strokeWidth={5} />
                        </button>
                    </div>
                </FormItem>
            </div>

            <div className="grid grid-cols-2 gap-4">
                <FormItem label="Facilities">
                    <div className="flex gap-2">
                        <Controller
                            name="facilities"
                            control={control}
                            // defaultValue="ae"
                            render={({ field }) => (
                                <Select
                                    className="w-full"
                                    {...field}
                                    isMulti
                                    options={facilities}
                                />
                            )}
                        />
                        <button type="button" onClick={() => { setFilter("facilities"); setShowPopup(true); }} className="py-2 px-3 bg-indigo-600 text-white rounded">
                            <PiPlusBold size={20} strokeWidth={5} />
                        </button>
                    </div>
                </FormItem>
                <FormItem label="Store">
                    <div className="flex gap-2">
                        <Controller
                            name="store"
                            control={control}
                            // defaultValue="ae"
                            render={({ field }) => (
                                <Select
                                    {...field}
                                    className="w-full"
                                    options={stores}
                                />
                            )}
                        />
                        <button type="button" onClick={() => { setFilter("store"); setShowPopup(true); }} className="py-2 px-3 bg-indigo-600 text-white rounded">
                            <PiPlusBold size={20} strokeWidth={5} />
                        </button>
                    </div>
                </FormItem>
            </div>

            <div className="grid grid-cols-2 gap-4">
                <FormItem label="Brands">
                    <Controller
                        name="brands"
                        control={control}
                        render={({ field }) => (
                            <Select
                                {...field}
                                isMulti
                                className="w-full"
                                options={brands}
                                placeholder="Select brands..."
                            />
                        )}
                    />
                </FormItem>
                <FormItem label="Insurance Providers">
                    <Controller
                        name="insurance"
                        control={control}
                        render={({ field }) => (
                            <Select
                                {...field}
                                isMulti
                                className="w-full"
                                options={insurance}
                                placeholder="Select insurance providers..."
                            />
                        )}
                    />
                </FormItem>
            </div>

            <div>
                <FormItem label="Image">
                    <Upload
                        draggable
                        uploadLimit={1}
                        accept="image/*"
                        fileList={imageFile}
                        onChange={handleImageUpload}
                        ratio={[200, 80]}
                    />
                    {/* {imageError && (
                        <small className="text-red-600">{imageError}</small>
                    )} */}
                </FormItem>
            </div>

            <div className="grid grid-cols-2 gap-4 mt-4">
                <FormItem label="Availabel in Click & Collect">
                    <Controller
                        name="isClickAndCollect"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'value is required' }}
                        render={({ field }) => (
                            <Select
                                defaultValue={""}
                                {...field}
                                options={activeOptions}
                            />
                        )}
                    />
                    {errors.isClickAndCollect && <small className="text-red-600 py-3">{errors.isClickAndCollect.message as string}</small>}
                </FormItem>
            </div>

            <Button loading={loading} disabled={loading} className='float-right mt-4' variant="solid" type="submit" icon={<AiOutlineSave />}>
                Save
            </Button>

            <Dialog height={300} width={600} isOpen={showPopup} onClose={() => { setShowPopup(false); setFilterValue('') }}>
                <h3>Add {filter}</h3>
                <div className="flex flex-col gap-4 mt-2">
                    <FormItem label="Name">
                        <Input
                            onChange={(e) => setFilterValue(e.target.value)}
                            type="text"
                            className={`${errors.addressEn ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                        />
                    </FormItem>
                    <Button variant="solid" type="button" onClick={addFilterValues}>
                        Save
                    </Button>
                </div>
            </Dialog>

        </form>
    )
}
