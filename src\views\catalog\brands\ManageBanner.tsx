import { LaptopMockup, PhoneMockup } from '@/components/shared/Mockup'
import { FormItem, Input, Upload } from '@/components/ui'
import React from 'react'

const imageUrl = import.meta.env.VITE_ASSET_URL

function ManageBanner({ pageSection, changeBannerType, handleSliderVideo }: any) {
    return (
        <>
            <h5 >Banner Section</h5>
            <div className="flex gap-2 mt-4">
                <label
                    htmlFor="image"
                    className="w-full border flex items-center gap-2 p-4 cursor-pointer"
                >
                    <input onChange={() => changeBannerType(pageSection?.id, "image")} type="radio" name="bannerType" id="image" checked={pageSection?.bannerType == "image"} />
                    Image
                </label>
                <label
                    htmlFor="video"
                    className="w-full border flex items-center gap-2 p-4 cursor-pointer"
                >
                    <input onChange={() => changeBannerType(pageSection?.id, "video")} type="radio" name="bannerType" id="video" checked={pageSection?.bannerType == "video"} />
                    Video
                </label>
            </div>
            {pageSection?.bannerType == "image" ?
                <>
                    <div className="mt-4">
                        <FormItem label="Banner Image English">
                            <Upload
                                draggable
                                uploadLimit={1}
                                accept="image/*"
                                fileList={pageSection?.banner?.en ? [imageUrl + pageSection?.banner?.en] : []}
                                onChange={(e) => handleSliderVideo({ target: { files: e } }, `banner.en`, pageSection?.id)}
                                ratio={[1332, 240]}
                            />
                        </FormItem>
                    </div>
                    <div className="mt-4">
                        <FormItem label="Banner Image Arabic">
                            <Upload
                                draggable
                                uploadLimit={1}
                                accept="image/*"
                                fileList={pageSection?.banner?.ar ? [imageUrl + pageSection?.banner?.ar] : []}
                                onChange={(e) => handleSliderVideo({ target: { files: e } }, `banner.ar`, pageSection?.id)}
                                ratio={[1332, 240]}
                            />
                        </FormItem>
                    </div>
                </>
                : <>
                    <FormItem label="Desktop Video (Preferred size: 1920x1080px)" className='mt-2'>
                        <Input
                            type="file"
                            id="video"
                            accept="video/mp4,video/x-m4v,video/*"
                            onChange={(e) => handleSliderVideo(e, `src`, pageSection?.id)}
                        />
                        {/* {mainVideoForm?.video && ( */}

                        {/* )} */}
                    </FormItem>
                    <FormItem label="Mobile Video (Preferred size: 720x1280px)">
                        <Input
                            type="file"
                            id="mobileVideo"
                            accept="video/mp4,video/x-m4v,video/*"
                            onChange={(e) => handleSliderVideo(e, `mobileSrc`, pageSection?.id)}
                        />
                        {/* {mainVideoForm?.video && ( */}

                        {/* )} */}
                    </FormItem>
                    <div className="grid grid-cols-2 w-full items-center">
                        <PhoneMockup>
                            <video
                                className="h-full object-cover"
                                autoPlay
                                controls
                                loop
                                src={
                                    imageUrl +
                                    pageSection?.mobileSrc
                                }
                            />
                        </PhoneMockup>
                        <LaptopMockup>
                            <video
                                className="h-full object-cover"
                                autoPlay
                                controls
                                loop
                                src={
                                    imageUrl +
                                    pageSection?.src
                                }
                            />
                        </LaptopMockup>
                    </div>
                </>
            }
        </>
    )
}

export default ManageBanner