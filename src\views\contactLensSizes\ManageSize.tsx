import { Button, FormItem, Input, Select, toast } from "@/components/ui";
import endpoints from "@/endpoints";
import api from "@/services/api.interceptor";
import { Controller, useForm } from "react-hook-form";
import { AiOutlineSave } from "react-icons/ai";
import Notification from '@/components/ui/Notification'
import { useNavigate, useParams } from "react-router-dom";
import { useEffect } from "react";
import Breadcrumb from "../modals/BreadCrumb";

/* eslint-disable */
export default function ManageSize() {
  const navigate = useNavigate()
  const params = useParams()

  const options: any = [
    { value: 'true', label: 'True' },
    { value: 'false', label: 'False' },
  ]

  const breadcrumbItems = [
    { title: 'Contact Lens Sizes', url: '/contact-lens-sizes' },
    { title: params.id ? 'Edit' : 'Create', url: '' },
  ];

  const {
    handleSubmit,
    control,
    setValue,
    formState: { errors },
  } = useForm<any>();

  useEffect(() => {
    if (params.id) {
      api.get(endpoints.contactSizeDetail + params.id).then((res) => {
        if (res.status == 200) {
          const data = res.data?.result
          setValue('name', data?.name)
          setValue('position', data?.position)
          setValue('isActive', { value: data?.isActive.toString(), label: data?.isActive ? 'True' : 'False' })
        }
      }).catch((err) => {
        navigate('/access-denied')
        console.log(err);
      })
    }
  }, [params.id])


  const onSubmit = (data: any) => {
    const values: any = {
      name: data?.name,
      position: parseInt(data?.position),
    }

    if (params.id) {
      values.isActive = data?.isActive?.value == 'true' ? true : false;

      api.put(endpoints.updateContactSize + params.id, values).then((res) => {
        if (res.status == 200) {
          if (res?.data?.errorCode == 0) {
            toast.push(
              <Notification type="success" title={res.data.message} />, {
              placement: 'top-center',
            })
            navigate('/contact-lens-sizes');
          }
        } else {
          toast.push(
            <Notification type="warning" title={res.data.message} />, {
            placement: 'top-center',
          })
        }
      })
    }
    else {
      api.post(endpoints.createContactSize, values).then((res) => {
        if (res.status == 200) {
          toast.push(
            <Notification type="success" title={res.data.message} />, {
            placement: 'top-center',
          })
          navigate('/contact-lens-sizes');
        }
      }).catch((err) => {
        console.log(err);
      })
    };
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <h3 className="mb-2">{params.id ? 'Edit' : 'Add '} Size</h3>
      <Breadcrumb items={breadcrumbItems} />
      <div className="grid grid-cols-2 gap-4 mt-4">
        <FormItem label="Name">
          <Controller
            name="name"
            control={control}
            defaultValue=""
            rules={{ required: 'Name is required' }}
            render={({ field }) => (
              <Input
                type="text"
                className={`${errors.name ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                {...field}
              />
            )}
          />
          {errors.name && <small className="text-red-600 py-3">{errors.name.message as string}</small>}
        </FormItem>

        {params.id &&
          <FormItem label="Is Active?">
            <Controller
              name="isActive"
              control={control}
              render={({ field }) => (
                <Select options={options} {...field} />
              )}
            />
          </FormItem>
        }
      </div>
      <div className="grid grid-cols-2 gap-4 mt-4">
        <FormItem label="Position">
          <Controller
            name="position"
            control={control}
            defaultValue=""
            rules={{ required: 'Position is required' }}
            render={({ field }) => (
              <Input
                type="number"
                className={`${errors.name ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                {...field}
              />
            )}
          />
          {errors.position && <small className="text-red-600 py-3">{errors.position.message as string}</small>}
        </FormItem>
      </div>

      <Button className='float-right mt-4' variant="solid" type="submit" icon={<AiOutlineSave />}>
        Save
      </Button>

    </form>
  )
}
