import { FormItem, Input } from '@/components/ui'
import React from 'react'
import { Controller } from 'react-hook-form'

export const productList: { label: string, value: string, db: string }[] = [
    {
        label: 'Sort by',
        value: 'SortBy',
        db: 'sortBy',
    },
    {
        label: 'Filter by',
        value: 'FilterBy',
        db: 'filterBy',
    },
    {
        label: 'Apply',
        value: 'Apply',
        db: 'apply',
    },
    {
        label: 'Reset',
        value: 'Reset',
        db: 'reset',
    },
    {
        label: 'Clear All',
        value: 'ClearAll',
        db: 'clearAll',
    },
    {
        label: 'Showing',
        value: 'Showing',
        db: 'showing',
    },
    {
        label: 'Products',
        value: 'Products',
        db: 'products',
    },
    {
        label: 'Category',
        value: 'Category',
        db: 'category',
    },
    {
        label: 'Frame Type',
        value: 'FrameType',
        db: 'frameType',
    },
    {
        label: 'Frame Shape',
        value: 'FrameShape',
        db: 'frameShape',
    },
    {
        label: 'Brands',
        value: 'Brands',
        db: 'brands',
    },
    {
        label: 'Frame Size',
        value: 'FrameSize',
        db: 'frameSize',
    },
    {
        label: 'Front Material',
        value: 'FrontMaterial',
        db: 'frontMaterial',
    },
    {
        label: 'Color',
        value: 'Color',
        db: 'color',
    },
    {
        label: 'Price',
        value: 'Price',
        db: 'price',
    },
    {
        label: 'Type',
        value: 'Type',
        db: 'types',
    },
    {
        label: 'Lens Type',
        value: 'LensType',
        db: 'lensType',
    },
    {
        label: "Virtual Try",
        value: "VirtualTry",
        db: "virtualTry"
    },
    {
        label: "View More",
        value: "viewMore",
        db: "viewMore"
    },
    {
        label: "New Arrivals",
        value: "NewArrivals",
        db: "newArrivals"
    },
    {
        label: "Sale",
        value: "Sale",
        db: "sale"
    },
    {
        label: "Exclusive",
        value: "Exclusive",
        db: "exclusive"
    },
    {
        label: "Price Low To High",
        value: "PriceLowToHigh",
        db: "priceLowToHigh"
    },
    {
        label: "Price High To Low",
        value: "PriceHighToLow",
        db: "priceHighToLow"
    },
    {
        label: "No Products Found",
        value: "NoProductsFound",
        db: "noProductsFound"
    },
]


function ProductListingTab({ control, errors }: any) {
    return (
        <>
            <h3>Product Listing</h3>
            {productList.map((item, index) => (
                <div className="mt-2" key={index}>
                    <div className="grid grid-cols-2 gap-4">
                        <FormItem label={`${item.label} English`}>
                            <Controller
                                control={control}
                                name={`productListing${item.value}En`}
                                rules={{ required: 'Field Required' }}
                                render={({ field }) => <Input type="text" {...field} />}
                            />
                            {errors[`productListing${item.value}En`] && (
                                <small className="text-red-600 py-3">
                                    {' '}
                                    {errors[`productListing${item.value}En`].message as string}{' '}
                                </small>
                            )}
                        </FormItem>
                        <FormItem label={`${item.label} Arabic`}>
                            <Controller
                                control={control}
                                name={`productListing${item.value}Ar`}
                                rules={{ required: 'Field Required' }}
                                render={({ field }) => <Input dir='rtl' type="text" {...field} />}
                            />
                            {errors[`productListing${item.value}Ar`] && (
                                <small className="text-red-600 py-3">
                                    {' '}
                                    {errors[`productListing${item.value}Ar`].message as string}{' '}
                                </small>
                            )}
                        </FormItem>
                    </div>
                </div>
            ))}
        </>
    )
}

export default ProductListingTab