import Button from '@/components/ui/Button'
import Card from '@/components/ui/Card'
import Chart from '@/components/shared/Chart'

type SalesReportProps = {
    data?: {
        series?: {
            name: string
            data: number[]
        }[]
        categories?: string[]
    }
    className?: string
}

const SalesReport = ({ className, data = {} }: SalesReportProps) => {
    const generateCSV = (data: SalesReportProps['data']) => {
        if (!data || !data.series || !data.categories) return '';

        const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
        const header = ['Month', 'Year', 'Revenue'].join(',') + '\n';
        const rows = data.categories.map((category, index) => {
            const [monthStr, yearStr] = category.split(' ');
            const year = parseInt(yearStr, 10);
            const monthIndex = monthNames.indexOf(monthStr);
            const monthName = monthIndex !== -1 ? monthNames[monthIndex] : monthStr;
            const rowData = data.series
                ? data.series.map(s => s.data[index] || '').join(',')
                : '';
            return `${monthName},${year},${rowData}`;
        }).join('\n');
        
        return header + rows;

    };

    const exportReport = () => {
        const csvData = generateCSV(data);
        const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'sales_report.csv';
        a.click();
        URL.revokeObjectURL(url);
      };
      
    return (
        <Card className={className}>
            <div className="flex items-center justify-between">
                <h4>Average Sale Amount Report</h4>
                <Button size="sm" onClick={exportReport}>Export Report</Button>
            </div>
            <Chart
                series={data.series}
                xAxis={data.categories}
                height="380px"
                customOptions={{ legend: { show: false } }}
            />
        </Card>
    )
}

export default SalesReport
