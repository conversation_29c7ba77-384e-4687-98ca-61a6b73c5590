import { Button, FormItem, toast, Select, Upload, Input } from '@/components/ui'
import endpoints from '@/endpoints'
import api from '@/services/api.interceptor'
import { Controller, useForm } from 'react-hook-form'
import { AiOutlineSave } from 'react-icons/ai'
import Notification from '@/components/ui/Notification'
import { useNavigate, useParams } from 'react-router-dom'
import { useEffect, useState } from 'react'
import Breadcrumb from '@/views/modals/BreadCrumb'
import { Country, State, City } from 'country-state-city';
import icons from 'currency-icons';

console.log(Country.getCountryByCode('AE'))
console.log(icons[Country.getCountryByCode('AE').currency])
console.log("")

/* eslint-disable */

export default function ManageMultiStore() {
    const countries = Country.getAllCountries().map((country: any) => ({
        value: country.isoCode,
        label: country.name,
    }));


    const navigate = useNavigate()
    const params = useParams()

    const breadcrumbItems = [
        { title: 'Multi Store', url: '/multi-store' },
        { title: 'Manage Multi Store', url: '' },
    ]


    useEffect(() => {
        if (params.id) {
            api.get(endpoints.multiStoreDetails + params.id)
                .then((res) => {
                    if (res?.status == 200) {
                        const data = res?.data?.result
                        console.log(data)
                        setValue('country', {
                            value: data?.storeId?.toUpperCase(),
                            label: data?.name?.en,
                        })
                        setValue('countryAr', data?.name?.ar)
                        setValue('isActive', {
                            value: data?.isActive,
                            label: data?.isActive ? 'True' : 'False',
                        })
                        setValue('NOON_API_KEY', data?.envs?.NOON_API_KEY);
                        setValue('NOON_API_URL', data?.envs?.NOON_API_URL);
                        setValue('TAMARA_API_URL', data?.envs?.TAMARA_API_URL);
                        setValue('TAMARA_API_TOKEN', data?.envs?.TAMARA_API_TOKEN);
                        setValue('TAMARA_NOTIFICATION_TOKEN', data?.envs?.TAMARA_NOTIFICATION_TOKEN);
                        setValue('TABBY_TOKEN', data?.envs?.TABBY_TOKEN);

                    }
                })
                .catch((error) => {
                    console.error('Error fetching data: ', error)
                })
        }
    }, [])


    const options: any = [
        { value: 'true', label: 'True' },
        { value: 'false', label: 'False' },
    ]

    const {
        handleSubmit,
        control,
        setValue,
        formState: { errors },
    } = useForm<any>()

    const onSubmit = (value: any) => {
        let { country, countryAr, isActive, ...values } = value
        let data: any = {
            name: {
                en: country.label,
                ar: countryAr
            },
            storeId: country.value.toLowerCase(),
            currencyCode: Country.getCountryByCode(country.value).currency ?? "",
            currencySymbol: icons[Country.getCountryByCode(country.value).currency]?.symbol ?? "",
            envs: values,
            countryCode: "+" + Country.getCountryByCode(country.value)?.phonecode,
        }

        if (params.id) {
            data.refid = params.id
            data.isActive = isActive.value
            api.put(endpoints.updateMultiStore, data).then(
                (res) => {
                    if (res.status == 200) {
                        if (res?.data?.errorCode == 0) {
                            toast.push(
                                <Notification
                                    type="success"
                                    title={res.data.message}
                                />,
                                {
                                    placement: 'top-center',
                                }
                            )
                            navigate('/multi-store')
                        }
                    } else {
                        toast.push(
                            <Notification
                                type="warning"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                    }
                }
            ).catch((err) => {
                console.log(err)
                toast.push(
                    <Notification
                        type="warning"
                        title={err.response.data.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )
            })
        } else {
            api.post(endpoints.createMultiStore, data)
                .then((res) => {
                    if (res.status == 200) {
                        if (res?.data?.errorCode == 0) {
                            toast.push(
                                <Notification
                                    type="success"
                                    title={res.data.message}
                                />,
                                {
                                    placement: 'top-center',
                                }
                            )
                            navigate('/multi-store')
                        }
                    } else {
                        toast.push(
                            <Notification
                                type="warning"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                    }
                })
                .catch((err) => {
                    console.log(err)
                    toast.push(
                        <Notification
                            type="warning"
                            title={err.response.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                })
        }
    }

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <h3 className="mb-2">
                {params.id ? 'Edit Multi Store' : 'Add Multi Store'}
            </h3>
            <Breadcrumb items={breadcrumbItems} />

            <div className="grid grid-cols-2 gap-4 mt-4">
                <FormItem label="Country">
                    <Controller
                        name="country"
                        rules={{ required: 'Field is required' }}
                        control={control}
                        defaultValue=""
                        render={({ field }) => (
                            <Select options={countries} {...field} />
                        )}
                    />
                    {errors.country && (
                        <small className="text-red-600 py-3">
                            {errors.country.message as string}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Country Arabic">
                    <Controller
                        name="countryAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {/* {errors.titleAr && (
                        <small className="text-red-600 py-3">
                            {errors.titleAr.message as string}
                        </small>
                    )} */}
                </FormItem>
            </div>

            <div className="grid grid-cols-2 gap-4">
                {params.id && (
                    <FormItem label="is Active ?">
                        <Controller
                            name="isActive"
                            control={control}
                            defaultValue=""
                            render={({ field }) => (
                                <Select options={options} {...field} />
                            )}
                        />
                    </FormItem>
                )}
            </div>

            {/* <h5>Environment variables</h5> */}

            {/* <h6 className='mt-4'>OC credentials</h6>
            <div className="grid grid-cols-2 gap-x-4 mt-2">
                <FormItem label="ORG ID">
                    <Controller
                        name="CRM_ORG_ID"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {errors.CRM_ORG_ID && (
                        <small className="text-red-600 py-3">
                            {errors.CRM_ORG_ID.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="USERNAME">
                    <Controller
                        name="CRM_USERNAME"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {errors.CRM_USERNAME && (
                        <small className="text-red-600 py-3">
                            {errors.CRM_USERNAME.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="TOKEN">
                    <Controller
                        name="CRM_TOKEN"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {errors.CRM_TOKEN && (
                        <small className="text-red-600 py-3">
                            {errors.CRM_TOKEN.message as string}
                        </small>
                    )}
                </FormItem>
            </div> */}

            {/* <h6>Email</h6>
            <div className="grid grid-cols-2 gap-x-2 mt-2">
                <FormItem label="From text">
                    <Controller
                        name="EMAIL_FROM"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {errors.EMAIL_FROM && (
                        <small className="text-red-600 py-3">
                            {errors.EMAIL_FROM.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="USERNAME">
                    <Controller
                        name="EMAIL_USERNAME"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {errors.EMAIL_USERNAME && (
                        <small className="text-red-600 py-3">
                            {errors.EMAIL_USERNAME.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="PASSWORD">
                    <Controller
                        name="EMAIL_PASSWORD"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {errors.EMAIL_PASSWORD && (
                        <small className="text-red-600 py-3">
                            {errors.EMAIL_PASSWORD.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="ORDER CC (comma separated)">
                    <Controller
                        name="ORDER_CCS"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {errors.ORDER_CCS && (
                        <small className="text-red-600 py-3">
                            {errors.ORDER_CCS.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="INSURANCE CC (comma separated)">
                    <Controller
                        name="INSURANCE_CCS"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {errors.INSURANCE_CCS && (
                        <small className="text-red-600 py-3">
                            {errors.INSURANCE_CCS.message as string}
                        </small>
                    )}
                </FormItem>
            </div> */}

            <h6>Noon credentials</h6>
            <div className="grid grid-cols-2 gap-x-2 mt-2">
                <FormItem label="API KEY">
                    <Controller
                        name="NOON_API_KEY"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {errors.NOON_API_KEY && (
                        <small className="text-red-600 py-3">
                            {errors.NOON_API_KEY.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="API URL">
                    <Controller
                        name="NOON_API_URL"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {errors.NOON_API_URL && (
                        <small className="text-red-600 py-3">
                            {errors.NOON_API_URL.message as string}
                        </small>
                    )}
                </FormItem>
            </div>
            <h6>Tamara credentials</h6>
            <div className="grid grid-cols-2 gap-x-2 mt-2">
                <FormItem label="API URL">
                    <Controller
                        name="TAMARA_API_URL"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {errors.TAMARA_API_URL && (
                        <small className="text-red-600 py-3">
                            {errors.TAMARA_API_URL.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="API TOKEN">
                    <Controller
                        name="TAMARA_API_TOKEN"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {errors.TAMARA_API_TOKEN && (
                        <small className="text-red-600 py-3">
                            {errors.TAMARA_API_TOKEN.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="NOTIFICATION TOKEN">
                    <Controller
                        name="TAMARA_NOTIFICATION_TOKEN"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {errors.TAMARA_NOTIFICATION_TOKEN && (
                        <small className="text-red-600 py-3">
                            {errors.TAMARA_NOTIFICATION_TOKEN.message as string}
                        </small>
                    )}
                </FormItem>
            </div>
            <h6>Tabby credentials</h6>
            <div className="grid grid-cols-2 gap-x-2 mt-2">
                <FormItem label="Token">
                    <Controller
                        name="TABBY_TOKEN"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {errors.TABBY_TOKEN && (
                        <small className="text-red-600 py-3">
                            {errors.TABBY_TOKEN.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <Button
                className="float-right mt-6"
                variant="solid"
                type="submit"
                icon={<AiOutlineSave />}
            >
                Save
            </Button>
        </form>
    )
}
