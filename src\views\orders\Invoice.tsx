/*eslint-disable*/
import { Button } from '@/components/ui'
import endpoints from '@/endpoints'
import api from '@/services/api.interceptor'
import { useEffect, useState } from 'react'
import { useParams } from 'react-router-dom'

export default function Invoice() {
    const params = useParams()
    const [data, setData] = useState<any>()
    const baseUrl = import.meta.env.VITE_ASSET_URL
    const currency = localStorage.getItem("currency") || "AED"

    const getOrderDetails = () => {
        api.get(endpoints.orderDetail + params.id).then((res) => {
            if (res.status == 200) {
                console.log(res.data.result)
                setData(res.data.result)
            }
        }).catch((err) => {
            console.log(err)
        })
    }

    useEffect(() => {
        getOrderDetails()

    }, [])

    const handlePrint = () => {
        window.print()
    }

    return (
        <div className="max-w-2xl mx-auto my-8">
            <div className="flex justify-between items-center">
                <img
                    className="w-48 mx-auto"
                    src="/img/logo/logo-light-full.svg"
                    alt=""
                />
                {/* <div>
                    <p>
                        Arous Al Bahar, Opp. Hotel Hilton, Business Bay,
                        Fujairah
                    </p>
                    <p>Contact us: 0889889988 | <EMAIL></p>
                </div> */}
            </div>
            <div className="mt-2 text-lg text-black font-semibold">

            </div>
            <hr className="shadow text-gray-300" />
            <div className="mt-3 flex justify-between">
                <div className="text-sm line-height-5   ">
                    {data?.address?.name ?
                        <>
                            <p className="text-black font-semibold">Shipped to : </p>
                            <p>{data?.address?.name}</p>
                            <p>{data?.address?.suiteUnit} , {data?.address?.street}</p>
                            <p>{data?.address?.postalCode}</p>
                            <p>{data?.address?.city} , {data?.address?.emirates}, {data?.address?.country}</p>
                            <p>{data?.address?.countryCode} {data?.address?.mobile}</p>
                        </>
                        :
                        <>
                            <p className="text-black font-semibold">Shipped to : </p>
                            <p>{data?.addressDetails?.name}</p>
                            <p>{data?.addressDetails?.suiteUnit} , {data?.addressDetails?.street}</p>
                            <p>{data?.addressDetails?.postalCode}</p>
                            <p>{data?.addressDetails?.city} , {data?.addressDetails?.emirates}, {data?.addressDetails?.country}</p>
                            <p>{data?.addressDetails?.countryCode} {data?.addressDetails?.mobile}</p>
                        </>
                    }
                </div>
                <div>
                    <p className='text-end'>INVOICE ID: {data?.invoiceId}</p>
                    <p className='text-end'>ORDER ID: {data?.orderNo}</p>
                </div>
            </div>
            <div className="mt-3 flex justify-between">
                <div>
                    <p className="text-black text-sm font-semibold">
                        Payement Method:
                    </p>
                    <p>{data?.paymentMethod}</p>
                </div>
                {data?.paymentMethod == "ONLINE" && <div>
                    <p className="text-black text-sm font-semibold">
                        Payment Gateway:
                    </p>
                    <p>{data?.gateway}</p>
                </div>}
                <div>
                    <p className="text-black text-sm font-semibold">
                        Payment Status:
                    </p>
                    <p>{data?.paymentStatus}</p>
                </div>
                <div>
                    <p className="text-black text-sm font-semibold">
                        Order Date:
                    </p>
                    <p>{new Date(data?.orderDate).toLocaleDateString()}</p>
                </div>
            </div>
            <hr className="shadow text-gray-300 my-2" />
            <table className="mt-3">
                <thead>
                    <tr className="border-b bg-gray-100">
                        <td className="px-4 py-2">Item</td>
                        <td className="px-4 py-2">Price</td>
                        <td className="px-4 py-2">Quantity</td>
                        <td className="px-4 py-2">Total</td>
                        <td className="px-4 py-2">Image</td>
                    </tr>
                </thead>
                <tbody>
                    {data?.products?.map((product: any) => (
                        <tr key={product?._id} className="bg-gray-100">
                            <td className="px-4 py-2">
                                <p className='font-semibold'>{product?.product?.name?.en}</p>
                                {(product?.size?.name || product?.product?.color?.name) && <div className="flex gap-4">
                                    {product?.size?.name && (
                                        <p>Size: {product?.size?.name}</p>
                                    )}
                                    {product?.product?.color?.name && (
                                        <p>Color: {product?.product?.color?.name?.en}</p>
                                    )}
                                </div>}

                                <div className="grid grid-cols-2 gap-2">
                                    {product?.contactLens?.sphLeft && (
                                        <p>SPH Left: {product?.contactLens?.sphLeft?.name}</p>
                                    )}
                                    {product?.contactLens?.sphRight && (
                                        <p>SPH Right: {product?.contactLens?.sphRight?.name}</p>
                                    )}
                                    {product?.contactLens?.cylLeft && (
                                        <p>CYL Left: {product?.contactLens?.cylLeft?.name}</p>
                                    )}
                                    {product?.contactLens?.cylRight && (
                                        <p>CYL Right: {product?.contactLens?.cylRight?.name}</p>
                                    )}
                                    {product?.contactLens?.axisLeft && (
                                        <p>AXIS Left: {product?.contactLens?.axisLeft?.name}</p>
                                    )}
                                    {product?.contactLens?.axisRight && (
                                        <p>AXIS Right: {product?.contactLens?.axisRight?.name}</p>
                                    )}
                                    {product?.contactLens?.multiFocal && (
                                        <p>Addition: {product?.contactLens?.multiFocal}</p>
                                    )}
                                    {product?.contactLens?.pd && (
                                        <p>PD: {product?.contactLens?.pd}</p>
                                    )}
                                </div>
                            </td>
                            <td className="px-4 py-2 text-center">{currency} {product?.price}</td>
                            <td className="px-4 py-2 text-center">{product?.quantity}</td>
                            <td className="px-4 py-2 text-center">{currency} {product?.total}</td>
                            <td className="px-4 py-2 text-center">
                                <img
                                    alt="Product Image"
                                    className="aspect-square rounded-md object-cover"
                                    height={64}
                                    src={baseUrl + product?.product?.thumbnail}
                                    width={64}
                                />
                            </td>
                        </tr>
                    ))}
                    {/* <tr className="font-semibold bg-gray-100">
                        <td className="px-4 py-2 text-center">Total AED</td>
                        <td className="px-4 py-2 text-center" />
                        <td className="px-4 py-2 text-center" />
                        <td className="px-4 py-2 text-center" />
                        <td className="px-4 py-2 text-center">AED {data?.total}</td>
                        <td className="px-4 py-2 text-center" />
                    </tr> */}
                </tbody>
            </table>
            <div className="mt-4">
                <div className="flex justify-between py-2 border-t border-b">
                    <span>Sub Total</span>
                    <span>{currency} {data?.baseTotal - (data?.vatAmount ?? 0)}</span>
                </div>
                {data?.savings ?
                    <div className="flex justify-between py-2 border-b">
                        <span>Savings</span>
                        <span>{currency} {data?.savings}</span>
                    </div>
                    : ""}
                {data?.shippingCharge ?
                    <div className="flex justify-between py-2 border-b">
                        <span>Shipping Charges</span>
                        <span>{currency} {data?.shippingCharge}</span>
                    </div>
                    : ""}
                {data?.vat > 0 ?
                    <div className="flex justify-between py-2 border-b">
                        <span>VAT ({data.vat}%)</span>
                        <span>{currency} {data?.vatAmount}</span>
                    </div>
                    : ""}
                {data?.paymentCharge > 0 ?
                    <div className="flex justify-between py-2 border-b">
                        <span>{data?.paymentMethod == "COD" ? "COD Charges" : "Payment Gateway Charges"}</span>
                        <span>{currency} {data?.paymentCharge}</span>
                    </div>
                    : ""}
                {data?.isGiftWrapping && data?.giftWrappingFee > 0 ?
                    <div className="flex justify-between py-2 border-b">
                        <span>Gift Wrapping Fee</span>
                        <span>{currency} {data?.giftWrappingFee}</span>
                    </div>
                    : ""}
                <div className="flex justify-between py-2 border-b font-semibold">
                    <span>Total</span>
                    <span>{currency} {data?.total}</span>
                </div>
            </div>
            <div className="flex justify-center mt-6 print:hidden">
                <Button variant="solid" onClick={handlePrint}>
                    Print
                </Button>
            </div>
        </div>
    )
}
