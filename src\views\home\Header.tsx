import DatePicker from '@/components/ui/DatePicker'
import Button from '@/components/ui/Button'

import { useAppDispatch } from '@/store'
import { HiOutlineFilter } from 'react-icons/hi'
import dayjs from 'dayjs'

const dateFormat = 'MMM DD, YYYY'

const { DatePickerRange } = DatePicker

const DashboardHeader = () => {

    const handleDateChange = (dates: any) => {
        console.log(dates)
    }

    return (
        <div className="lg:flex items-center justify-between mb-4 gap-3">
            <div className="mb-4 lg:mb-0">
                <h3>Sales Overview</h3>
                <p>View your current sales & summary</p>
            </div>
            {/* <div className="flex flex-col lg:flex-row lg:items-center gap-3">
                <DatePickerRange
                    value={[
                        dayjs.unix(1710050171).toDate(),
                        dayjs.unix(1710490591).toDate(),
                    ]}
                    inputFormat={dateFormat}
                    size="sm"
                    onChange={handleDateChange}
                />
                <Button size="sm" icon={<HiOutlineFilter />}>
                    Filter
                </Button>
            </div> */}
        </div>
    )
}

export default DashboardHeader
