import Dropdown from '@/components/ui/Dropdown'
import withHeaderItem from '@/utils/hoc/withHeaderItem'
import classNames from 'classnames'
import type { CommonProps } from '@/@types/common'
import { useEffect, useState } from 'react'
import api from '@/services/api.interceptor'
import endpoints from '@/endpoints'
import { CircleFlag } from 'react-circle-flags'
import { IoMdArrowDropdown } from "react-icons/io";

const _StoreSwitcher = ({ className }: CommonProps) => {
    const [selectedCountry, setSelectedCountry] = useState<any>('')
    const [countries, setCountries] = useState<any>([])

    const onCountryChange = (item:any) => {
        localStorage.setItem("store", item?.value)
        localStorage.setItem("currency", item?.currencyCode)
        window.location.reload()
    }

    useEffect(() => {
        const value = localStorage.getItem("store") || "sa"
        api.get(endpoints.activeMultiStores).then((res) => {
            if (res.status == 200) {
                const country = res.data?.result.find((item: any) => item.storeId == value)
                localStorage.setItem("currency", country?.currencyCode)
                if (country) {
                    setSelectedCountry({ label: country.name.en, value: country.storeId })
                } else {
                    setSelectedCountry({ label: res.data?.result[0].name.en, value: res.data?.result[0].storeId })
                    localStorage.setItem("store", res.data?.result[0].storeId)
                    localStorage.setItem("currency", res.data?.result[0].currencyCode)
                }
                setCountries(res.data?.result?.map((item: any) => ({
                    label: item.name.en,
                    value: item.storeId,
                    currencyCode: item?.currencyCode
                })))
            }
        })
    }, [])

    const UserAvatar = (
        <div className={classNames(className, 'flex items-center gap-2')}>
            <span className="flex gap-2 items-center w-full">
                <div className="w-6">
                    <CircleFlag countryCode={selectedCountry?.value} />
                </div>
                <span>{selectedCountry?.label}</span>
                <IoMdArrowDropdown size={20} />
            </span>
        </div>
    )

    return (
        <div>
            <Dropdown
                menuStyle={{ minWidth: 240 }}
                renderTitle={UserAvatar}
                placement="bottom-end"
            >

                {countries.map((item: any) => (
                    <Dropdown.Item
                        key={item.value}
                        eventKey={item.value}
                        className="mb-1 px-0"
                        onSelect={() => onCountryChange(item)}
                    >
                        <span className={`flex gap-2 items-center w-full p-2 ${item.value == selectedCountry?.value ? "bg-gray-100 dark:bg-gray-700" : ""}`}>
                            <div className="w-6">
                                <CircleFlag countryCode={item.value} />
                            </div>
                            <span>{item.label}</span>
                        </span>
                    </Dropdown.Item>
                ))}
            </Dropdown>
        </div>
    )
}

const StoreSwitcher = withHeaderItem(_StoreSwitcher)

export default StoreSwitcher
