/* eslint-disable */
import { <PERSON><PERSON>, <PERSON>I<PERSON>, toast, FormContainer, Upload } from '@/components/ui'
import endpoints from '@/endpoints'
import api from '@/services/api.interceptor'
import { Controller, useFieldArray, useForm } from 'react-hook-form'
import { AiOutlineMinus, AiOutlinePlus, AiOutlineSave } from 'react-icons/ai'
import Notification from '@/components/ui/Notification'
import { useNavigate } from 'react-router-dom'
import Input from '@/components/ui/Input'
import { useEffect, useState } from 'react'
import { Dropzone } from '@/components/shared/Dropzone'
import { IoIosCloseCircleOutline } from 'react-icons/io'
import Breadcrumb from '../modals/BreadCrumb'

export default function ContactLens() {
    const [isUpdate, setIsUpdate] = useState(false);
    const [imageFile, setImageFile] = useState<string[]>([])
    const [ogImageFile, setOgImageFile] = useState<string[]>([])
    const navigate = useNavigate()

    const breadcrumbItems = [
        { title: 'Contact Lens ', url: '' },
    ]

    const getContactLens = async () => {
        api.get(endpoints.contactLens).then((res) => {
            if (res?.status == 200) {
                const data = res?.data?.result
                setIsUpdate(true)
                setValue('titleEn', data?.title?.en)
                setValue('titleAr', data?.title?.ar)
                setValue('descriptionEn', data.description.en)
                setValue('descriptionAr', data.description.ar)
                setValue('sectionOneTitleEn', data.sectionOne.title.en)
                setValue('sectionOneTitleAr', data.sectionOne.title.ar)
                setValue('sectionOneDescEn', data.sectionOne.description.en)
                setValue('sectionOneDescAr', data.sectionOne.description.ar)
                setImageFile([data.sectionOne.image])
                setValue('sectionTwoTitleEn', data?.sectionTwo?.title?.en)
                setValue('sectionTwoTitleAr', data?.sectionTwo?.title?.ar)
                setValue('sectionTwoDescEn', data?.sectionTwo?.description?.en)
                setValue('sectionTwoDescAr', data?.sectionTwo?.description?.ar)
                setValue('steps', data?.sectionTwo?.steps?.map((item: any) => ({
                    titleEn: item?.title?.en,
                    titleAr: item?.title?.ar,
                    descEn: item?.description?.en,
                    descAr: item?.description?.ar,
                })))
                setValue('metaTitleEn', data?.seoDetails?.title?.en)
                setValue('metaTitleAr', data?.seoDetails?.title?.ar)
                setValue('metaDescriptionEn', data?.seoDetails?.description?.en)
                setValue('metaDescriptionAr', data?.seoDetails?.description?.ar)
                setValue('metaKeywordsEn', data?.seoDetails?.keywords?.en)
                setValue('metaKeywordsAr', data?.seoDetails?.keywords?.ar)
                setValue('metaCanonicalUrl', data?.seoDetails?.canonical?.en)
                setValue('metaCanonicalUrlAr', data?.seoDetails?.canonical?.ar)
                setOgImageFile([data?.seoDetails?.ogImage])
                
            }
        }).catch((error) => {
            console.error('Error fetching data: ', error)
        })
    }

    useEffect(() => {
        if (stepsFields.length == 0) stepsAppend({})
        getContactLens()
    }, [])

    const {
        handleSubmit,
        control,
        setValue,
        formState: { errors },
    } = useForm<any>()

    const {
        fields: stepsFields,
        append: stepsAppend,
        remove: stepsRemove,
    } = useFieldArray({
        control,
        name: 'steps',
    })

    const onSubmit = (value: any) => {
        console.log(value)
        const data = {
            title: {
                en: value.titleEn,
                ar: value.titleAr
            },
            description: {
                en: value.descriptionEn,
                ar: value.descriptionAr
            },
            sectionOne: {
                title: {
                    en: value.sectionOneTitleEn,
                    ar: value.sectionOneTitleAr
                },
                description: {
                    en: value.sectionOneDescEn,
                    ar: value.sectionOneDescAr
                },
                image: imageFile[0]
            },
            sectionTwo: {
                title: {
                    en: value.sectionTwoTitleEn,
                    ar: value.sectionTwoTitleAr
                },
                description: {
                    en: value.sectionTwoDescEn,
                    ar: value.sectionTwoDescAr
                },
                steps: value.steps.map((item: any) => ({
                    title: {
                        en: item.titleEn,
                        ar: item.titleAr
                    },
                    description: {
                        en: item.descEn,
                        ar: item.descAr
                    }
                }))
            },
            seoDetails: {
                title: {
                    en: value.metaTitleEn,
                    ar: value.metaTitleAr
                },
                description: {
                    en: value.metaDescriptionEn,
                    ar: value.metaDescriptionAr
                },
                keywords: {
                    en: value.metaKeywordsEn,
                    ar: value.metaKeywordsAr
                },
                canonical: {
                    en: value.metaCanonicalUrl,
                    ar: value.metaCanonicalUrlAr
                },
                ogImage: ogImageFile[0]
            },
        }

        if (isUpdate) {
            api.put(endpoints.updateContactLens, data).then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification type="success" title={res.data.message} />, {
                        placement: 'top-center',
                    })
                }
            }).catch((err) => {
                toast.push(
                    <Notification type="warning" title={err.response.data.message} />, {
                    placement: 'top-center',
                })
            })
        } else {
            api.post(endpoints.createContactLens, data).then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification type="success" title={res.data.message} />, {
                        placement: 'top-center',
                    })
                }
            }).catch((err) => {
                toast.push(
                    <Notification type="warning" title={err.response.data.message} />, {
                    placement: 'top-center',
                })
            })
        }
    }

    const handleOgImageUpload = (file: any) => {
        setOgImageFile(file)
    }

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <h3 className="mb-2">Contact Lens</h3>
            <Breadcrumb items={breadcrumbItems} />
            <h5 className="my-1">Title</h5>
            <div className="grid grid-cols-2 gap-4">
                <FormItem label="English">
                    <Controller
                        control={control}
                        name="titleEn"
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {errors.titleEn && (
                        <small className="text-red-600 py-3">
                            {' '}
                            {errors.titleEn.message as string}{' '}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Arabic">
                    <Controller
                        control={control}
                        name="titleAr"
                        // rules={{ required: 'Field Required' }}
                        render={({ field }) => (
                            <Input dir="rtl" type="text" {...field} />
                        )}
                    />
                    {/* {errors.titleAr && (
                        <small className="text-red-600 py-3">
                            {' '}
                            {errors.titleAr.message as string}{' '}
                        </small>
                    )} */}
                </FormItem>
            </div>
            <h5>Description</h5>
            <div className="grid grid-cols-2 gap-4">
                <FormItem label='English'>
                    <Controller
                        control={control}
                        name="descriptionEn"
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                textArea
                                {...field}
                            />
                        )}
                    />
                    {errors.descriptionEn && (<small className="text-red-600 py-3"> {errors.descriptionEn.message as string} </small>)}
                </FormItem>

                <FormItem label='Arabic'>
                    <Controller
                        control={control}
                        name="descriptionAr"
                        render={({ field }) => (
                            <Input
                                dir='rtl'
                                type="text"
                                textArea
                                {...field}
                            />
                        )}
                    />
                    {/* {errors.descriptionAr && (<small className="text-red-600 py-3"> {errors.descriptionAr.message as string} </small>)} */}
                </FormItem>
            </div>

            <h5>How Works Section</h5>
            <div className='grid grid-cols-2 gap-4'>
                <FormItem label='Title (English)'>
                    <Controller
                        control={control}
                        name="sectionOneTitleEn"
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.sectionOneTitleEn && (<small className="text-red-600 py-3"> {errors.sectionOneTitleEn.message as string} </small>)}
                </FormItem>

                <FormItem label='Title (Arabic)'>
                    <Controller
                        control={control}
                        name="sectionOneTitleAr"
                        // rules={{ required: 'Field Required' }}
                        render={({ field }) => (
                            <Input
                                dir='rtl'
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {/* {errors.sectionOneTitleAr && (<small className="text-red-600 py-3"> {errors.sectionOneTitleAr.message as string} </small>)} */}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label='Description (English)'>
                    <Controller
                        control={control}
                        name="sectionOneDescEn"
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                textArea
                                {...field}
                            />
                        )}
                    />
                    {errors.sectionOneDescEn && (<small className="text-red-600 py-3"> {errors.sectionOneDescEn.message as string} </small>)}
                </FormItem>

                <FormItem label='Description (Arabic)'>
                    <Controller
                        control={control}
                        name="sectionOneDescAr"
                        // rules={{ required: 'Field Required' }}
                        render={({ field }) => (
                            <Input
                                dir='rtl'
                                type="text"
                                textArea
                                {...field}
                            />
                        )}
                    />
                    {/* {errors.sectionOneDescAr && (<small className="text-red-600 py-3"> {errors.sectionOneDescAr.message as string} </small>)} */}
                </FormItem>
            </div>

            <div>
                <FormItem label="Image">
                    <Dropzone ratio={[1074, 616]} previews={imageFile} onFileUrlChange={(e) => { setImageFile([e]) }} />
                </FormItem>
            </div>

            <h5>Steps Section</h5>
            <div className='grid grid-cols-2 gap-4'>
                <FormItem label='Title (English)'>
                    <Controller
                        control={control}
                        name="sectionTwoTitleEn"
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.sectionTwoTitleEn && (<small className="text-red-600 py-3"> {errors.sectionTwoTitleEn.message as string} </small>)}
                </FormItem>

                <FormItem label='Title (Arabic)'>
                    <Controller
                        control={control}
                        name="sectionTwoTitleAr"
                        // rules={{ required: 'Field Required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {/* {errors.sectionTwoTitleAr && (<small className="text-red-600 py-3"> {errors.sectionTwoTitleAr.message as string} </small>)} */}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label='Description (English)'>
                    <Controller
                        control={control}
                        name="sectionTwoDescEn"
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                textArea
                                {...field}
                            />
                        )}
                    />
                    {errors.sectionTwoDescEn && (<small className="text-red-600 py-3"> {errors.sectionTwoDescEn.message as string} </small>)}
                </FormItem>

                <FormItem label='Title (Arabic)'>
                    <Controller
                        control={control}
                        name="sectionTwoDescAr"
                        // rules={{ required: 'Field Required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                textArea
                                {...field}
                            />
                        )}
                    />
                    {/* {errors.sectionTwoDescAr && (<small className="text-red-600 py-3"> {errors.sectionTwoDescAr.message as string} </small>)} */}
                </FormItem>
            </div>

            <ul className="mt-6">
                {stepsFields.map((item, index) => {
                    return (
                        <li key={item.id} className="border-2 rounded-md border-gray-400 mt-2 py-6 px-3 relative">
                            <div className="grid grid-cols-2 gap-4">
                                <FormItem label="Title (English)">
                                    <Controller
                                        name={`steps.${index}.titleEn`}
                                        control={control}
                                        defaultValue=""
                                        rules={{ required: 'Field is required' }}
                                        render={({ field }) => (
                                            <Input
                                                type="text"
                                                {...field}
                                            />
                                        )}
                                    />
                                    {errors.steps && (errors.steps as any)[index]?.titleEn && <small className="text-red-600 py-3">{(errors.steps as any)[index]?.titleEn.message as string}</small>}
                                </FormItem>

                                <FormItem label="Title (Arabic)">
                                    <Controller
                                        name={`steps.${index}.titleAr`}
                                        control={control}
                                        defaultValue=""
                                        // rules={{ required: 'Field is required' }}
                                        render={({ field }) => (
                                            <Input
                                                type="text"
                                                dir='rtl'
                                                {...field}
                                            />
                                        )}
                                    />
                                </FormItem>
                                {/* {errors.steps && (errors.steps as any)[index]?.titleAr && <small className="text-red-600 py-3">{(errors.steps as any)[index]?.titleAr.message as string}</small>} */}
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                                <FormItem label="Description (English)">
                                    <Controller
                                        name={`steps.${index}.descEn`}
                                        control={control}
                                        defaultValue=""
                                        rules={{ required: 'Field is required' }}
                                        render={({ field }) => (
                                            <Input
                                                type="text"
                                                {...field}
                                            />
                                        )}
                                    />
                                    {errors.steps && (errors.steps as any)[index]?.descEn && <small className="text-red-600 py-3">{(errors.steps as any)[index]?.descEn.message as string}</small>}
                                </FormItem>

                                <FormItem label="Description (Arabic)">
                                    <Controller
                                        name={`steps.${index}.descAr`}
                                        control={control}
                                        defaultValue=""
                                        // rules={{ required: 'Field is required' }}
                                        render={({ field }) => (
                                            <Input
                                                type="text"
                                                dir='rtl'
                                                {...field}
                                            />
                                        )}
                                    />
                                </FormItem>
                            </div>

                            <div className="absolute right-0 top-0">
                                <IoIosCloseCircleOutline size={30} color="red" onClick={() => stepsRemove(index)} />
                            </div>

                        </li>
                    );
                })}
            </ul>

            <div className='my-2'>
                <Button variant="solid" type="button" color='green' icon={<AiOutlinePlus />} onClick={() => {
                    stepsAppend({});
                }}>
                    Add More Steps
                </Button>
            </div>

            <h5 className='mt-4'>Seo Section</h5>
            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Title English">
                    <Controller
                        name="metaTitleEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaTitleEn && (
                        <small className="text-red-600 py-3">
                            {errors.metaTitleEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Meta Title Arabic">
                    <Controller
                        name="metaTitleAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaTitleAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaTitleAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Description English">
                    <Controller
                        name="metaDescriptionEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaDescriptionEn && (
                        <small className="text-red-600 py-3">
                            {errors.metaDescriptionEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Meta Description Arabic">
                    <Controller
                        name="metaDescriptionAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaDescriptionAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaDescriptionAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Keywords English">
                    <Controller
                        name="metaKeywordsEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaKeywordsEn && (
                        <small className="text-red-600 py-3">
                            {errors.metaKeywordsEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Meta Keywords Arabic">
                    <Controller
                        name="metaKeywordsAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaKeywordsAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaKeywordsAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Canonical URL English">
                    <Controller
                        name="metaCanonicalUrl"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaCanonicalUrl && (
                        <small className="text-red-600 py-3">
                            {errors.metaCanonicalUrl.message as string}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Meta Canonical URL Arabic">
                    <Controller
                        name="metaCanonicalUrlAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaCanonicalUrlAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaCanonicalUrlAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <FormItem label="Meta OG Image">
                <Dropzone ratio={[1074, 616]} previews={ogImageFile} onFileUrlChange={(e) => { setOgImageFile([e]) }} />
            </FormItem>

            <Button
                className="float-right mt-4"
                variant="solid"
                type="submit"
                icon={<AiOutlineSave />}
            >
                {isUpdate ? 'Update' : 'Save'}
            </Button>
        </form>
    )
}
