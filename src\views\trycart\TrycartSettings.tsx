
import { Button, FormItem, toast, Select } from "@/components/ui";
import endpoints from "@/endpoints";
import api from "@/services/api.interceptor";
import { Controller, useForm } from "react-hook-form";
import { AiOutlineSave } from "react-icons/ai";
import Notification from '@/components/ui/Notification'
import Input from '@/components/ui/Input'
import { useEffect, useState } from "react";
import Breadcrumb from "../modals/BreadCrumb";

/* eslint-disable */
export default function TrycartSettings() {
  const [productOptions, setProductOptions] = useState([])
  const [isUpdate, setIsUpdate] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  const breadcrumbItems = [
    { title: 'Try Cart Settings', url: '' },
  ]

  function getProducts() {
    api.post(endpoints.products).then((res) => {
      if (res?.status == 200) {
        const products = res?.data?.result?.products || []
        const newproductOptions = products.map((product: any) => ({
          value: product._id,
          label: product.name.en
        }));
        setProductOptions(newproductOptions);
      }
    }).catch((error) => {
      console.error('Error fetching data: ', error)
    })
  }

  const {
    handleSubmit,
    setValue,
    control,
    formState: { errors },
  } = useForm<any>();


  useEffect(() => {
    getProducts()

    api.get(endpoints.getTryCart).then((res) => {
      if (res?.status == 200) {
        if (res?.data?.result.length > 0) {
          setIsUpdate(true)
          const data = res?.data?.result[0]
          setValue('duration', data?.duration)
          setValue('amount', data.amount)
          // setValue('products', data?.products?.map((product: any) => ({
          //   value: product?._id, label: product?.name.en
          // })))
        }
      }
    }).catch((error) => {
      console.error('Error fetching data: ', error)
    })

  }, [isSubmitted])

  const onSubmit = (value: any) => {
    console.log(value)

    const data: any = {
      duration: value?.duration,
      amount: value?.amount,
      // products: value?.products?.map((product: any) => product?.value)
    }

    if (isUpdate) {
      api.put(endpoints.updateTryCart, data).then((res) => {
        if (res.status == 200) {
          toast.push(
            <Notification type="success" title={res.data.message} />, {
            placement: 'top-center',
          })
          setIsSubmitted(true)
        }
      }).catch((err) => {
        console.log(err);
      })
    }
    else {
      api.post(endpoints.createTryCart, data).then((res) => {
        if (res.status == 200) {
          toast.push(
            <Notification type="success" title={res.data.message} />, {
            placement: 'top-center',
          })
          setIsSubmitted(true)
        }
      }).catch((err) => {
        console.log(err);
      })
    };
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <h3 className="mb-2">{isUpdate ? 'Edit' : 'Add'} Try Cart Settings </h3>
      <Breadcrumb items={breadcrumbItems} />
      <div className="grid grid-cols-1 mt-4">
        {/* <FormItem label="Products">
          <Controller
            name="products"
            control={control}
            defaultValue=""
            rules={{ required: 'Product is required' }}
            render={({ field }) => (
              <Select
                {...field}
                isMulti={true}
                options={productOptions}
              />
            )}
          />
          {errors.products && <small className="text-red-600 py-3">{errors.products.message as string}</small>}
        </FormItem> */}

      </div>

      <div className="grid grid-cols-2 gap-4">
        <FormItem label="Duration">
          <Controller
            name="duration"
            control={control}
            defaultValue=""
            rules={{ required: 'Duration is required' }}
            render={({ field }) => (
              <Input
                type="number"
                className={`${errors.duration ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                {...field}
              />
            )}
          />
          {errors.duration && (
            <small className="text-red-600 py-3">
              {errors.duration.message as string}
            </small>
          )}
        </FormItem>

        <FormItem label="Amount">
          <Controller
            name="amount"
            control={control}
            defaultValue=""
            rules={{ required: 'Amount is required' }}
            render={({ field }) => (
              <Input
                type="number"
                className={`${errors.amount ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                {...field}
              />
            )}
          />
          {errors.amount && <small className="text-red-600 py-3">{errors.amount.message as string}</small>}
        </FormItem>
      </div>

      <Button className='float-right mt-4' variant="solid" type="submit" icon={<AiOutlineSave />}>
        Save
      </Button>

    </form>
  )
}
