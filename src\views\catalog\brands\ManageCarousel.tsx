import { FormItem, Input, Select } from '@/components/ui'
import endpoints from '@/endpoints'
import api from '@/services/api.interceptor'
import React, { useEffect, useState } from 'react'

function ManageCarousel({ pageSection, handleContentChange, productSelect, brand }: any) {


    const [productValue, setProductValue] = useState('')
    const [isLoading, setIsLoading] = useState(false)
    const [productOptions, setProductOptions] = useState<any>([])

    useEffect(() => {
        const controller = new AbortController()
        const limit = productValue ? 60 : 30;
        setIsLoading(true)
        api.post(endpoints.products + `?page=1&limit=${limit}`, { isActive: true, keyword: productValue, brand: brand?._id }, {
            signal: controller.signal
        }).then((res) => {
            if (res?.status == 200) {
                const products = res?.data?.result?.products || []
                let newproductOptions: any = products.map((product: any) => ({
                    value: product._id,
                    label: product.name.en,
                }))
                setProductOptions([...newproductOptions])
            }
            setIsLoading(false)
        }).catch((error) => {
            console.error('Error fetching data: ', error)
            // setIsLoading(false)
        })
        return () => {
            controller.abort()
        }
    }, [productValue, brand])

    return (
        <>
            <h5>Featured Products Section</h5>
            <div className="grid grid-cols-2 gap-2">
                <FormItem label="Title English">
                    <Input
                        value={pageSection?.name?.en}
                        onChange={(e) => handleContentChange(e.target.value, pageSection?.id, "name.en")}
                    />
                </FormItem>
                <FormItem label="Title Arabic">
                    <Input
                        value={pageSection?.name?.ar}
                        onChange={(e) => handleContentChange(e.target.value, pageSection?.id, "name.ar")}
                    />
                </FormItem>
            </div>
            <FormItem label="Products">
                <Select
                    onInputChange={(value) => setProductValue(value)}
                    options={productOptions}
                    isLoading={isLoading}
                    isMulti
                    value={pageSection?.products}
                    onChange={productSelect}
                    placeholder="Select Products"

                />

            </FormItem>
        </>
    )
}

export default ManageCarousel