import { Button, FormItem, toast, Select, InputGroup } from '@/components/ui'
import endpoints from '@/endpoints'
import api from '@/services/api.interceptor'
import { Controller, useForm } from 'react-hook-form'
import { AiOutlineSave } from 'react-icons/ai'
import Notification from '@/components/ui/Notification'
import { useNavigate, useParams } from 'react-router-dom'
import Input from '@/components/ui/Input'
import { useEffect } from 'react'
import Breadcrumb from '../modals/BreadCrumb'

/* eslint-disable */
export default function ManagePlans() {
    const navigate = useNavigate()
    const params = useParams()

    const breadcrumbItems = [
        { title: 'Subscription Plans', url: '/subscription-plans' },
        {
            title: params?.id
                ? 'Update Subscription Plan'
                : 'Create Subscription Plan',
            url: '',
        },
    ]

    const options: any = [
        { value: 'true', label: 'True' },
        { value: 'false', label: 'False' },
    ]

    const typeOptions = [
        { value: 'Weekly', label: 'Weekly' },
        { value: 'Monthly', label: 'Monthly' },
        { value: 'Yearly', label: 'Yearly' },
    ]

    const {
        handleSubmit,
        setValue,
        control,
        formState: { errors },
    } = useForm<any>()

    useEffect(() => {
        if (params.id)
            api.get(endpoints.planDetail + params.id)
                .then((res) => {
                    if (res?.status == 200) {
                        setValue('name', res.data?.result?.name?.en)
                        setValue('nameAr', res.data?.result?.name?.ar)
                        setValue('description', res.data.result.description?.en)
                        setValue(
                            'descriptionAr',
                            res.data.result.description?.ar
                        )
                        setValue(
                            'discountPercent',
                            res.data?.result?.discountPercent
                        )
                        setValue('duration', res.data.result.duration.duration)
                        setValue('type', {
                            value: res.data.result.duration.type,
                            label: res.data.result.duration.type,
                        })
                        setValue('isActive', {
                            value: res.data.result.isActive,
                            label: res.data.result.isActive ? 'True' : 'False',
                        })
                    }
                })
                .catch((error) => {
                    if(error?.response?.status == 422) navigate('/access-denied');
                    console.error('Error fetching data: ', error)
                })
    }, [])

    const onSubmit = (value: any) => {
        const data: any = {
            name: { en: value.name, ar: value.nameAr },
            description: { en: value.description, ar: value?.descriptionAr },
            discountPercent: value.discountPercent,
            duration: {
                type: value.type?.value || typeOptions[0].value,
                duration: value.duration,
            },
        }
        if (params.id) {
            data.isActive = value.isActive?.value

            api.put(endpoints.updatePlan + params.id, data)
                .then((res) => {
                    if (res.status == 200) {
                        toast.push(
                            <Notification
                                type="success"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                        navigate('/subscription-plans')
                    }
                })
                .catch((err) => {
                    console.log(err)
                })
        } else {
            api.post(endpoints.createPlan, data)
                .then((res) => {
                    if (res.status == 200) {
                        toast.push(
                            <Notification
                                type="success"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                        navigate('/subscription-plans')
                    }
                })
                .catch((err) => {
                    console.log(err)
                })
        }
    }

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <h3 className="mb-6">
                {params.id ? 'Edit' : 'Add'} Subscription Plan{' '}
            </h3>
            <Breadcrumb items={breadcrumbItems} />
            <div className="grid grid-cols-2 md:grid-cols-2 gap-4">
                <FormItem label="Name">
                    <Controller
                        name="name"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Name is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                className={`${
                                    errors.name
                                        ? ' input input-md h-11 input-invalid'
                                        : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                }`}
                                {...field}
                            />
                        )}
                    />
                    {errors.name && (
                        <small className="text-red-600 py-3">
                            {errors.name.message as string}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Name Arabic">
                    <Controller
                        name="nameAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Name is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                className={`${
                                    errors.name
                                        ? ' input input-md h-11 input-invalid'
                                        : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                }`}
                                {...field}
                            />
                        )}
                    />
                    {errors.nameAr && (
                        <small className="text-red-600 py-3">
                            {errors.nameAr.message as string}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Description">
                    <Controller
                        name="description"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'description is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                className={`${
                                    errors.description
                                        ? ' input input-md h-11 input-invalid'
                                        : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                }`}
                                {...field}
                            />
                        )}
                    />
                    {errors.description && (
                        <small className="text-red-600 py-3">
                            {errors.description.message as string}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Description Arabic">
                    <Controller
                        name="descriptionAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'description is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                className={`${
                                    errors.description
                                        ? ' input input-md h-11 input-invalid'
                                        : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                }`}
                                {...field}
                            />
                        )}
                    />
                    {errors.descriptionAr && (
                        <small className="text-red-600 py-3">
                            {errors.descriptionAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-2 gap-4">
                <FormItem label="Discount (%)">
                    <Controller
                        name="discountPercent"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                min={0}
                                type="Number"
                                className={`${
                                    errors.discountPercent
                                        ? ' input input-md h-11 input-invalid'
                                        : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                }`}
                                {...field}
                            />
                        )}
                    />
                    {errors.discountPercent && (
                        <small className="text-red-600 py-3">
                            {errors.discountPercent.message as string}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Duration">
                    <Controller
                        name="duration"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Duration is required' }}
                        render={({ field }) => (
                            <InputGroup>
                                <Input
                                    type="number"
                                    className={`${
                                        errors.duration
                                            ? 'input input-md h-11 input-invalid'
                                            : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                    }`}
                                    {...field}
                                />

                                <Controller
                                    control={control}
                                    name="type"
                                    render={({ field }) => (
                                        <Select
                                            className="w-36 font-bold"
                                            defaultValue={typeOptions[0]}
                                            placeholder=""
                                            options={typeOptions}
                                            isSearchable={false}
                                            {...field}
                                        />
                                    )}
                                />
                            </InputGroup>
                        )}
                    />
                    {errors.duration && (
                        <small className="text-red-600 py-3">
                            {errors.duration.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            {params?.id && (
                <div className="grid grid-cols-2 md:grid-cols-2 gap-4">
                    <FormItem label="isActive ?">
                        <Controller
                            control={control}
                            name="isActive"
                            render={({ field }) => (
                                <Select
                                    className="font-bold"
                                    placeholder=""
                                    options={options}
                                    isSearchable={false}
                                    {...field}
                                />
                            )}
                        />
                    </FormItem>
                </div>
            )}

            <Button
                className="float-right mt-4"
                variant="solid"
                type="submit"
                icon={<AiOutlineSave />}
            >
                Save
            </Button>
        </form>
    )
}
