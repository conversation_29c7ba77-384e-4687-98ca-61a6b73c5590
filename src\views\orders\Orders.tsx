/* eslint-disable */
import { useState, useMemo, useEffect, InputHTMLAttributes } from 'react'
import Table from '@/components/ui/Table'
import Pagination from '@/components/ui/Pagination'
import Select from '@/components/ui/Select'
import {
    useReactTable,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    flexRender,
    getSortedRowModel,
    getFacetedRowModel,
    getFacetedUniqueValues,
} from '@tanstack/react-table'
import type {
    Column,
    ColumnDef,
    ColumnFiltersState,
    FilterFn,
} from '@tanstack/react-table'
import Input from '@/components/ui/Input'
import { rankItem } from '@tanstack/match-sorter-utils'
import api from '@/services/api.interceptor'
import endpoints from '@/endpoints'
import { Link, useLocation, useSearchParams } from 'react-router-dom'
import { HiPencilSquare } from 'react-icons/hi2'
import Breadcrumb from '../modals/BreadCrumb'
import { Button, Checkbox, DatePicker, FormItem, Notification, Tag, toast } from '@/components/ui'
import { MdOutlineFileDownload } from 'react-icons/md'
import { mkConfig, generateCsv, download } from 'export-to-csv'
import Filter from '@/components/shared/TableFilter'
import { BiSelectMultiple, BiSolidSelectMultiple } from "react-icons/bi";
import { MdDelete } from "react-icons/md";

const csvConfig = mkConfig({
    fieldSeparator: ',',
    decimalSeparator: '.',
    useKeysAsHeaders: true,
})
interface DebouncedInputProps
    extends Omit<
        InputHTMLAttributes<HTMLInputElement>,
        'onChange' | 'size' | 'prefix'
    > {
    value: string | number
    onChange: (value: string | number) => void
    debounce?: number
}

const { Tr, Th, Td, THead, TBody, Sorter } = Table

function DebouncedInput({
    value: initialValue,
    onChange,
    debounce = 500,
    ...props
}: DebouncedInputProps) {
    const [value, setValue] = useState(initialValue)

    useEffect(() => {
        setValue(initialValue)
    }, [initialValue])

    useEffect(() => {
        const timeout = setTimeout(() => {
            onChange(value)
        }, debounce)

        return () => clearTimeout(timeout)
    }, [value])

    return (
        <div className="flex justify-end">
            <div className="flex items-center mb-4">
                <Input
                    {...props}
                    value={value}
                    onChange={(e) => setValue(e.target.value)}
                />
            </div>
        </div>
    )
}

type Option = {
    value: number
    label: string
}

const pageSizeOption = [
    { value: 10, label: '10 / page' },
    { value: 20, label: '20 / page' },
    { value: 30, label: '30 / page' },
    { value: 40, label: '40 / page' },
    { value: 50, label: '50 / page' },
]

const breadcrumbItems = [{ title: 'Orders', url: '' }]

const orderStatusOptions: any = [
    {
        value: '',
        label: 'All',
    },
    {
        value: 'PENDING',
        label: 'PENDING',
    },
    {
        value: 'PLACED',
        label: 'PLACED',
    },
    {
        value: 'CONFIRMED',
        label: 'CONFIRMED',
    },
    {
        value: 'SHIPPED VIA ECO',
        label: 'SHIPPED VIA ECO',
    },
    {
        value: 'SHIPPED VIA INHOUSE',
        label: 'SHIPPED VIA INHOUSE',
    },
    {
        value: 'OUT FOR DELIVERY',
        label: 'OUT FOR DELIVERY',
    },
    {
        value: 'DELIVERED',
        label: 'DELIVERED',
    },
    {
        value: 'CANCELLED',
        label: 'CANCELLED',
    },
    {
        value: 'FAILED',
        label: 'FAILED',
    },
    {
        value: 'RETURNED',
        label: 'RETURNED',
    },
    {
        value: 'REFUNDED',
        label: 'REFUNDED',
    },
]

const Orders = () => {
    const [params] = useSearchParams()

    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
    const [backendFilter, setBackendFilter] = useState<ColumnFiltersState>([])
    const [globalFilter, setGlobalFilter] = useState('')
    const [fromDate, setFromDate] = useState<any>(params.get('from') ?? null)
    const [toDate, setToDate] = useState<any>(params.get('to') ?? null)

    const [isMultiSelect, setIsMultiSelect] = useState(false)
    const [selectedOrders, setSelectedOrders] = useState<any>([])

    const location = useLocation()
    const [totalData, setTotalData] = useState(0)
    const [page, setPage] = useState(params.get('page') ?? 1)
    const [limit, setLimit] = useState(params.get('limit') ?? 10)

    const getStatusClassName = (status: any) => {
        switch (status) {
            case 'PENDING':
                return 'bg-amber-100 text-amber-600 dark:bg-amber-500/20 dark:text-amber-100 border-0 rounded'
            case 'PLACED':
                return 'bg-blue-100 text-blue-600 dark:bg-blue-500/20 dark:text-blue-100 border-0 rounded'
            case 'READY TO SHIP':
                return 'bg-indigo-100 text-indigo-600 dark:bg-indigo-500/20 dark:text-indigo-100 border-0 rounded'
            case 'SHIPPED VIA ECO':
                return 'bg-lime-100 text-lime-600 dark:bg-lime-500/20 dark:text-lime-100 border-0 rounded'
            case 'SHIPPED VIA INHOUSE':
                return 'bg-lime-100 text-lime-600 dark:bg-lime-500/20 dark:text-lime-100 border-0 rounded'
            case 'OUT FOR DELIVERY':
                return 'bg-purple-100 text-purple-600 dark:bg-purple-500/20 dark:text-purple-100 border-0 rounded'
            case 'DELIVERED':
                return 'bg-emerald-100 text-emerald-600 dark:bg-emerald-500/20 dark:text-emerald-100 border-0 rounded'
            case 'FAILED':
                return 'bg-red-100 text-red-600 dark:bg-red-500/20 dark:text-red-100 border-0 rounded'
            case 'RETURNED':
                return 'bg-pink-100 text-pink-600 dark:bg-pink-500/20 dark:text-pink-100 border-0 rounded'
            case 'REFUNDED':
                return 'bg-violet-100 text-violet-600 dark:bg-violet-500/20 dark:text-violet-100 border-0 rounded'
            case 'CANCELLED':
                return 'bg-red-100 text-red-600 dark:bg-red-500/20 dark:text-red-100 border-0 rounded'
            case 'CONFIRMED':
                // You can either leave this blank or provide default styling
                return 'bg-gray-100 text-gray-600 dark:bg-gray-500/20 dark:text-gray-100 border-0 rounded'
            default:
                return 'bg-gray-100 text-gray-600 dark:bg-gray-500/20 dark:text-gray-100 border-0 rounded'
        }
    }

    const columns = useMemo<ColumnDef<any>[]>(
        () => [
            {
                header: 'Sl No.',
                accessorKey: 'slNo',
                cell: (info) => {
                    return isMultiSelect ?
                        <Checkbox checked={selectedOrders.includes(info.row.original.orderNo)}
                            onChange={(checked: boolean) => {
                                console.log(checked ? [...selectedOrders, info.row.original.orderNo] : selectedOrders.filter((item: any) => item !== info.row.original.orderNo))
                                setSelectedOrders(checked ? [...selectedOrders, info.row.original.orderNo] : selectedOrders.filter((item: any) => item !== info.row.original.orderNo))
                            }} />
                        :
                        info.row.index + 1 + ((Number(page) - 1) * (Number(limit)))
                },
                enableColumnFilter: false,
                enableSorting: false,
            },
            {
                header: 'Order',
                accessorKey: 'orderNo',
                enableSorting: false,
                cell: (info: any) => (
                    <Link to={`/manage-orders/${info?.row?.original?.orderNo}`}>
                        {info.getValue()}
                    </Link>
                ),
            },
            {
                header: 'Placed Date',
                accessorKey: 'orderDate',
                enableColumnFilter: false,
                enableSorting: false,
                cell: (info) => {
                    const date = info.getValue() as any
                    const dateObj = new Date(date)
                    const formattedDate = `${dateObj.getMonth() + 1
                        }/${dateObj.getDate()}/${dateObj.getFullYear()} ${dateObj.getHours()}:${dateObj.getMinutes()}:${dateObj.getSeconds()}`
                    return <div>{formattedDate}</div>
                },
            },
            {
                header: 'Customer',
                accessorKey: 'addressDetails.name',
                enableSorting: false,
                cell: (info) =>
                    info.getValue()
                        ? info.getValue()
                        : info.row.original.address.name ??
                        info.row.original.customer.name,
            },
            {
                header: 'Mobile',
                accessorKey: 'customer.mobile',
                enableSorting: false,
            },
            {
                header: 'Total',
                accessorKey: 'total',
                enableColumnFilter: false,
                enableSorting: false,
            },
            {
                header: 'Method',
                accessorKey: 'paymentMethod',
                enableSorting: false,
            },
            {
                header: 'Order Status',
                accessorKey: 'orderStatus',
                enableSorting: false,
                cell: (info: any) => {
                    const status = info.getValue()
                    return (
                        <div>
                            <Tag className={getStatusClassName(status)}>
                                {status}
                            </Tag>
                        </div>
                    )
                },
            },
            {
                header: 'Actions',
                accessorKey: '',
                enableColumnFilter: false,
                enableSorting: false,
                cell: (info) => {
                    return (
                        <div>
                            <Link to={`/manage-orders/${info?.row?.original?.orderNo}`}>
                                <HiPencilSquare size={25} />
                            </Link>
                        </div>
                    )
                },
            },
        ],
        [limit, page, isMultiSelect, selectedOrders]
    )

    const [data, setData] = useState<any[]>([])
    const [exportLoading, setExportLoading] = useState(false)

    const fuzzyFilter: FilterFn<any> = (row, columnId, value, addMeta) => {
        const itemRank = rankItem(row.getValue(columnId), value)

        addMeta({
            itemRank,
        })

        return itemRank.passed
    }

    const table = useReactTable({
        data,
        columns,
        initialState: {
            pagination: {
                pageSize: Number(limit),
            },
        },
        filterFns: {
            fuzzy: fuzzyFilter,
        },
        state: {
            columnFilters,
            globalFilter,
        },
        onColumnFiltersChange: setColumnFilters,
        onGlobalFilterChange: setGlobalFilter,
        globalFilterFn: fuzzyFilter,
        getSortedRowModel: getSortedRowModel(),
        getCoreRowModel: getCoreRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getFacetedRowModel: getFacetedRowModel(),
        getFacetedUniqueValues: getFacetedUniqueValues(),
    })

    const onPaginationChange = (page: number) => {
        table.setPageIndex(page - 1)
        setPage(page)
    }

    const onSelectChange = (value = 0) => {
        table.setPageSize(Number(value))
    }

    const convertValuesReducer = (result: any, item: any) => {
        let key = item[Object.keys(item)[0]]
        let value = item[Object.keys(item)[1]]
        result[key] = value
        return result
    }

    const getOrders = (controller: any) => {
        const payload = {
            fromDate: fromDate,
            toDate: toDate,
            isActive: true,
            keyword: globalFilter,
            filters: backendFilter.reduce(convertValuesReducer, {}),
        }
        api.post(endpoints.orders + `?page=${page}&limit=${limit}`, payload, {
            signal: controller?.signal,
        })
            .then((res) => {
                setData(res?.data?.result?.orders)
                setTotalData(res?.data?.result?.total)
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }

    const bulkDeleteOrders = () => {
        if (selectedOrders.length === 0) return;
        api.put(endpoints.bulkDeleteOrders, { orderIds: selectedOrders })
            .then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification
                            type="success"
                            title={res.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    setIsMultiSelect(false)
                    setSelectedOrders([])
                    getOrders({})
                    table.setPageIndex(0)
                }
            })
            .catch((err) => {
                toast.push(
                    <Notification
                        type="warning"
                        title={err.response.data.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )
            })
    }

    useEffect(() => {
        const controller = new AbortController()
        getOrders(controller)
        const newParams = new URLSearchParams(params.toString())
        if (!newParams.get('page')) {
            newParams.append('page', `${page}`)
        } else {
            newParams.delete('page')
            newParams.append('page', `${page}`)
        }
        if (!newParams.get('limit')) {
            newParams.append('limit', `${limit}`)
        } else {
            newParams.delete('limit')
            newParams.append('limit', `${limit}`)
        }

        if (!newParams.get('from') && fromDate) {
            newParams.append('from', `${fromDate}`)
        } else if (fromDate) {
            newParams.delete('from')
            newParams.append('from', `${fromDate}`)
        } else {
            newParams.delete('from')
        }
        if (!newParams.get('to') && toDate) {
            newParams.append('to', `${toDate}`)
        } else if (toDate) {
            newParams.delete('to')
            newParams.append('to', `${toDate}`)
        } else {
            newParams.delete('to')
        }
        window.history.pushState(
            null,
            '',
            `${location.pathname}?${newParams.toString()}`
        )
        return () => {
            controller.abort()
        }
    }, [page, limit, globalFilter, fromDate, toDate, backendFilter])

    const handleExportRows = (rows: any) => {
        const rowData = rows.map((row: any) => row.original)
        console.log('rowData', rowData)
        const csv = generateCsv(csvConfig)(rowData)
        download(csvConfig)(csv)
    }

    const clearFilters = () => {
        setColumnFilters([])
        setGlobalFilter('')
    }

    const exportToCSV = () => {
        setExportLoading(true)
        const payload = {
            fromDate: fromDate,
            toDate: toDate,
            isActive: true,
            keyword: globalFilter,
            filters: backendFilter.reduce(convertValuesReducer, {}),
        }
        api.post(endpoints.exportOrders, payload)
            .then((res) => {
                const link = document.createElement('a')
                link.href = `data:text/csv;charset=utf-8,${escape(res.data)}`
                link.setAttribute('download', 'orders.csv')
                document.body.appendChild(link)
                link.click()
                link.remove()
                setExportLoading(false)
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
                setExportLoading(false)
            })
    }

    const onSearch = (value: any) => {
        setGlobalFilter(String(value))
        if (value) {
            setPage(1)
        }
    }

    const statusFilter = (option: any, name: string) => {
        let filters = backendFilter
        const index = filters.findIndex((item) => item.id == name)
        if (option.value) {
            if (index != -1) filters[index].value = option.value
            else filters.push({ id: name, value: option.value })
        } else {
            if (index != -1) filters.splice(index, 1)
        }
        setBackendFilter([...filters])
    }

    return (
        <div>
            <div className="mb-4 flex">
                <h2>Orders</h2>
            </div>
            <Breadcrumb items={breadcrumbItems} />
            <div className="flex gap-3 justify-end mb-4 items-center">
                <FormItem label="Status" className="mb-5">
                    <Select
                        className="w-[200px]"
                        placeholder="Status"
                        options={orderStatusOptions}
                        defaultValue={orderStatusOptions[0]}
                        onChange={(option) =>
                            statusFilter(option, 'orderStatus')
                        }
                    />
                </FormItem>
                <div className="w-[200px]">
                    <DatePicker
                        value={fromDate ? new Date(fromDate) : null}
                        placeholder="From Date"
                        onChange={(date: Date | null) => {
                            console.log('setFromDate', date)
                            setFromDate(date)
                        }}
                    />
                </div>
                <div className="w-[200px]">
                    <DatePicker
                        value={toDate ? new Date(toDate) : null}
                        placeholder="To Date"
                        onChange={(date: Date | null) => {
                            setToDate(date)
                        }}
                    />
                </div>
            </div>
            <div className="mb-4 flex space-x-2 justify-end">
                <Button
                    variant="twoTone"
                    color="indigo-600"
                    className={`mr-2 mb-2 ${isMultiSelect ? "bg-indigo-600 text-white hover:bg-indigo-700" : ""}`}
                    onClick={() => setIsMultiSelect(!isMultiSelect)}
                    icon={
                        isMultiSelect ? (
                            <BiSolidSelectMultiple />
                        ) : (
                            <BiSelectMultiple />
                        )
                    }
                >
                    Multi Select
                </Button>
                {isMultiSelect && <Button
                    variant="solid" color="red"
                    className={`mr-2 mb-2 `}
                    onClick={bulkDeleteOrders}
                    icon={<MdDelete />}
                >
                    Delete All
                </Button>}
                {!isMultiSelect && <> <Button
                    loading={exportLoading}
                    variant="twoTone"
                    color="indigo-600"
                    className="mr-2 mb-2"
                    onClick={exportToCSV}
                    icon={<MdOutlineFileDownload />}
                >
                    Export Orders
                </Button>
                    <DebouncedInput
                        value={globalFilter ?? ''}
                        className="p-2 font-lg shadow border border-block"
                        placeholder="Search orders..."
                        // onChange={(value) => setGlobalFilter(String(value))}
                        onChange={onSearch}
                    />
                    <Button variant="solid" color="red" onClick={clearFilters}>
                        Clear Filters
                    </Button>
                </>}
            </div>
            <Table>
                <THead>
                    {table.getHeaderGroups().map((headerGroup) => (
                        <Tr key={headerGroup.id}>
                            {headerGroup.headers.map((header) => {
                                return (
                                    <Th
                                        key={header.id}
                                        colSpan={header.colSpan}
                                    >
                                        {header.column.getCanSort() ? (
                                            <div
                                                className="cursor-pointer select-none"
                                                onClick={header.column.getToggleSortingHandler()}
                                            >
                                                {flexRender(
                                                    header.column.columnDef
                                                        .header,
                                                    header.getContext()
                                                )}
                                                <Sorter
                                                    sort={header.column.getIsSorted()}
                                                />
                                            </div>
                                        ) : (
                                            <div>
                                                {flexRender(
                                                    header.column.columnDef
                                                        .header,
                                                    header.getContext()
                                                )}
                                            </div>
                                        )}
                                        <div>
                                            {header.column.getCanFilter() ? (
                                                <div>
                                                    <Filter
                                                        column={header.column}
                                                        table={table}
                                                    />
                                                </div>
                                            ) : null}
                                        </div>
                                    </Th>
                                )
                            })}
                        </Tr>
                    ))}
                </THead>
                <TBody>
                    {table.getRowModel().rows.map((row) => {
                        return (
                            <Tr key={row.id}>
                                {row.getVisibleCells().map((cell) => {
                                    return (
                                        <Td key={cell.id}>
                                            {flexRender(
                                                cell.column.columnDef.cell,
                                                cell.getContext()
                                            )}
                                        </Td>
                                    )
                                })}
                            </Tr>
                        )
                    })}
                </TBody>
            </Table>
            <div className="flex items-center justify-between mt-4">
                <Pagination
                    pageSize={Number(limit)}
                    currentPage={Number(page)}
                    total={totalData}
                    onChange={onPaginationChange}
                />
                <div style={{ minWidth: 130 }}>
                    <Select<Option>
                        size="sm"
                        isSearchable={false}
                        value={pageSizeOption.filter(
                            (option) => option.value === Number(limit)
                        )}
                        options={pageSizeOption}
                        onChange={(option) => {
                            onSelectChange(option?.value)
                            setLimit(option?.value ?? 10)
                        }}
                    />
                </div>
            </div>
        </div>
    )
}

export default Orders
