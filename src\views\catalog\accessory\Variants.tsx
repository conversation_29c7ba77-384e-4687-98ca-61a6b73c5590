import { useState, useMemo, useEffect, InputHTMLAttributes } from 'react'
import Table from '@/components/ui/Table'
import Pagination from '@/components/ui/Pagination'
import Select from '@/components/ui/Select'
import {
    useReactTable,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    flexRender,
    getSortedRowModel,
} from '@tanstack/react-table'
import type {
    ColumnDef,
    ColumnFiltersState,
    FilterFn,
} from '@tanstack/react-table'
import Input from '@/components/ui/Input'
import { rankItem } from '@tanstack/match-sorter-utils'
import Button from '@/components/ui/Button'
import { AiOutlinePlus } from 'react-icons/ai'
import api from '@/services/api.interceptor'
import endpoints from '@/endpoints'
import { Link, useNavigate, useParams } from 'react-router-dom'
import { HiPencilSquare } from 'react-icons/hi2'
import Tag from '@/components/ui/Tag'
import Breadcrumb from '@/views/modals/BreadCrumb'
import { Dialog, Skeleton, toast } from '@/components/ui'
import Notification from '@/components/ui/Notification'
import { IoIosRemoveCircle } from 'react-icons/io'

/* eslint-disable */

interface DebouncedInputProps
    extends Omit<
        InputHTMLAttributes<HTMLInputElement>,
        'onChange' | 'size' | 'prefix'
    > {
    value: string | number
    onChange: (value: string | number) => void
    debounce?: number
}

const { Tr, Th, Td, THead, TBody, Sorter } = Table
const imageUrl = import.meta.env.VITE_ASSET_URL

function DebouncedInput({
    value: initialValue,
    onChange,
    debounce = 500,
    ...props
}: DebouncedInputProps) {
    const [value, setValue] = useState(initialValue)

    useEffect(() => {
        setValue(initialValue)
    }, [initialValue])

    useEffect(() => {
        const timeout = setTimeout(() => {
            onChange(value)
        }, debounce)

        return () => clearTimeout(timeout)
    }, [value])

    return (
        <div className="flex justify-end">
            <div className="flex items-center mb-4">
                <Input
                    {...props}
                    value={value}
                    onChange={(e) => setValue(e.target.value)}
                />
            </div>
        </div>
    )
}

type Option = {
    value: number
    label: string
}

const pageSizeOption = [
    { value: 10, label: '10 / page' },
    { value: 20, label: '20 / page' },
    { value: 30, label: '30 / page' },
    { value: 40, label: '40 / page' },
    { value: 50, label: '50 / page' },
]

const VariantModal = () => {
    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
    const [globalFilter, setGlobalFilter] = useState('')
    const params = useParams()
    const navigate = useNavigate()
    const [productOptions, setProductOptions] = useState<any>([])

    function removeVariant(id: any) {
        api.put(endpoints.removeVariant + id)
            .then((res) => {
                if (res?.status == 200) {
                    toast.push(
                        <Notification
                            type="success"
                            title={res.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                }
                getVariants()
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
                toast.push(
                    <Notification
                        type="warning"
                        title="Product image is required"
                    />,
                    {
                        placement: 'top-center',
                    }
                )
            })
    }

    const breadcrumbItems = [
        { title: 'Products', url: '/catalog/products' },
        { title: 'Variants', url: '' }
    ];

    const columns = useMemo<ColumnDef<any>[]>(
        () => [
            {
                header: 'Sl No.',
                accessorKey: 'slNo',
                cell: (info) => info.row.index + 1,
                enableSorting: false,
            },
            {
                header: 'SKU',
                accessorKey: 'sku',
                enableSorting: false,
            },
            {
                header: 'Name',
                accessorKey: 'name.en',
                enableSorting: false,
            },
            {
                header: 'Brand',
                accessorKey: 'brand.name.en',
                enableSorting: false,
            },
            {
                header: 'Category',
                accessorKey: 'category',
                enableSorting: false,
                cell: (info) => {
                    const categories = info.getValue() as any[]
                    const categoryNames = categories
                        .map((cat) => cat.name.en)
                        .join(', ')
                    return <div>{categoryNames}</div>
                },
            },
            {
                header: 'Stock',
                accessorKey: 'stock',
                enableSorting: false,
            },
            {
                header: 'Status',
                accessorKey: 'isActive',
                enableSorting: false,
                cell: (info) => {
                    const isActive = info.getValue()
                    return (
                        <div>
                            <Tag
                                className={
                                    isActive
                                        ? 'bg-emerald-100 text-emerald-600 dark:bg-emerald-500/20 dark:text-emerald-100 border-0 rounded'
                                        : 'text-red-600 bg-red-100 dark:text-red-100 dark:bg-red-500/20 border-0 rounded'
                                }
                            >
                                {isActive ? 'Active' : 'Inactive'}
                            </Tag>
                        </div>
                    )
                },
            },
            {
                header: 'Actions',
                accessorKey: 'refid',
                enableSorting: false,
                cell: (info) => {
                    return (
                        <div className='flex gap-2'>
                            <Link
                                to={`/catalog/manage-products/Edit-Variant/${info.getValue()}/${params.id}`}
                            >
                                <HiPencilSquare size={25} />
                            </Link>
                            <Button type='button' title='Remove variant' className='border-0 !p-0 !m-0 !h-fit' onClick={() => removeVariant(info.getValue())}>
                                <IoIosRemoveCircle size={25} color='#d61c32' />
                            </Button>
                        </div>
                    )
                },
            },
        ],
        []
    )
    const [productValue, setProductValue] = useState("")
    const [show, setShow] = useState(false)
    const [isLoading, setIsLoading] = useState(true)
    const [data, setData] = useState<any[]>([])
    const [product, setProduct] = useState<any>({})
    const totalData = data?.length

    const fuzzyFilter: FilterFn<any> = (row, columnId, value, addMeta) => {
        const itemRank = rankItem(row.getValue(columnId), value)

        addMeta({
            itemRank,
        })

        return itemRank.passed
    }

    const table = useReactTable({
        data,
        columns,
        filterFns: {
            fuzzy: fuzzyFilter,
        },
        state: {
            columnFilters,
            globalFilter,
        },
        onColumnFiltersChange: setColumnFilters,
        onGlobalFilterChange: setGlobalFilter,
        globalFilterFn: fuzzyFilter,
        getSortedRowModel: getSortedRowModel(),
        getCoreRowModel: getCoreRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
    })

    const onPaginationChange = (page: number) => {
        table.setPageIndex(page - 1)
    }

    const onSelectChange = (value = 0) => {
        table.setPageSize(Number(value))
    }

    function getProductDetail() {
        api.get(endpoints.productDetail + params?.id)
            .then((res) => {
                if (res?.status == 200) {
                    setProduct(res?.data?.result)
                }
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }

    function getVariants() {
        api.get(endpoints.getVariant + params?.id)
            .then((res) => {
                setData(res?.data?.result?.variants)
                setProduct(res?.data?.result?.main)
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }

    useEffect(() => {
        getVariants()
        // getProductDetail()
    }, [])

    useEffect(() => {
        const controller = new AbortController()
        const limit = productValue ? 60 : 30;
        setIsLoading(true)
        api.post(endpoints.products + `?page=1&limit=${limit}`, { isActive: true, keyword: productValue }, {
            signal: controller.signal
        }).then((res) => {
            if (res?.status == 200) {
                const products = res?.data?.result?.products || []
                const newproductOptions = products.map((product: any) => ({
                    value: product.refid,
                    label: product.name.en,
                }))
                console.log(newproductOptions)
                setProductOptions([...newproductOptions])
            }
            setIsLoading(false)
        }).catch((error) => {
            console.error('Error fetching data: ', error)
            // setIsLoading(false)
        })
        return () => {
            controller.abort()
        }
    }, [productValue])

    function addExisting(id: any) {
        setIsLoading(true)
        api.post(endpoints.addExistingVariant, {
            refid: params.id,
            variantRefid: id
        }).then((res) => {
            if (res?.status == 200) {
                toast.push(
                    <Notification
                        type="success"
                        title={res.data.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )
            }
            getVariants()
            setShow(false)
        })
            .catch((error) => {
                console.error('Error fetching data: ', error)
                setIsLoading(false)
                toast.push(
                    <Notification
                        type="warning"
                        title="Product image is required"
                    />,
                    {
                        placement: 'top-center',
                    }
                )
            })
    }

    return (
        <div>
            <div className="mb-4 flex">
                <h2>Variants</h2>
                <div className="ml-auto flex gap-4">
                    <Button
                        onClick={() => setShow(true)}
                        variant="twoTone"
                        color="green-600"
                        icon={<AiOutlinePlus />}
                    >
                        Add Existing Product
                    </Button>
                    <Link
                        className="mr-2 mb-2 ml-auto"
                        to={`/catalog/manage-products/Add-Variant/${params?.id}`}
                    >
                        <Button
                            variant="twoTone"
                            color="green-600"
                            icon={<AiOutlinePlus />}
                        >
                            Add Variant
                        </Button>
                    </Link>
                </div>
            </div>
            <Breadcrumb items={breadcrumbItems} />
            <div className="mb-4 flex space-x-2 justify-end">
                <DebouncedInput
                    value={globalFilter ?? ''}
                    className="p-2 font-lg shadow border border-block"
                    placeholder="Search all columns..."
                    onChange={(value) => setGlobalFilter(String(value))}
                />
            </div>
            <div className="bg-gradient-to-r from-green-400 to-blue-500 text-white rounded-lg p-5 m-3 shadow-lg">
                <div className="flex items-center">
                    <img
                        src={imageUrl + product.thumbnail}
                        alt={product.name?.en}
                        className="w-16 h-16 rounded-full mr-4"
                    />
                    <div>
                        <h2 className="text-xl font-semibold">
                            {product.name?.en}
                        </h2>
                        <p className="text-gray-200">
                            {product.sku}
                        </p>
                        <p className="text-gray-200">
                            {product.brand?.name?.en}
                        </p>
                        <p className="text-gray-200">{product.color?.name?.en}</p>
                        {product.category?.map((cat: any) => (
                            <p className="text-gray-200">{cat.name?.en}</p>
                        ))}
                    </div>
                    {/* {(product && !product?.isDefaultVariant) && <Button variant='solid' className='ml-auto bg-red-500 text-white hover:bg-red-600'>Remove Variant</Button>} */}
                </div>
            </div>

            <Table>
                <THead>
                    {table.getHeaderGroups().map((headerGroup) => (
                        <Tr key={headerGroup.id}>
                            {headerGroup.headers.map((header) => {
                                return (
                                    <Th
                                        key={header.id}
                                        colSpan={header.colSpan}
                                    >
                                        <div
                                            {...{
                                                className:
                                                    header.column.getCanSort()
                                                        ? 'cursor-pointer select-none'
                                                        : '',
                                                onClick:
                                                    header.column.getToggleSortingHandler(),
                                            }}
                                        >
                                            {flexRender(
                                                header.column.columnDef.header,
                                                header.getContext()
                                            )}
                                            {
                                                <Sorter
                                                    sort={header.column.getIsSorted()}
                                                />
                                            }
                                        </div>
                                    </Th>
                                )
                            })}
                        </Tr>
                    ))}
                </THead>
                <TBody>
                    {table.getRowModel().rows.map((row) => {
                        return (
                            <Tr key={row.id}>
                                {row.getVisibleCells().map((cell) => {
                                    return (
                                        <Td key={cell.id}>
                                            {flexRender(
                                                cell.column.columnDef.cell,
                                                cell.getContext()
                                            )}
                                        </Td>
                                    )
                                })}
                            </Tr>
                        )
                    })}
                </TBody>
            </Table>
            <div className="flex items-center justify-between mt-4">
                <Pagination
                    pageSize={table.getState().pagination.pageSize}
                    currentPage={table.getState().pagination.pageIndex + 1}
                    total={totalData}
                    onChange={onPaginationChange}
                />
                <div style={{ minWidth: 130 }}>
                    <Select<Option>
                        size="sm"
                        isSearchable={false}
                        value={pageSizeOption.filter(
                            (option) =>
                                option.value ===
                                table.getState().pagination.pageSize
                        )}
                        options={pageSizeOption}
                        onChange={(option) => onSelectChange(option?.value)}
                    />
                </div>
            </div>
            {show &&
                <Dialog className="h-[85vh]" isOpen={show} onClose={() => setShow(false)}>
                    <div className="flex flex-col gap-4 mt-4 h-full">
                        <Input placeholder='Search Product..' onChange={(e) => setProductValue(e.target.value)} value={productValue} />
                        <div className="flex flex-col gap-4 overflow-y-auto">
                            {!isLoading ? productOptions.map((product: any) => (
                                <button type="button" onClick={()=> addExisting(product?.value)} key={product?.value} className="bg-white border rounded-md p-3 hover:shadow-sm text-left">
                                    <p>{product?.label}</p>
                                </button>
                            ))
                                : (
                                    <>
                                        Loading...
                                    </>
                                )
                            }
                            {
                                (!isLoading && productOptions.length < 1) && (
                                    <span>No Product found</span>
                                )
                            }
                        </div>
                    </div>
                </Dialog>
            }
        </div>
    )
}

export default VariantModal
