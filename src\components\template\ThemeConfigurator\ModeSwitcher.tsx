import { useCallback } from 'react'
import useDarkMode from '@/utils/hooks/useDarkmode'
import Switcher from '@/components/ui/Switcher'
import { Button } from '@/components/ui'
import { FiMoon, FiSun } from 'react-icons/fi'

const ModeSwitcher = () => {
    const [isDark, setIsDark] = useDarkMode()

    const onSwitchChange = useCallback(
        (checked: boolean) => {
            setIsDark(checked ? 'dark' : 'light')
        },
        [setIsDark]
    )

    return (
        <div>
            {/* <Switcher
                defaultChecked={isDark}
                onChange={(checked) => onSwitchChange(checked)}
            /> */}
            <Button
                onClick={() => onSwitchChange(!isDark)}
                variant="plain"
                size="sm"
                className="px-2"
            >
                {isDark && <FiSun />}
                {!isDark && <FiMoon />}
            </Button>
        </div>
    )
}

export default ModeSwitcher
