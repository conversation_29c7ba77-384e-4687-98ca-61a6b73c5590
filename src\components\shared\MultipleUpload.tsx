import endpoints from '@/endpoints'
import api from '@/services/api.interceptor'
import { useState, useCallback } from 'react'
import toast from '@/components/ui/toast'
import Notification from '@/components/ui/Notification'

interface MultipleUploadProps {
    // onImagesUploaded: (newFiles: string[]) => void
    imageFiles: any[]
    setImageFiles: any
    ratio?: [number, number]
    id: string
}

const MultipleUpload = ({
    // onImagesUploaded,
    imageFiles,
    setImageFiles,
    ratio,
    id,
}: MultipleUploadProps) => {
    const [files, setFiles] = useState<any>([])
    // const [previews, setPreviews] = useState<any>([])
    const assetUrl = import.meta.env.VITE_ASSET_URL
    const acceptedFileTypes = [
        'image/jpeg',
        'image/png',
        'image/webp',
        'image/svg+xml',
    ]

    const handleFileChange = async (event: any) => {
        event.preventDefault()
        const newFiles = Array.from(event.target.files)

        const formData = new FormData()
        newFiles.forEach((file: any) => {
            if (!acceptedFileTypes.includes(file.type)) {
                toast.push(
                    <Notification
                        type="warning"
                        title="File type not supported. Only JPEG, PNG ,WEBP ,SVG files are allowed."
                    />,
                    {
                        placement: 'top-center',
                    }
                )
                return
            }
            formData.append('files', file)
        })
        api.post(endpoints.multipleUpload, formData)
            .then((res: any) => {
                if (res.status == 200) {
                    console.log(res.data)

                    const newImageUrls = res.data.imageUrl.map(
                        (url: any) => url
                    )
                    setImageFiles((prevFiles: any) => {
                        const updatedFiles = [...imageFiles, ...newImageUrls]
                        // onImagesUploaded(updatedFiles) // Call the prop function with all files
                        return updatedFiles
                    })
                }
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }

    const handleDrop = async (event: any) => {
        event.preventDefault()
        const newFiles = Array.from(event.target.files)

        const formData = new FormData()
        newFiles.forEach((file: any) => {
            if (!acceptedFileTypes.includes(file.type)) {
                toast.push(
                    <Notification
                        type="warning"
                        title="File type not supported. Only JPEG, PNG and WEBP files are allowed."
                    />,
                    {
                        placement: 'top-center',
                    }
                )
                return
            }
            formData.append('files', file)
        })
        api.post(endpoints.multipleUpload, formData)
            .then((res: any) => {
                if (res.status == 200) {
                    console.log(res.data)
                    const newImageUrls = res.data.imageUrl.map(
                        (url: any) => url
                    )
                    setImageFiles((prevFiles: any) => {
                        const updatedFiles = [...imageFiles, ...newImageUrls]
                        // onImagesUploaded(updatedFiles) // Call the prop function with all files
                        return updatedFiles
                    })
                }
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }

    const handleDragOver = useCallback((event: any) => {
        event.preventDefault()
    }, [])

    const removeFile = useCallback((indexToRemove: number) => {
        setFiles((prevFiles: string[]) => {
            const updatedFiles = prevFiles.filter(
                (_, index) => index !== indexToRemove
            )
            // onImagesUploaded(updatedFiles) // Update parent when a file is removed
            return updatedFiles
        })
        setImageFiles((prevPreviews: string[]) =>
            prevPreviews.filter((_, index) => index !== indexToRemove)
        )
    }, [])

    return (
        <div className="w-full mx-auto p-2">
            <div
                className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center bg-gray-50"
                onDrop={handleDrop}
                onDragOver={handleDragOver}
            >
                <p className="text-sm text-gray-600 mb-2">
                    Support: jpeg, png, webp, svg
                </p>
                <p className="text-sm text-gray-600 mb-2">
                    Max file size: 4 MB
                </p>
                <p className="text-sm text-gray-600 mb-2">
                    Browse files or drop them here
                </p>
                <p className="text-sm text-gray-600 mb-4">
                    Preferred Size {ratio ? `${ratio[0]}×${ratio[1]} px` : ''}
                </p>
                <input
                    type="file"
                    onChange={handleFileChange}
                    accept=".jpeg,.jpg,.png,.webp,.svg"
                    id={id}
                    className="hidden"
                    multiple
                />
                <label
                    htmlFor={id}
                    className="inline-block px-4 py-2 bg-white border border-gray-300 rounded-md font-semibold text-xs text-gray-700 uppercase tracking-widest shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150 cursor-pointer"
                >
                    Browse files
                </label>
            </div>

            {imageFiles?.length > 0 && (
                <div className="mt-6">
                    <div className="grid grid-cols-3 gap-4">
                        {imageFiles?.map((file: any, index: any) => (
                            <div key={index} className="relative">
                                <img
                                    src={assetUrl + file}
                                    alt={`Preview ${index}`}
                                    className="w-full h-32 object-cover rounded-lg"
                                />
                                <button
                                    type="button"
                                    onClick={() => removeFile(index)}
                                    className="absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600 focus:outline-none"
                                >
                                    ×
                                </button>
                            </div>
                        ))}
                    </div>
                </div>
            )}
        </div>
    )
}

export default MultipleUpload
