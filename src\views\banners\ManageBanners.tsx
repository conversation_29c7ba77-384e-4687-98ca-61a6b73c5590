import { Button, FormItem, Input, Select, toast, Upload } from "@/components/ui";
import endpoints from "@/endpoints";
import api from "@/services/api.interceptor";
import { Controller, useFieldArray, useForm } from "react-hook-form";
import { AiOutlineMinus, AiOutlinePlus, AiOutlineSave } from "react-icons/ai";
import Notification from '@/components/ui/Notification'
import { useNavigate, useParams } from "react-router-dom";
import { useEffect, useState } from "react";
import { pages } from "../headerMenu/pageoptions";

// const imageUrl = import.meta.env.VITE_ASSET_URL

/* eslint-disable */
export default function ManageBanner() {
  const [imageFile, setImageFile] = useState<any>([]);
  const [imageArray, setImageArray] = useState<any>([]);
  const [imageFileMobile, setImageFileMobile] = useState<any>([]);
  const [selectedRedirectionType, setSelectedRedirectionType] = useState('');
  const [typeOptions, setTypeOptions] = useState([]);
  const [categoryOptions, setCategoryOptions] = useState([]);
  const [productOptions, setProductOptions] = useState([]);
  const [brandOptions, setBrandOptions] = useState([]);
  const [pageOptions, setPageOptions] = useState<any>(pages);
  const [collectionOptions, setCollectionOptions] = useState([]);
  const navigate = useNavigate()
  const params = useParams()

  const options: any = [
    { value: "true", label: "True" },
    { value: "false", label: "False" },
  ]

  const types: any = [
    { value: 'category', label: 'category' },
    { value: 'product', label: 'product' },
    { value: 'brand', label: 'brand' },
    { value: 'collections', label: 'collections' },
    { value: 'page', label: 'page' },
  ]

  function getCategories() {
    api.get(endpoints.parentCategories).then((res) => {
      if (res?.status == 200) {
        const categories = res?.data?.result || []
        const newCategoryOptions = categories.map((category: any) => ({
          value: category?._id,
          label: category?.name.en,
          slug: category?.slug
        }));
        setCategoryOptions(newCategoryOptions);
      }
    }).catch((error) => {
      console.error('Error fetching data: ', error)
    })
  }

  function getBrands() {
    api.get(endpoints.brands).then((res) => {
      if (res?.status == 200) {
        const brands = res?.data?.result || []
        const newBrandOptions = brands.map((brand: any) => ({
          value: brand._id,
          label: brand.name.en,
          slug: brand.slug
        }));
        setBrandOptions(newBrandOptions);
      }
    }).catch((error) => {
      console.error('Error fetching data: ', error)
    })
  }

  function getProducts() {
    api.post(endpoints.products).then((res) => {
      if (res?.status == 200) {
        const products = res?.data?.result?.products || []
        const newProductOptions = products.map((product: any) => ({
          value: product._id,
          label: product.name.en,
          slug: product.slug,
        }));
        setProductOptions(newProductOptions);
      }
    }).catch((error) => {
      console.error('Error fetching data: ', error)
    })
  }

  function getCollections() {
    api.get(endpoints.collections).then((res) => {
      if (res?.status == 200) {
        const collections = res?.data?.result || []
        const newCollectionOptions = collections.map((collection: any) => ({
          value: collection._id,
          label: collection.title.en,
          slug: collection.slug,
        }));
        setCollectionOptions(newCollectionOptions);
      }
    }).catch((error) => {
      console.error('Error fetching data: ', error)
    })
  }

  // const handleImageChange = (files: any) => {
  //   setImageFile(files);
  // }

  const handleImageChange = (files: any, index: any) => {
    const updatedFiles: any = [...imageFile]
    updatedFiles[index] = files[0]
    setImageFile(updatedFiles)

    const updatedArray: any = [...imageArray]
    updatedArray[index] = files
    setImageArray(updatedArray)
  }


  const handleMobileFileChange = (files: any) => {
    setImageFileMobile(files);
  }

  const {
    handleSubmit,
    control,
    setValue,
    formState: { errors },
  } = useForm<any>();

  const {
    fields,
    append,
    remove,
  } = useFieldArray({
    control,
    name: 'files',
  })

  useEffect(() => {
    if (params.id) {
      api.get(endpoints.bannerDetail + params.id).then((res) => {
        if (res.status == 200) {

        }
      }).catch((err) => {
        console.log(err);
      })
    }
    else {
      append({})
    }
    getCategories();
    getBrands();
    getProducts();
    getCollections();
  }, [params.id])


  const onSubmit = (data: any) => {
    console.log(data);
    const formData = new FormData();
    const values = {
      title: data.title,
      type: '',
      files: data.files.map((file: any) => {
        return {
          title: file.title,
          description: file.description,
          buttonText: file.buttonText,
          redirectionType: file.redirectionType.value,
          redirection: file.redirection,
        }
      }),
    }
    formData.append('data', JSON.stringify(values));
    if (imageFile.length > 0) {
      for (let i = 0; i < imageFile.length; i++) {
        formData.append('image', imageFile[i]);
      }
    }

    if (params.id) {
      api.post(endpoints.updateBanner + params.id, formData).then((res) => {
        if (res.status == 200) {
          if (res?.data?.errorCode == 0) {
            toast.push(
              <Notification type="success" title={res.data.message} />, {
              placement: 'top-center',
            })
            navigate('/banners');
          }
        } else {
          toast.push(
            <Notification type="warning" title={res.data.message} />, {
            placement: 'top-center',
          })
        }
      })
    }
    else {
      api.post(endpoints.addBanner, formData).then((res) => {
        if (res.status == 200) {
          toast.push(
            <Notification type="success" title={res.data.message} />, {
            placement: 'top-center',
          })
          navigate('/banners');
        }
      }).catch((err) => {
        console.log(err);
      })
    };
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <h3 className="mb-2">{params.id ? 'Edit' : 'Add '} Banner</h3>

      <div className="grid grid-cols-2 gap-4 mt-4">
        <FormItem label="Title">
          <Controller
            name="title"
            control={control}
            defaultValue=""
            rules={{ required: 'Title is required' }}
            render={({ field }) => (
              <input
                type="text"
                className={`${errors.title ? ' input input-md h-11 input-invalid' : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'}`}
                {...field}
              />
            )}
          />
          {errors.title && <small className="text-red-600 py-3">{errors.title.message as string}</small>}
        </FormItem>
      </div>

      <ul className="mt-4">
        {fields.map((item, index) => {
          return (
            <li key={item.id}>
              <div className="grid grid-cols-2 gap-4">
                <FormItem label="Web Banner">
                  <Upload
                    draggable
                    uploadLimit={1}
                    fileList={imageArray[index]}
                    onChange={(files) =>
                      handleImageChange(files, index)
                    }
                  />
                </FormItem>

                {/* <FormItem label="Mobile Banner">
                  <Upload draggable uploadLimit={1} fileList={imageFile} onChange={handleImageChange} />
                </FormItem> */}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <FormItem label="Title">
                  <Controller
                    name={`files.${index}.title`}
                    control={control}
                    defaultValue=""
                    render={({ field }) => (
                      <Input
                        defaultValue={''}
                        type="text"
                        {...field}
                      />
                    )}
                  />
                </FormItem>

                <FormItem label="Description">
                  <Controller
                    name={`files.${index}.description`}
                    control={control}
                    defaultValue=""
                    render={({ field }) => (
                      <Input
                        defaultValue={''}
                        type="text"
                        {...field}
                      />
                    )}
                  />
                </FormItem>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <FormItem label="Button Text">
                  <Controller
                    name={`files.${index}.buttonText`}
                    control={control}
                    defaultValue=""
                    render={({ field }) => (
                      <Input
                        defaultValue={''}
                        type="text"
                        {...field}
                      />
                    )}
                  />
                </FormItem>

                <FormItem label="Redirection Type">
                  <Controller
                    name={`files.${index}.redirectionType`}
                    control={control}
                    defaultValue=""
                    render={({ field }) => (
                      <Select
                        defaultValue=""
                        placeholder='Select Type'
                        isSearchable={false}
                        options={types}
                        {...field}
                        onChange={(selectedOption) => {
                          field.onChange(selectedOption);
                          setSelectedRedirectionType(selectedOption.label);
                          switch (selectedOption.value) {
                            case 'category':
                              setTypeOptions(categoryOptions);
                              break;
                            case 'product':
                              setTypeOptions(productOptions);
                              break;
                            case 'brand':
                              setTypeOptions(brandOptions);
                              break;
                            case 'collections':
                              setTypeOptions(collectionOptions);
                              break;
                            case 'page':
                              setTypeOptions(pageOptions);
                              break;
                          }
                        }}
                      />
                    )}
                  />
                </FormItem>
              </div>

              {selectedRedirectionType &&
                <div className="grid grid-cols-2 gap-4">
                  <FormItem label={`Select ${selectedRedirectionType} `}>
                    <Controller
                      name={`files.${index}.value`}
                      control={control}
                      defaultValue=""
                      render={({ field }) => (
                        <Select
                          defaultValue=""
                          placeholder={`Select ${selectedRedirectionType}`}
                          isSearchable={false}
                          options={typeOptions}
                          {...field}
                          onChange={(selectedOption) => {
                            field.onChange(selectedOption);
                            setValue(`files.${index}.redirection`, selectedOption.slug);
                          }}
                        />
                      )}
                    />
                  </FormItem>

                  <FormItem label="Redirection">
                    <Controller
                      disabled={true}
                      defaultValue={''}
                      render={({ field }) => (
                        <Input type="text" {...field} />
                      )}
                      name={`files.${index}.redirection`}
                      control={control}
                    />
                  </FormItem>
                </div>
              }

              <Button
                className="mb-4 "
                variant="twoTone"
                type="button"
                icon={<AiOutlineMinus />}
                onClick={() => remove(index)}>
                Remove
              </Button>
            </li>
          );
        })}
      </ul>

      <Button variant="solid" type="button" icon={<AiOutlinePlus />} onClick={() => {
        append({});
      }}>
        Add More
      </Button>


      <Button className='float-right mt-4' variant="solid" type="submit" icon={<AiOutlineSave />}>
        Save
      </Button>

    </form>
  )
}
