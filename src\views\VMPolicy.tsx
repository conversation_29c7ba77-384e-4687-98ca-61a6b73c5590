/* eslint-disable */
import { Button, FormItem, Tag, toast } from '@/components/ui'
import endpoints from '@/endpoints'
import api from '@/services/api.interceptor'
import { Controller, SubmitHandler, useForm } from 'react-hook-form'
import { AiOutlineSave } from 'react-icons/ai'
import Notification from '@/components/ui/Notification'
import Input from '@/components/ui/Input'
import { useEffect, useState } from 'react'
import Breadcrumb from './modals/BreadCrumb'
import { RichTextEditor } from '@/components/shared'

export default function VMPolicy() {
    const [isUpdate, setIsUpdate] = useState(false)
    const [formSubmitted, setFormSubmitted] = useState(false)

    const breadcrumbItems = [{ title: 'VM Policy', url: '' }]

    useEffect(() => {
        api.get(endpoints.VMPolicy).then((res) => {
            if (res?.status == 200) {
                if (res?.data?.result?.length > 0) {
                    const data = res?.data?.result[0]
                    setValue('title', data?.title?.en)
                    setValue('titleAr', data?.title?.ar)
                    setValue('content', data?.content?.en)
                    setValue('contentAr', data?.content?.ar)
                    setIsUpdate(true)
                }
            }
        })
    }, [formSubmitted])

    const {
        handleSubmit,
        control,
        setValue,
        formState: { errors },
    } = useForm<any>()

    const onSubmit: SubmitHandler<any> = (data) => {
        const values = {
            title: { en: data.title, ar: data?.titleAr },
            content: { en: data.content, ar: data?.contentAr },
        }
        if (isUpdate) {
            api.put(endpoints.VMPolicy, values)
                .then((res) => {
                    if (res.status == 200) {
                        toast.push(
                            <Notification
                                type="success"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                        setFormSubmitted(true)
                    }
                })
                .catch((err) => {
                    console.log(err)
                })
        } else {
            api.post(endpoints.VMPolicy, values)
                .then((res) => {
                    if (res.status == 200) {
                        toast.push(
                            <Notification
                                type="success"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                        setFormSubmitted(true)
                    }
                })
                .catch((err) => {
                    console.log(err)
                })
        }
    }

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <h3 className="mb-2">VM Policy</h3>
            <Breadcrumb items={breadcrumbItems} />

            <div className="grid grid-cols-2 gap-4 mt-4">
                <FormItem label="Title">
                    <Controller
                        control={control}
                        name="title"
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {errors.title && (
                        <small className="text-red-600 py-3">
                            {' '}
                            {errors.title.message as string}{' '}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Title Arabic">
                    <Controller
                        control={control}
                        name="titleAr"
                        // rules={{ required: 'Field Required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {errors.titleAr && (
                        <small className="text-red-600 py-3">
                            {' '}
                            {errors.titleAr.message as string}{' '}
                        </small>
                    )}
                </FormItem>
            </div>

            <div>
                <FormItem label="Content">
                    <Controller
                        name="content"
                        control={control}
                        defaultValue=""
                        rules={{
                            required: 'Field is Required',
                            // minLength: {
                            //     value: 12,
                            //     message: 'Field is Required',
                            // },
                        }}
                        render={({ field }) => <RichTextEditor {...field} />}
                    />
                    {errors.content && (
                        <small className="text-red-600 py-3">
                            {errors.content.message as string}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Content Arabic">
                    <Controller
                        name="contentAr"
                        control={control}
                        defaultValue=""
                        // rules={{
                        //     required: 'Field is Required',
                        //     minLength: {
                        //         value: 12,
                        //         message: 'Field is Required',
                        //     },
                        // }}
                        render={({ field }) => <RichTextEditor {...field} />}
                    />
                    {errors.contentAr && (
                        <small className="text-red-600 py-3">
                            {errors.contentAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>
            <Button
                className="float-right mt-4"
                variant="solid"
                type="submit"
                icon={<AiOutlineSave />}
            >
                Save
            </Button>
        </form>
    )
}
