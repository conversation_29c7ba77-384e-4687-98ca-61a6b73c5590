import { HiOutlineHome, HiOutlineBookOpen, HiOutlineUsers, HiOutlineUserGroup } from 'react-icons/hi'
import { BiStore } from 'react-icons/bi'
import { TfiAnnouncement } from "react-icons/tfi";
import { GiPencilBrush } from "react-icons/gi";
import { BsBoxSeam, BsPatchQuestion, BsShieldCheck } from "react-icons/bs";
import { MdOutlineCamera, MdOutlineCategory } from 'react-icons/md';
import { IoSettingsOutline } from "react-icons/io5";
import { RxCardStackPlus } from "react-icons/rx";
import { CiViewList } from "react-icons/ci";
import { TbReportAnalytics } from "react-icons/tb";
import { MdAddBusiness } from "react-icons/md";

export type NavigationIcons = Record<string, JSX.Element>

const navigationIcon: NavigationIcons = {
    home: <HiOutlineHome />,
    customize: <GiPencilBrush />,
    catalog: <BsBoxSeam />,
    attribute: <MdOutlineCategory />,
    marketing: <TfiAnnouncement />,
    enquiries: <BsPatchQuestion />,
    store: <BiStore />,
    settings: <IoSettingsOutline />,
    subscription: <RxCardStackPlus />,
    insurance: <BsShieldCheck />,
    pages: <HiOutlineBookOpen />,
    admins: <HiOutlineUsers />,
    customers: <HiOutlineUserGroup />,
    lens: <MdOutlineCamera />,
    orders: <CiViewList />,
    reports : <TbReportAnalytics />,
    multiStore: <MdAddBusiness />

}

export default navigationIcon
