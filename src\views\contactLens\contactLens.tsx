/* eslint-disable */
import { useState, useMemo, useEffect, InputHTMLAttributes } from 'react'
import Table from '@/components/ui/Table'
import Pagination from '@/components/ui/Pagination'
import Select from '@/components/ui/Select'
import {
    useReactTable,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    flexRender,
    getSortedRowModel,
    getFacetedRowModel,
    getFacetedUniqueValues,
} from '@tanstack/react-table'
import type {
    ColumnDef,
    ColumnFiltersState,
    FilterFn,
} from '@tanstack/react-table'
import Input from '@/components/ui/Input'
import { rankItem } from '@tanstack/match-sorter-utils'
import Button from '@/components/ui/Button'
import { AiOutlinePlus, AiOutlineUpload } from 'react-icons/ai'
import api from '@/services/api.interceptor'
import endpoints from '@/endpoints'
import { Link } from 'react-router-dom'
import { HiPencilSquare } from 'react-icons/hi2'
import Tag from '@/components/ui/Tag'
import Breadcrumb from '@/views/modals/BreadCrumb'
import Filter from '@/components/shared/TableFilter'
import { MdDeleteOutline, MdOutlineFileDownload } from 'react-icons/md'
import { mkConfig, generateCsv, download } from 'export-to-csv'
import Dialog from '@/components/ui/Dialog'
import Bulk from '../catalog/products/Bulk'
import Notification from '@/components/ui/Notification'
import { toast } from '@/components/ui'
import DeleteModal from '../modals/DeleteModal'
import TotalProductsCard from '../catalog/products/TotalCount'

interface DebouncedInputProps
    extends Omit<
        InputHTMLAttributes<HTMLInputElement>,
        'onChange' | 'size' | 'prefix'
    > {
    value: string | number
    onChange: (value: string | number) => void
    debounce?: number
}

const { Tr, Th, Td, THead, TBody, Sorter } = Table

function DebouncedInput({
    value: initialValue,
    onChange,
    debounce = 500,
    ...props
}: DebouncedInputProps) {
    const [value, setValue] = useState(initialValue)

    useEffect(() => {
        setValue(initialValue)
    }, [initialValue])

    useEffect(() => {
        const timeout = setTimeout(() => {
            onChange(value)
        }, debounce)

        return () => clearTimeout(timeout)
    }, [value])

    return (
        <div className="flex justify-end">
            <div className="flex items-center mb-4">
                <Input
                    {...props}
                    value={value}
                    onChange={(e) => setValue(e.target.value)}
                />
            </div>
        </div>
    )
}

type Option = {
    value: number
    label: string
}

const pageSizeOption = [
    { value: 10, label: '10 / page' },
    { value: 20, label: '20 / page' },
    { value: 30, label: '30 / page' },
    { value: 40, label: '40 / page' },
    { value: 50, label: '50 / page' },
]

const breadcrumbItems = [{ title: 'Contact Lens', url: '' }]

const ContactLens = () => {
    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
    const [globalFilter, setGlobalFilter] = useState('')
    const [showImportDialog, setShowImportDialog] = useState(false)
    const [deleteModalOpen, setDeleteModalOpen] = useState(false)
    const [itemToDelete, setItemToDelete] = useState(null)

    const openDeleteModal = (refid: any) => {
        setDeleteModalOpen(true)
        setItemToDelete(refid)
    }

    const closeDeleteModal = () => {
        setDeleteModalOpen(false)
    }

    const confirmDelete = () => {
        api.put(endpoints.deleteProduct + itemToDelete)
            .then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification
                            type="success"
                            title={res.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    closeDeleteModal()
                    getProducts()
                }
            })
            .catch((err) => {
                toast.push(
                    <Notification
                        type="warning"
                        title={err.response.data.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )
                closeDeleteModal()
            })
    }

    const columns = useMemo<ColumnDef<any>[]>(
        () => [
            {
                header: 'Sl No.',
                accessorKey: 'slNo',
                cell: (info) => info.row.index + 1,
                enableColumnFilter: false,
                enableSorting: false,
            },
            {
                header: 'SKU',
                accessorKey: 'sku',
                enableSorting: false,
            },
            {
                header: 'Name',
                accessorKey: 'name.en',
                enableSorting: false,
            },
            {
                header: 'Brand',
                accessorKey: 'brand.name.en',
                enableSorting: false,
            },
            {
                header: 'Category',
                accessorKey: 'category',
                enableSorting: false,
                cell: (info) => {
                    const categories = info.getValue() as any[]
                    const categoryNames = categories
                        .map((cat) => cat.name.en)
                        .join(', ')
                    return <div>{categoryNames}</div>
                },
                enableColumnFilter: false,
            },
            {
                header: 'Stock',
                accessorKey: 'stock',
                enableColumnFilter: false,
                enableSorting: false,
            },
            {
                header: 'Status',
                accessorKey: 'isActive',
                enableSorting: false,
                cell: (info) => {
                    const isActive = info.getValue()
                    return (
                        <div>
                            <Tag
                                className={
                                    isActive
                                        ? 'bg-emerald-100 text-emerald-600 dark:bg-emerald-500/20 dark:text-emerald-100 border-0 rounded'
                                        : 'text-red-600 bg-red-100 dark:text-red-100 dark:bg-red-500/20 border-0 rounded'
                                }
                            >
                                {isActive ? 'Active' : 'Inactive'}
                            </Tag>
                        </div>
                    )
                },
                enableColumnFilter: false,
            },
            {
                header: 'Actions',
                accessorKey: 'refid',
                enableSorting: false,
                cell: (info) => {
                    return (
                        <div className="flex items-center">
                            <Link
                                to={`/manage-contact-lenses/${info.getValue()}`}
                            >
                                <HiPencilSquare size={25} />
                            </Link>
                            <div
                                onClick={() => openDeleteModal(info.getValue())}
                            >
                                <MdDeleteOutline size={25} className="ml-4" />
                            </div>
                        </div>
                    )
                },
                enableColumnFilter: false,
            },
        ],
        []
    )

    const [data, setData] = useState<any[]>([])
    const [totalData, setTotalData] = useState(0)

    const toggleImportDialog = () => {
        setShowImportDialog((prev) => !prev)
    }

    const handleExportRows = (rows: any) => {
        const rowData = rows.map((row: any) => {
            return {
                name: row.original.name.en,
                sku: row.original.sku,
                brand: row.original.brand.name.en,
                categories: row.original.category
                    .map((item: any) => item.name.en)
                    .join(', '),
                product_type: row.original.productType,
                price: row.original?.price?.aed || null,
                offer_price: row.original?.offerPrice?.aed || null,
                age_groups: row.original.ageGroup
                    .map((item: any) => item.name)
                    .join(', '),
                colour: row?.original?.color?.name,
                stock: row.original.stock,
                is_new_arrival: row.original.isNewArrival ? 1 : 0,
                label: row.original.label?.name.en,
                is_active: row.original.isActive ? 1 : 0,
                technical_details: row.original.technicalInfo
                    ?.map((item: any) => {
                        return `title:${item.title.en},description:${item.description.en}`
                    })
                    .join(', '),
            }
        })
        const csv = generateCsv(csvConfig)(rowData)
        download(csvConfig)(csv)
    }

    const csvConfig = mkConfig({
        fieldSeparator: ',',
        decimalSeparator: '.',
        useKeysAsHeaders: true,
    })

    const fuzzyFilter: FilterFn<any> = (row, columnId, value, addMeta) => {
        const itemRank = rankItem(row.getValue(columnId), value)

        addMeta({
            itemRank,
        })

        return itemRank.passed
    }

    const table = useReactTable({
        data,
        columns,
        filterFns: {
            fuzzy: fuzzyFilter,
        },
        state: {
            columnFilters,
            globalFilter,
        },
        onColumnFiltersChange: setColumnFilters,
        onGlobalFilterChange: setGlobalFilter,
        globalFilterFn: fuzzyFilter,
        getSortedRowModel: getSortedRowModel(),
        getCoreRowModel: getCoreRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getFacetedRowModel: getFacetedRowModel(),
        getFacetedUniqueValues: getFacetedUniqueValues(),
    })

    const onPaginationChange = (page: number) => {
        table.setPageIndex(page - 1)
    }

    const onSelectChange = (value = 0) => {
        table.setPageSize(Number(value))
    }

    const getProducts = () => {
        api.post(endpoints.products, {
            isDefaultVariant: true,
            productType: 'contactLens',
        })
            .then((res) => {
                setData(res?.data?.result?.products)
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }

    useEffect(() => {
        getProducts()
    }, [])

    useEffect(() => {
        const filteredData = data.filter((item) => {
            return item.name.en
                .toLowerCase()
                .includes(globalFilter.toLowerCase())
        })
        setTotalData(filteredData.length)
    }, [data, globalFilter])

    return (
        <div>
            <div className="mb-4 flex">
                <h2>Contact Lenses</h2>

                <Link className="mr-2 mb-2 ml-auto" to="/manage-contact-lenses">
                    <Button
                        variant="twoTone"
                        color="green-600"
                        icon={<AiOutlinePlus />}
                    >
                        Add Contact Lens
                    </Button>
                </Link>
                <Button
                    variant="twoTone"
                    color="indigo-600"
                    className="mr-2 mb-2"
                    //export all data that is currently in the table (ignore pagination, sorting, filtering, etc.)
                    onClick={() =>
                        handleExportRows(table.getPrePaginationRowModel().rows)
                    }
                    icon={<MdOutlineFileDownload />}
                >
                    Export Products
                </Button>
                <Button
                    className="mr-2 mb-2"
                    onClick={toggleImportDialog}
                    variant="twoTone"
                    color="blue-600"
                    icon={<AiOutlineUpload />}
                >
                    Import Products
                </Button>
            </div>
            <Breadcrumb items={breadcrumbItems} />

            <div className="grid grid-cols-3 gap-4">
                <TotalProductsCard count={data?.length} />
            </div>

            <div className="mb-4 flex space-x-2 justify-end">
                <DebouncedInput
                    value={globalFilter ?? ''}
                    className="p-2 font-lg shadow border border-block"
                    placeholder="Search all columns..."
                    onChange={(value) => setGlobalFilter(String(value))}
                />
            </div>
            <Table>
                <THead>
                    {table.getHeaderGroups().map((headerGroup) => (
                        <Tr key={headerGroup.id}>
                            {headerGroup.headers.map((header) => {
                                return (
                                    <Th
                                        key={header.id}
                                        colSpan={header.colSpan}
                                    >
                                        {header.column.getCanSort() ? (
                                            <div
                                                className="cursor-pointer select-none"
                                                onClick={header.column.getToggleSortingHandler()}
                                            >
                                                {flexRender(
                                                    header.column.columnDef
                                                        .header,
                                                    header.getContext()
                                                )}
                                                <Sorter
                                                    sort={header.column.getIsSorted()}
                                                />
                                            </div>
                                        ) : (
                                            <div>
                                                {flexRender(
                                                    header.column.columnDef
                                                        .header,
                                                    header.getContext()
                                                )}
                                            </div>
                                        )}{' '}
                                        <div>
                                            {header.column.getCanFilter() ? (
                                                <div>
                                                    <Filter
                                                        column={header.column}
                                                        table={table}
                                                    />
                                                </div>
                                            ) : null}
                                        </div>
                                    </Th>
                                )
                            })}
                        </Tr>
                    ))}
                </THead>
                <TBody>
                    {table.getRowModel().rows.map((row) => {
                        return (
                            <Tr key={row.id}>
                                {row.getVisibleCells().map((cell) => {
                                    return (
                                        <Td key={cell.id}>
                                            {flexRender(
                                                cell.column.columnDef.cell,
                                                cell.getContext()
                                            )}
                                        </Td>
                                    )
                                })}
                            </Tr>
                        )
                    })}
                </TBody>
            </Table>
            <div className="flex items-center justify-between mt-4">
                <Pagination
                    pageSize={table.getState().pagination.pageSize}
                    currentPage={table.getState().pagination.pageIndex + 1}
                    total={totalData}
                    onChange={onPaginationChange}
                />
                <div style={{ minWidth: 130 }}>
                    <Select<Option>
                        size="sm"
                        isSearchable={false}
                        value={pageSizeOption.filter(
                            (option) =>
                                option.value ===
                                table.getState().pagination.pageSize
                        )}
                        options={pageSizeOption}
                        onChange={(option) => onSelectChange(option?.value)}
                    />
                </div>
            </div>

            <Dialog isOpen={showImportDialog} onClose={toggleImportDialog}>
                <Bulk
                    onClose={toggleImportDialog}
                    getProducts={getProducts}
                    type="Contact Lens"
                />
            </Dialog>

            <DeleteModal
                isOpen={deleteModalOpen}
                title="Delete Product"
                content="Are you sure you want to delete this product?"
                onClose={closeDeleteModal}
                onConfirm={confirmDelete}
            />
        </div>
    )
}

export default ContactLens
