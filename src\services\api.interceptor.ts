import { permissionObject } from '@/views/adminUser/permissions';
import axios from 'axios';

const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
});

api.interceptors.request.use((config) => {
  const admin = JSON.parse(localStorage.getItem('admin') || '{}');
  const token = admin && admin.auth && JSON.parse(admin.auth).session.token;
  let store = localStorage.getItem("store") ?? "ae"
  config.headers.storeid = store;
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
    // config.headers["Permission"] = permissionObject[window.location.pathname?.split("/")?.[2]? window.location.pathname?.split("/")?.[2] : window.location.pathname?.split("/")?.[1] || 'home']
    // const pathParts = window.location.pathname?.split("/");
    // console.log(pathParts, 'pathParts')
    // const secondPart = pathParts?.[2];
    // console.log(secondPart, 'secondPart')
    // const permissionKey = secondPart && !isNaN(Number(secondPart)) ? pathParts?.[1] || 'home' : secondPart || pathParts?.[1] || 'home';
    // console.log(permissionObject[permissionKey], 'permissionKey')
    // config.headers["Permission"] = permissionObject[permissionKey];
    config.headers["Permission"] = "home"
  }
  return config;
}, (error) => {
  return Promise.reject(error);
});

api.interceptors.response.use((response) => {
  return response;
}, (error) => {
  // if(error.response.status === 403) {
  //   window.location.href = '/access-denied';
  // }
  return Promise.reject(error);
});


export default api;