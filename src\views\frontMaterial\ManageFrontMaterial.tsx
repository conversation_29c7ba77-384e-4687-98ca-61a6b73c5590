import { Button, FormItem, Input, Select, toast } from "@/components/ui";
import endpoints from "@/endpoints";
import api from "@/services/api.interceptor";
import { Controller, useForm } from "react-hook-form";
import { AiOutlineSave } from "react-icons/ai";
import Notification from '@/components/ui/Notification'
import { useNavigate, useParams } from "react-router-dom";
import { useEffect } from "react";
import Breadcrumb from "@/views/modals/BreadCrumb";

/* eslint-disable */
export default function ManageFrontMaterial() {
    const navigate = useNavigate()
    const params = useParams()

    const breadcrumbItems = [
        { title: 'Front Material', url: '/front-material' },
        { title: 'Manage Front Material', url: '' },
    ];

    const options: any = [
        { value: 'true', label: 'True' },
        { value: 'false', label: 'False' },
    ]

    const {
        handleSubmit,
        control,
        setValue,
        formState: { errors },
    } = useForm<any>();

    useEffect(() => {
        if (params.id) {
            api.get(endpoints.frontMaterials + params.id).then((res) => {
                if (res.status == 200) {
                    setValue('name', res?.data?.result?.name?.en)
                    setValue('nameAr', res?.data?.result?.name?.ar)
                    setValue('position', res?.data?.result?.position)
                    setValue('isActive', { value: res.data.result.isActive, label: res.data.result.isActive ? 'True' : 'False' })
                }
            }).catch((err) => {
                navigate('/access-denied')
                console.log(err);
            })
        }
    }, [params.id])


    const onSubmit = (data: any) => {
        const values: any = {
            name: {en: data?.name, ar: data?.nameAr},
            isActive: data?.isActive?.value,
            position: parseInt(data?.position),
        }

        if (params.id) {
            values.refid = params.id

            api.put(endpoints.frontMaterials + params.id, values).then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification type="success" title={res.data.message} />, {
                        placement: 'top-center',
                    })
                    navigate('/front-material');
                }
            }).catch((err) => {
                toast.push(
                    <Notification type="warning" title={err.response.data.message} />, {
                    placement: 'top-center',
                })
            })
        }
        else {
            api.post(endpoints.frontMaterials, values).then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification type="success" title={res.data.message} />, {
                        placement: 'top-center',
                    })
                    navigate('/front-material');
                }
            }).catch((err) => {
                toast.push(
                    <Notification type="warning" title={err.response.data.message} />, {
                    placement: 'top-center',
                })
            })
        };
    }

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <h3 className="mb-2">
                {params.id ? 'Edit' : 'Add '} Front Material
            </h3>
            <Breadcrumb items={breadcrumbItems} />
            <div className="grid grid-cols-2 gap-4 mt-4">
                <FormItem label="Name">
                    <Controller
                        name="name"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Name is required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {errors.name && (
                        <small className="text-red-600 py-3">
                            {errors.name.message as string}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Name Arabic">
                    <Controller
                        name="nameAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Name is required' }}
                        render={({ field }) => <Input dir="rtl" type="text" {...field} />}
                    />
                    {errors.nameAr && (
                        <small className="text-red-600 py-3">
                            {errors.nameAr.message as string}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Position">
                    <Controller
                        name="position"
                        control={control}
                        defaultValue="0"
                        rules={{ required: 'Name is required' }}
                        render={({ field }) => <Input type="number" {...field} />}
                    />
                    {errors.position && (
                        <small className="text-red-600 py-3">
                            {errors.position.message as string}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Is Active?">
                    <Controller
                        name="isActive"
                        control={control}
                        defaultValue={options[0]}
                        render={({ field }) => (
                            <Select options={options} {...field} />
                        )}
                    />
                </FormItem>
            </div>

            <Button
                className="float-right mt-4"
                variant="solid"
                type="submit"
                icon={<AiOutlineSave />}
            >
                Save
            </Button>
        </form>
    )
}
