import { FormItem, Input } from '@/components/ui'
import React from 'react'
import { Controller } from 'react-hook-form'

const search: { label: string, value: string, db: string }[] = [
    {
        label: "Search",
        value: "Search",
        db: "search"
    },
    {
        label: "Result",
        value: "Result",
        db: "result"
    },
    {
        label: "No Result",
        value: "NoResult",
        db: "noResult"
    },
    {
        label: "Popular",
        value: "Popular",
        db: "popular"
    },
    {
        label: "Top Category",
        value: "TopCategory",
        db: "topCategory"
    },

]


export const searchPage: { label: string, value: string, db: string }[] = [
    ...search
]

function SearchTab({ control, errors }: any) {
    return (
        <>
            <h3>Search</h3>
            {search.map((item) => (
                <Forms key={item.value} control={control} errors={errors} item={item} />
            ))}
        </>
    )
}

function Forms({ item, control, errors }: any) {
    return (
        <div className="mt-2">
            <div className="grid grid-cols-2 gap-4">
                <FormItem label={`${item.label} English`}>
                    <Controller
                        control={control}
                        name={`search${item.value}En`}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {errors[`search${item.value}En`] && (
                        <small className="text-red-600 py-3">
                            {' '}
                            {errors[`search${item.value}En`].message as string}{' '}
                        </small>
                    )}
                </FormItem>
                <FormItem label={`${item.label} Arabic`}>
                    <Controller
                        control={control}
                        name={`search${item.value}Ar`}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => <Input dir='rtl' type="text" {...field} />}
                    />
                    {errors[`search${item.value}Ar`] && (
                        <small className="text-red-600 py-3">
                            {' '}
                            {errors[`search${item.value}Ar`].message as string}{' '}
                        </small>
                    )}
                </FormItem>
            </div>
        </div>
    )
}

export default SearchTab