/* eslint-disable */
import { Button, FormItem, Input, toast } from "@/components/ui";
import endpoints from "@/endpoints";
import api from "@/services/api.interceptor";
import { useEffect, useState } from "react";
import { Controller, useFieldArray, useForm } from "react-hook-form";
import { IoIosCloseCircleOutline } from "react-icons/io";
import Notification from '@/components/ui/Notification'
import { useNavigate } from "react-router-dom";
import Breadcrumb from "../modals/BreadCrumb";

export default function ImageMap() {
    const baseUrl = import.meta.env.VITE_ASSET_URL
    const [mapMainImage, setMapMainImage] = useState<string | null>(null);
    const [clickedPositions, setClickedPositions] = useState<Array<{ x: number; y: number }>>([]);
    const [currentPosition, setCurrentPosition] = useState<{ x: number; y: number } | null>(null);
    const [imagePreviews, setImagePreviews] = useState<string[]>([]);
    const [isUpdate, setIsUpdate] = useState(false);
    const navigate = useNavigate()

    const breadcrumbItems = [
        { title: 'Home Page Customization', url: '/dashboard-rearrange' },
        { title: 'Image Map', url: '' },
    ];

    const {
        handleSubmit,
        control,
        setValue,
        formState: { errors },
    } = useForm<any>()

    const {
        fields,
        append,
        remove,
    } = useFieldArray({
        control,
        name: 'fields',
    })

    const handleMainImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const newMainImage = e.target.files?.[0];

        if (newMainImage) {
            const formData = new FormData();
            formData.append('video', newMainImage);

            api.post(endpoints.videoUpload, formData).then((res) => {
                console.log(res?.data)
                setMapMainImage(res?.data?.videoUrl);
            }).catch((error) => {
                console.error('Error fetching data: ', error)
            })

        }
    };

    const handleImages = async (e: any, index: number) => {
        const image = e.target.files?.[0];
        if (image) {
            const formData = new FormData();
            formData.append('video', image);

            try {
                const res = await api.post(endpoints.videoUpload, formData);
                const imageUrl = res?.data?.videoUrl;
                setValue(`fields.${index}.imageUrl`, imageUrl);
                setImagePreviews((prevPreviews) => {
                    const newPreviews = [...prevPreviews];
                    newPreviews[index] = imageUrl;
                    return newPreviews;
                });
            } catch (error) {
                console.error('Error fetching data: ', error);
            }
        }
    };

    const handleImageClick = (e: any) => {
        const imageElement = e.currentTarget;
        const imageRect = imageElement.getBoundingClientRect();

        const clickX = (e.nativeEvent.offsetX / imageRect.width) * 100;
        const clickY = (e.nativeEvent.offsetY / imageRect.height) * 100;

        if (clickedPositions.length < 3) {
            setClickedPositions([...clickedPositions, { x: clickX, y: clickY }]);
            setCurrentPosition({ x: clickX, y: clickY });
            append({})
        }
    };

    const getImageMap = () => {
        api.get(endpoints.imageMap).then((res) => {
            if (res.status == 200) {
                const data = res?.data?.result
                setIsUpdate(true)
                                setMapMainImage(data?.image)
                setValue('fields', data?.items.map((item: any) => ({
                    positionX: item.positionX,
                    positionY: item.positionY,
                    imageUrl: item.image,
                    link: item.link,
                    titleEn: item.title?.en,
                    titleAr: item.title?.ar,

                    buttonTextEn: item.buttonText?.en,
                    buttonTextAr: item.buttonText?.ar,
                })))
                setClickedPositions(data?.items.map((item: any) => ({
                    x: item.positionX,
                    y: item.positionY,
                })))
                setCurrentPosition(data?.items.map((item: any) => ({
                    x: item.positionX,
                    y: item.positionY,
                })))
                setImagePreviews(data?.items.map((item: any) => item.image))

            }
        })
    }

    useEffect(() => {
        getImageMap()

    }, [])


    const onSubmit = (data: any) => {
        console.log(data)
        const payload = {
            image: mapMainImage,
            items: data.fields.map((item: any) => ({
                positionX: item.positionX,
                positionY: item.positionY,
                image: item.imageUrl,
                link: item.link,
                title: {
                    en: item.titleEn,
                    ar: item.titleAr,
                },
                buttonText: {
                    en: item.buttonTextEn,
                    ar: item.buttonTextAr,
                }
            }))
        }

        if (isUpdate) {
            api.put(endpoints.updateImageMap, payload).then((res) => {
                if (res?.status == 200) {
                    toast.push(
                        <Notification type="success" title={res.data.message} />, {
                        placement: 'top-center',
                    })
                    navigate('/dashboard-rearrange')
                }
            }).catch((err) => {
                toast.push(
                    <Notification type="warning" title={err.response.data.message} />, {
                    placement: 'top-center',
                })
            })
        } else {
            api.post(endpoints.imageMap, payload).then((res) => {
                if (res?.status == 200) {
                    toast.push(
                        <Notification type="success" title={res.data.message} />, {
                        placement: 'top-center',
                    })
                    navigate('/dashboard-rearrange')
                }
            }).catch((err) => {
                toast.push(
                    <Notification type="warning" title={err.response.data.message} />, {
                    placement: 'top-center',
                })
            })
        }
    }

    return (
        <div>
            <h2 className="my-4">Image Map</h2>
            <Breadcrumb items={breadcrumbItems} />
            <label htmlFor="mainImageInput">Change Main Image(Preferred size :2732*1338px):</label>
            <input
                type="file"
                id="mainImageInput"
                onChange={(e) => handleMainImageChange(e)}
            />

            {mapMainImage && (
                <div className="mt-4 flex flex-col items-center">
                    <p>Preview:</p>
                    <div className='relative w-[950px] h-auto' onClick={(e) => handleImageClick(e)}>
                        <img
                            className="mt-2 w-full mx-auto object-cover object-top "
                            src={baseUrl + mapMainImage}
                            alt="Main Image Preview"
                        />

                        {clickedPositions.map((position, index) => (
                            <div
                                key={index}
                                style={{
                                    position: 'absolute',
                                    left: `${position.x}%`,
                                    top: `${position.y}%`,
                                    width: '10px',
                                    height: '10px',
                                    backgroundColor: 'red',
                                    borderRadius: '50%',
                                }}
                            />
                        ))}
                    </div>
                </div>
            )}

            {currentPosition && (
                <form onSubmit={handleSubmit(onSubmit)}>
                    <ul className="mt-6">
                        {fields.map((item, index) => {
                            return (
                                <li key={item.id} className="border-2 rounded-md border-gray-400 mt-2 py-6 px-3 relative">
                                    <div className="grid grid-cols-5 gap-4">
                                        <FormItem label="Position X">
                                            <Controller
                                                name={`fields.${index}.positionX`}
                                                control={control}
                                                defaultValue={currentPosition.x}
                                                rules={{ required: 'Field is required' }}
                                                render={({ field }) => (
                                                    <Input
                                                        type="number"
                                                        {...field}
                                                        onChange={(e) => {
                                                            field.onChange(e);
                                                            const newX = parseFloat(e.target.value);
                                                            setClickedPositions((positions) =>
                                                                positions.map((pos, idx) =>
                                                                    idx === index ? { ...pos, x: newX } : pos
                                                                )
                                                            );
                                                        }}
                                                    />
                                                )}
                                            />
                                            {errors.fields && (errors.fields as any)[index]?.positionX && <small className="text-red-600 py-3">{(errors.fields as any)[index]?.positionX?.message as string}</small>}
                                        </FormItem>

                                        <FormItem label="Position Y">
                                            <Controller
                                                name={`fields.${index}.positionY`}
                                                control={control}
                                                defaultValue={currentPosition.y}
                                                rules={{ required: 'Field is required' }}
                                                render={({ field }) => (
                                                    <Input
                                                        type="number"
                                                        {...field}
                                                        onChange={(e) => {
                                                            field.onChange(e);
                                                            const newY = parseFloat(e.target.value);
                                                            setClickedPositions((positions) =>
                                                                positions.map((pos, idx) =>
                                                                    idx === index ? { ...pos, y: newY } : pos
                                                                )
                                                            );
                                                        }}
                                                    />
                                                )}
                                            />
                                            {errors.fields && (errors.fields as any)[index]?.positionY && <small className="text-red-600 py-3">{(errors.fields as any)[index]?.positionY?.message as string}</small>}
                                        </FormItem>

                                        <FormItem label="Title English">
                                            <Controller
                                                name={`fields.${index}.titleEn`}
                                                control={control}
                                                defaultValue=""
                                                rules={{ required: 'Field is required' }}
                                                render={({ field }) => (
                                                    <Input
                                                        type="text"
                                                        {...field}
                                                    />
                                                )}
                                            />
                                            {errors.fields && (errors.fields as any)[index]?.titleEn && <small className="text-red-600 py-3">{(errors.fields as any)[index]?.titleEn.message as string}</small>}
                                        </FormItem>

                                        <FormItem label="Title Arabic">
                                            <Controller
                                                name={`fields.${index}.titleAr`}
                                                control={control}
                                                defaultValue=""
                                                rules={{ required: 'Field is required' }}
                                                render={({ field }) => (
                                                    <Input
                                                        dir="rtl"
                                                        type="text"
                                                        {...field}
                                                    />
                                                )}
                                            />
                                            {errors.fields && (errors.fields as any)[index]?.titleAr && <small className="text-red-600 py-3">{(errors.fields as any)[index]?.titleAr.message as string}</small>}
                                        </FormItem>

                                        <FormItem label="Button Text English">
                                            <Controller
                                                name={`fields.${index}.buttonTextEn`}
                                                control={control}
                                                defaultValue=""
                                                rules={{ required: 'Field is required' }}
                                                render={({ field }) => (
                                                    <Input
                                                        type="text"
                                                        {...field}
                                                    />
                                                )}
                                            />
                                            {errors.fields && (errors.fields as any)[index]?.buttonTextEn && <small className="text-red-600 py-3">{(errors.fields as any)[index]?.buttonTextEn.message as string}</small>}
                                        </FormItem>
                                        <FormItem label="Button Text Arabic">
                                            <Controller
                                                name={`fields.${index}.buttonTextAr`}
                                                control={control}
                                                defaultValue=""
                                                rules={{ required: 'Field is required' }}
                                                render={({ field }) => (
                                                    <Input
                                                    dir="rtl"
                                                        type="text"
                                                        {...field}
                                                    />
                                                )}
                                            />
                                            {errors.fields && (errors.fields as any)[index]?.buttonTextAr && <small className="text-red-600 py-3">{(errors.fields as any)[index]?.buttonTextAr.message as string}</small>}
                                        </FormItem>

                                        <FormItem label="Link">
                                            <Controller
                                                name={`fields.${index}.link`}
                                                control={control}
                                                defaultValue=""
                                                rules={{ required: 'Field is required' }}
                                                render={({ field }) => (
                                                    <Input
                                                        type="text"
                                                        {...field}
                                                    />
                                                )}
                                            />
                                            {errors.fields && (errors.fields as any)[index]?.link && <small className="text-red-600 py-3">{(errors.fields as any)[index]?.link.message as string}</small>}
                                        </FormItem>
                                    </div>

                                    <div>
                                        <FormItem label="Image(Preferred size: 492x550px)">
                                            <Controller
                                                name={`fields.${index}.image`}
                                                control={control}
                                                defaultValue=""
                                                render={({ field }) => (
                                                    <div>
                                                        <Input
                                                            type="file"
                                                            {...field}
                                                            onChange={(e) => {
                                                                field.onChange(e);
                                                                handleImages(e, index);
                                                            }}
                                                        />
                                                        {imagePreviews[index] && (
                                                            <img
                                                                className="mt-2 w-20 mx-auto object-cover object-top"
                                                                src={baseUrl + imagePreviews[index]}
                                                                alt={`Image Preview ${index}`}
                                                            />
                                                        )}
                                                    </div>
                                                )}
                                            />
                                        </FormItem>

                                    </div>

                                    <div className="absolute right-0 top-0">
                                        <IoIosCloseCircleOutline
                                            size={30}
                                            color="red"
                                            onClick={() => {
                                                remove(index);
                                                setClickedPositions((positions) =>
                                                    positions.filter((_, idx) => idx !== index)
                                                );
                                            }}
                                        />
                                    </div>
                                </li>
                            );
                        })}
                    </ul>

                    <Button variant="solid" className="mt-4 float-right">
                        Save
                    </Button>
                </form>
            )}
        </div>
    )
}
