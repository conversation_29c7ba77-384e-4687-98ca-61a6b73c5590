import { useState, useMemo, useEffect, InputHTMLAttributes } from 'react'
import Table from '@/components/ui/Table'
import Pagination from '@/components/ui/Pagination'
import Select from '@/components/ui/Select'
import { useReactTable, getCoreRowModel, getFilteredRowModel, getPaginationRowModel, flexRender, getSortedRowModel, } from '@tanstack/react-table'
import type { ColumnDef, ColumnFiltersState, FilterFn, } from '@tanstack/react-table'
import Input from '@/components/ui/Input'
import { rankItem } from '@tanstack/match-sorter-utils'
import api from '@/services/api.interceptor'
import endpoints from '@/endpoints'
import { MdOutlineFileDownload, MdOutlineRemoveRedEye } from "react-icons/md";
import Dialog from '@/components/ui/Dialog'
import type { MouseEvent } from 'react'
import Breadcrumb from '../modals/BreadCrumb'
import { Button } from '@/components/ui'

/*eslint-disable*/
interface DebouncedInputProps
    extends Omit<
        InputHTMLAttributes<HTMLInputElement>,
        'onChange' | 'size' | 'prefix'
    > {
    value: string | number
    onChange: (value: string | number) => void
    debounce?: number
}

const { Tr, Th, Td, THead, TBody, Sorter } = Table
const imageUrl = import.meta.env.VITE_ASSET_URL

function DebouncedInput({
    value: initialValue,
    onChange,
    debounce = 500,
    ...props
}: DebouncedInputProps) {
    const [value, setValue] = useState(initialValue)

    useEffect(() => {
        setValue(initialValue)
    }, [initialValue])

    useEffect(() => {
        const timeout = setTimeout(() => {
            onChange(value)
        }, debounce)

        return () => clearTimeout(timeout)
    }, [value])

    return (
        <div className="flex justify-end">
            <div className="flex items-center mb-4">
                <Input
                    {...props}
                    value={value}
                    onChange={(e) => setValue(e.target.value)}
                />
            </div>
        </div>
    )
}

type Option = {
    value: number
    label: string
}

const pageSizeOption = [
    { value: 10, label: '10 / page' },
    { value: 20, label: '20 / page' },
    { value: 30, label: '30 / page' },
    { value: 40, label: '40 / page' },
    { value: 50, label: '50 / page' },
]

const breadcrumbItems = [
    { title: 'Cart', url: '' },
];


const CartList = () => {
    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
    const [globalFilter, setGlobalFilter] = useState('')
    const [exportLoading, setExportLoading] = useState(false)
    const currency = localStorage.getItem("currency") || "AED"

    const columns = useMemo<ColumnDef<any>[]>(
        () => [
            {
                header: 'Sl No.',
                accessorKey: 'slNo',
                cell: (info) => info.row.index + 1,
                enableSorting: false,
            },
            {
                header: 'Customer',
                accessorKey: 'customer.name',
                enableSorting: false,
                cell: (info) => info.getValue() ?? "Guest",
            },
            {
                header: 'Mobile',
                accessorKey: 'customer.mobile',
                enableSorting: false,
            },
            {
                header: 'Date',
                accessorKey: 'updatedAt',
                enableSorting: false,
                cell: (info: any) => {
                    const date = new Date(info.getValue());
                    const formattedDate = date.toLocaleDateString();
                    return <div>{formattedDate}</div>;
                }
            },
            {
                header: 'Total',
                accessorKey: 'total',
                enableSorting: false,
                cell: (info) => {
                    const totalAmount: any = info.getValue();
                    const formattedTotal = new Intl.NumberFormat('en-US', {
                        style: 'currency',
                        currency: currency,
                    }).format(totalAmount);
                    return <div>{formattedTotal}</div>;
                },
            },
            {
                header: 'Products',
                accessorKey: 'products',
                enableSorting: false,
                cell: (info) => {
                    const products: any = info.getValue();
                    const productlength = products?.length;
                    return <div>{productlength}</div>;
                }
            },
            {
                header: 'Actions',
                accessorKey: 'refid',
                enableSorting: false,
                cell: (info) => {
                    return (
                        <div onClick={() => openDialog(info.getValue())}>
                            <MdOutlineRemoveRedEye size={25} />
                        </div>
                    )
                },
            }
        ],
        []
    )

    const [data, setData] = useState<any[]>([])
    const totalData = data?.length
    const [dialogIsOpen, setIsOpen] = useState(false)
    const [productData, setProductData] = useState<any>([])
    const [total, setTotal] = useState(0)

    const openDialog = (info: any) => {
        api.get(endpoints.cartDetail + info).then((res) => {
            if (res?.status == 200) {
                console.log(res.data?.result?.products)
                setProductData(res.data?.result?.products)
                setTotal(res.data?.result?.total)
                setIsOpen(true)
            }
        })

    }

    const onDialogClose = (e: MouseEvent) => {
        console.log('onDialogClose', e)
        setIsOpen(false)
    }

    const onDialogOk = (e: MouseEvent) => {
        console.log('onDialogOk', e)
        setIsOpen(false)
    }

    const fuzzyFilter: FilterFn<any> = (row, columnId, value, addMeta) => {
        const itemRank = rankItem(row.getValue(columnId), value)

        addMeta({
            itemRank,
        })

        return itemRank.passed
    }

    const table = useReactTable({
        data,
        columns,
        filterFns: {
            fuzzy: fuzzyFilter,
        },
        state: {
            columnFilters,
            globalFilter,
        },
        onColumnFiltersChange: setColumnFilters,
        onGlobalFilterChange: setGlobalFilter,
        globalFilterFn: fuzzyFilter,
        getSortedRowModel: getSortedRowModel(),
        getCoreRowModel: getCoreRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
    })

    const onPaginationChange = (page: number) => {
        table.setPageIndex(page - 1)
    }

    const onSelectChange = (value = 0) => {
        table.setPageSize(Number(value))
    }

    useEffect(() => {
        api
            .get(endpoints.cart)
            .then((res) => {
                setData(res?.data?.result)
                console.log(res?.data?.result)
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }, [])

    const exportToCSV = () => {
        setExportLoading(true)
        api.get(endpoints.exportCart)
            .then((res) => {
                const link = document.createElement('a')
                link.href = `data:text/csv;charset=utf-8,${escape(res.data)}`
                link.setAttribute('download', "cart.csv")
                document.body.appendChild(link)
                link.click()
                link.remove()
                setExportLoading(false)
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
                setExportLoading(false)
            })
    }

    return (
        <div>
            <div className='mb-4 flex'>
                <h2>Cart</h2>
            </div>
            <Breadcrumb items={breadcrumbItems} />
            <div className="mb-4 flex space-x-2 justify-end">
                <Button
                    loading={exportLoading}
                    variant="twoTone"
                    color="indigo-600"
                    className="mr-2 mb-2"
                    onClick={exportToCSV}
                    icon={<MdOutlineFileDownload />}
                >
                    Export Carts
                </Button>
                <DebouncedInput
                    value={globalFilter ?? ''}
                    className="p-2 font-lg shadow border border-block"
                    placeholder="Search all columns..."
                    onChange={(value) => setGlobalFilter(String(value))}
                />
            </div>
            <Table>
                <THead>
                    {table.getHeaderGroups().map((headerGroup) => (
                        <Tr key={headerGroup.id}>
                            {headerGroup.headers.map((header) => {
                                return (
                                    <Th
                                        key={header.id}
                                        colSpan={header.colSpan}
                                    >
                                        {header.column.getCanSort() ? (
                                            <div
                                                className="cursor-pointer select-none"
                                                onClick={header.column.getToggleSortingHandler()}
                                            >
                                                {flexRender(
                                                    header.column.columnDef.header,
                                                    header.getContext()
                                                )}
                                                <Sorter sort={header.column.getIsSorted()} />
                                            </div>
                                        ) : (
                                            <div>
                                                {flexRender(
                                                    header.column.columnDef.header,
                                                    header.getContext()
                                                )}
                                            </div>
                                        )}
                                    </Th>
                                )
                            })}
                        </Tr>
                    ))}
                </THead>
                <TBody>
                    {table.getRowModel().rows.map((row) => {
                        return (
                            <Tr key={row.id}>
                                {row.getVisibleCells().map((cell) => {
                                    return (
                                        <Td key={cell.id}>
                                            {flexRender(
                                                cell.column.columnDef.cell,
                                                cell.getContext()
                                            )}
                                        </Td>
                                    )
                                })}
                            </Tr>
                        )
                    })}
                </TBody>
            </Table>
            <div className="flex items-center justify-between mt-4">
                <Pagination
                    pageSize={table.getState().pagination.pageSize}
                    currentPage={table.getState().pagination.pageIndex + 1}
                    total={totalData}
                    onChange={onPaginationChange}
                />
                <div style={{ minWidth: 130 }}>
                    <Select<Option>
                        size="sm"
                        isSearchable={false}
                        value={pageSizeOption.filter(
                            (option) =>
                                option.value ===
                                table.getState().pagination.pageSize
                        )}
                        options={pageSizeOption}
                        onChange={(option) => onSelectChange(option?.value)}
                    />
                </div>
            </div>

            <div>
                <Dialog
                    width={700}
                    isOpen={dialogIsOpen}
                    onClose={onDialogClose}
                    onRequestClose={onDialogClose}
                >
                    <div className="mt-8">
                        <div className="flow-root max-h-96 overflow-y-auto ">
                            <ul role="list" className="-my-6 divide-y divide-gray-200">
                                {productData?.map((product: any, index: number) => {
                                    return <li key={index} className="flex py-6 h-28">
                                        <div className="h-20 w-20 flex-shrink-0 overflow-hidden rounded-md border border-gray-200">
                                            <img src={imageUrl + product?.product?.thumbnail} />
                                        </div>

                                        <div className="ml-4 flex flex-1 flex-col">
                                            <div>
                                                <div className="flex justify-between font-medium text-gray-900">
                                                    <p className="text-gray-900 font-semibold text-md">{product?.product?.name?.en}</p>
                                                    <p className="ml-4">{`${currency} ${product?.priceTotal}`}</p>
                                                </div>
                                                <p className="text-gray-500">Qty {product?.quantity}</p>
                                                <p className="text-gray-500">Color : {product?.product?.color?.name.en}</p>
                                                <p className="text-gray-500">Size : {product?.size?.name}</p>
                                            </div>
                                        </div>
                                    </li>
                                })}
                            </ul>


                            <div className="mt-8 float-right font-bold text-lg "> Total : {currency} {total} </div>
                        </div>
                    </div>
                </Dialog>
            </div>

        </div>

    )
}

export default CartList