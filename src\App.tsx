import { Provider } from 'react-redux'
import { <PERSON>rowserRouter } from 'react-router-dom'
import { PersistGate } from 'redux-persist/integration/react'
import store, { persistor } from './store'
import Theme from '@/components/template/Theme'
import Layout from '@/components/layouts'
import mockServer from './mock'
import appConfig from '@/configs/app.config'
import './locales'
import { useEffect } from 'react'

const environment = process.env.NODE_ENV

/**
 * Set enableMock(Default false) to true at configs/app.config.js
 * If you wish to enable mock api
 */
if (environment !== 'production' && appConfig.enableMock) {
    mockServer({ environment })
}
function App() {
    useEffect(()=>{
        let currentStore = localStorage.getItem("store")
        if(!currentStore){
            localStorage.setItem("store", "sa")
        }
    }, [])

    // useEffect(()=>{
    //       const numberInputes = document.querySelectorAll('input[type=number]')
    //       numberInputes.forEach((input) => {
    //         input.addEventListener('mousewheel', function (e:any) {
    //           e.target?.blur()
    //         });
    //       });
    // }, [])

    return (
        <Provider store={store}>
            <PersistGate loading={null} persistor={persistor}>
                <BrowserRouter>
                    <Theme>
                        <Layout />
                    </Theme>
                </BrowserRouter>
            </PersistGate>
        </Provider>
    )
}

export default App
