/* eslint-disable */
import { Button, FormItem, Select, toast } from '@/components/ui'
import endpoints from '@/endpoints'
import api from '@/services/api.interceptor'
import { Controller, useForm } from 'react-hook-form'
import { AiOutlineSave } from 'react-icons/ai'
import Notification from '@/components/ui/Notification'
import { useNavigate, useParams } from 'react-router-dom'
import Input from '@/components/ui/Input'
import { useEffect, useState } from 'react'
import Breadcrumb from '../modals/BreadCrumb'

export default function ManageCoupon() {
    const navigate = useNavigate()
    const params = useParams()

    const breadcrumbItems = [
        { title: 'Coupons', url: '/coupons' },
        { title: params?.id ? 'Update Coupon' : 'Create Coupon', url: '' },
    ];

    const typeOptions = [
        { value: 'fixed', label: 'Fixed' },
        { value: 'percentage', label: 'Percentage' },
        { value: 'freeShipping', label: 'Free Shipping' },
    ]

    const applicableOptions = [
        { value: 'Products', label: 'Products' },
        { value: 'Categories', label: 'Categories' },
    ]

    const getCouponDetail = async () => {
        api.get(endpoints.couponDetail + params.id).then((res) => {
            if (res?.status == 200) {
                console.log("res :: ", res)
            }
        }).catch((error) => {
            console.error('Error fetching data: ', error)
        })
    }

    useEffect(() => {
        if (params.id) {
            getCouponDetail()
        }
    }, [])

    const {
        handleSubmit,
        control,
        setValue,
        formState: { errors },
    } = useForm<any>()

    const onSubmit = (value: any) => {
        console.log(value)
        const data = {}

        if (params?.id) {
            api.put(endpoints.updateCoupon + params.id, data).then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification type="success" title={res.data.message} />, {
                        placement: 'top-center',
                    })
                    navigate('/coupons')
                }
            }).catch((err) => {
                toast.push(
                    <Notification type="warning" title={err.response.data.message} />, {
                    placement: 'top-center',
                })
            })
        } else {
            api.post(endpoints.createCoupon, data).then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification type="success" title={res.data.message} />, {
                        placement: 'top-center',
                    })
                    navigate('/coupons')
                }
            }).catch((err) => {
                toast.push(
                    <Notification type="warning" title={err.response.data.message} />, {
                    placement: 'top-center',
                })
            })
        }
    }

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <h3 className="mb-2">{params?.id ? 'Update' : 'Create '} Coupon </h3>
            <Breadcrumb items={breadcrumbItems} />
            <div className="grid grid-cols-2 gap-4 mt-4">
                <FormItem label='Title'>
                    <Controller
                        control={control}
                        name="title"
                        defaultValue={''}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.title && (<small className="text-red-600 py-3"> {errors.title.message as string} </small>)}
                </FormItem>

                <FormItem label='Coupon Code'>
                    <Controller
                        control={control}
                        name="code"
                        defaultValue={''}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.code && (<small className="text-red-600 py-3"> {errors.code.message as string} </small>)}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label='Type'>
                    <Controller
                        control={control}
                        name="discountType"
                        defaultValue={''}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => (
                            <Select
                                {...field}
                                options={typeOptions}
                            />
                        )}
                    />
                    {errors.discountType && (<small className="text-red-600 py-3"> {errors.discountType.message as string} </small>)}
                </FormItem>

                <FormItem label='Value'>
                    <Controller
                        control={control}
                        name="discountValue"
                        defaultValue={''}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.discountValue && (<small className="text-red-600 py-3"> {errors.discountValue.message as string} </small>)}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label='From Date'>
                    <Controller
                        control={control}
                        name="validFrom"
                        defaultValue={''}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => (
                            <Input
                                type="date"
                                {...field}
                            />
                        )}
                    />
                    {errors.validFrom && (<small className="text-red-600 py-3"> {errors.validFrom.message as string} </small>)}
                </FormItem>

                <FormItem label='Last Date'>
                    <Controller
                        control={control}
                        name="validTo"
                        defaultValue={''}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => (
                            <Input
                                type="date"
                                {...field}
                            />
                        )}
                    />
                    {errors.validTo && (<small className="text-red-600 py-3"> {errors.validTo.message as string} </small>)}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label='Minimum Purchase'>
                    <Controller
                        control={control}
                        name="minPurchase"
                        defaultValue={''}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.minPurchase && (<small className="text-red-600 py-3"> {errors.minPurchase.message as string} </small>)}
                </FormItem>


            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label='Usage Per User'>
                    <Controller
                        control={control}
                        name="usagePerUser"
                        defaultValue={''}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => (
                            <Input
                                type="number"
                                {...field}
                            />
                        )}
                    />
                </FormItem>

                <FormItem label='Total Usage'>
                    <Controller
                        control={control}
                        name="totalUsage"
                        defaultValue={''}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => (
                            <Input
                                type="number"
                                {...field}
                            />
                        )}
                    />
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label='Applicable For'>
                    <Controller
                        control={control}
                        name="applicableFor"
                        defaultValue={''}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => (
                            <Select
                                {...field}
                                options={applicableOptions}
                            />
                        )}
                    />
                </FormItem>

                <FormItem label='Choose '>
                    <Controller
                        control={control}
                        name="choose"
                        defaultValue={''}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => (
                            <Select
                                {...field}
                                options={typeOptions}
                            />
                        )}
                    />
                </FormItem>
            </div>

            <Button
                className="float-right mt-4"
                variant="solid"
                type="submit"
                icon={<AiOutlineSave />}
            >
                {params?.id ? 'Update' : 'Save'}
            </Button>
        </form>
    )
}