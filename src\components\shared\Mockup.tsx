import React from 'react'

export function PhoneMockup({ children }: { children: React.ReactNode }) {
    return (
        <div className="relative mx-auto border-gray-300 dark:border-black bg-gray-300 dark:bg-black border-[14px] rounded-[2.5rem] h-[600px] w-[300px]">
            <div className="h-[32px] w-[3px] bg-gray-300 dark:bg-black absolute -start-[17px] top-[72px] rounded-s-lg"></div>
            <div className="h-[46px] w-[3px] bg-gray-300 dark:bg-black absolute -start-[17px] top-[124px] rounded-s-lg"></div>
            <div className="h-[46px] w-[3px] bg-gray-300 dark:bg-black absolute -start-[17px] top-[178px] rounded-s-lg"></div>
            <div className="h-[64px] w-[3px] bg-gray-300 dark:bg-black absolute -end-[17px] top-[142px] rounded-e-lg"></div>
            <div className="rounded-[2rem] overflow-hidden w-[272px] h-[572px] bg-white dark:bg-black">
                {children}
            </div>
        </div>
    )
}

export function LaptopMockup({ children }: { children: React.ReactNode }) {
    return (
        <div>
            <div className="relative mx-auto border-gray-300 dark:border-black bg-gray-300 border-[8px] rounded-t-xl h-[172px] max-w-[301px] md:h-[294px] md:max-w-[512px]">
                <div className="rounded-lg overflow-hidden h-[156px] md:h-[278px] bg-white dark:bg-black">
                    {children}
                </div>
            </div>
            <div className="relative mx-auto bg-gray-400 dark:bg-gray-700 rounded-b-xl rounded-t-sm h-[17px] max-w-[351px] md:h-[21px] md:max-w-[597px]">
                <div className="absolute left-1/2 top-0 -translate-x-1/2 rounded-b-xl w-[56px] h-[5px] md:w-[96px] md:h-[8px] bg-black"></div>
            </div>
        </div>
    )
}
