/* eslint-disable */
import { Button, FormItem, Notification, Tabs, Tag, toast } from '@/components/ui'
import endpoints from '@/endpoints'
import api from '@/services/api.interceptor'
import { useForm } from 'react-hook-form'
import { AiOutlineSave } from 'react-icons/ai'

import { useEffect, useState } from 'react'
import Breadcrumb from '../modals/BreadCrumb'
import TabNav from '@/components/ui/Tabs/TabNav'
import TabList from '@/components/ui/Tabs/TabList'
import TabContent from '@/components/ui/Tabs/TabContent'
import FooterTab from './FooterTab'
import FormFieldsTab, { formFields } from './FormFieldsTab'
import ProductListingTab, { productList } from './ProductListingTab'
import MyAccountTab, { myAccount } from './MyAccount'
import ProductPageTab, { productPage } from './ProductPage'
import CartTab, { cartPage } from './CartTab'
import OrderTab, { orderPage } from './OrderTab'
import PopupTab, { popups } from './PopupTab'
import LoginTab, { login } from './LoginTab'
import OtherTab, { otherPage } from './OtherTab'
import SearchTab, { searchPage } from './SearchTab'
import BreadCrumpTab, { breadCrump } from './BreadCrump'
import PolicyTab, { policy } from './Policy'
import HomeTry, { homeTry } from './HomeTry'
import Compare, { compare } from './Compare'

export default function Translation() {

    const breadcrumbItems = [{ title: 'Translation', url: '' }]

    useEffect(() => {
        api.get(endpoints.translation)
            .then((res) => {
                if (res?.status == 200 && res.data.result) {
                    const data = res?.data?.result

                    formFields.forEach(item=>{
                        setValue(`formFields${item.value}En`, data?.[0]?.formFields?.[item.db]?.en)
                        setValue(`formFields${item.value}Ar`, data?.[0]?.formFields?.[item.db]?.ar)
                    })

                    productList.forEach(item=>{
                        setValue(`productListing${item.value}En`, data?.[0]?.productListing?.[item.db]?.en)
                        setValue(`productListing${item.value}Ar`, data?.[0]?.productListing?.[item.db]?.ar)
                    })

                    productPage.forEach(item=>{
                        setValue(`productPage${item.value}En`, data?.[0]?.productPage?.[item.db]?.en)
                        setValue(`productPage${item.value}Ar`, data?.[0]?.productPage?.[item.db]?.ar)
                    })

                    myAccount.forEach(item=>{
                        setValue(`myAccount${item.value}En`, data?.[0]?.myAccount?.[item.db]?.en)
                        setValue(`myAccount${item.value}Ar`, data?.[0]?.myAccount?.[item.db]?.ar)
                    })

                    cartPage.forEach(item=>{
                        setValue(`cartPage${item.value}En`, data?.[0]?.cartPage?.[item.db]?.en)
                        setValue(`cartPage${item.value}Ar`, data?.[0]?.cartPage?.[item.db]?.ar)
                    })

                    orderPage.forEach(item=>{
                        setValue(`orderPage${item.value}En`, data?.[0]?.orderPage?.[item.db]?.en)
                        setValue(`orderPage${item.value}Ar`, data?.[0]?.orderPage?.[item.db]?.ar)
                    })

                    popups.forEach(item=>{
                        setValue(`popup${item.value}En`, data?.[0]?.popup?.[item.db]?.en)
                        setValue(`popup${item.value}Ar`, data?.[0]?.popup?.[item.db]?.ar)
                    })

                    login.forEach(item=>{
                        setValue(`login${item.value}En`, data?.[0]?.login?.[item.db]?.en)
                        setValue(`login${item.value}Ar`, data?.[0]?.login?.[item.db]?.ar)
                    })

                    searchPage.forEach(item=>{
                        setValue(`search${item.value}En`, data?.[0]?.search?.[item.db]?.en)
                        setValue(`search${item.value}Ar`, data?.[0]?.search?.[item.db]?.ar)
                    })

                    otherPage.forEach(item=>{
                        setValue(`other${item.value}En`, data?.[0]?.other?.[item.db]?.en)
                        setValue(`other${item.value}Ar`, data?.[0]?.other?.[item.db]?.ar)
                    })

                    breadCrump.forEach(item=>{
                        setValue(`breadCrump${item.value}En`, data?.[0]?.breadCrump?.[item.db]?.en)
                        setValue(`breadCrump${item.value}Ar`, data?.[0]?.breadCrump?.[item.db]?.ar)
                    })

                    compare.forEach(item=>{
                        setValue(`compare${item.value}En`, data?.[0]?.compare?.[item.db]?.en)
                        setValue(`compare${item.value}Ar`, data?.[0]?.compare?.[item.db]?.ar)
                    })

                    policy.forEach(item=>{
                        setValue(`policy${item.value}En`, data?.[0]?.policy?.[item.db]?.en)
                        setValue(`policy${item.value}Ar`, data?.[0]?.policy?.[item.db]?.ar)
                    })

                    homeTry.forEach(item=>{
                        setValue(`homeTry${item.value}En`, data?.[0]?.homeTry?.[item.db]?.en)
                        setValue(`homeTry${item.value}Ar`, data?.[0]?.homeTry?.[item.db]?.ar)
                    })

                    data?.[0]?.footer?.top?.forEach((item: any, n: number) => {
                        setValue(`footerCol${n + 1}TitleEn`, item?.title?.en)
                        setValue(`footerCol${n + 1}TitleAr`, item?.title?.ar)
                        setValue(`footerCol${n + 1}DescriptionEn`, item?.description?.en)
                        setValue(`footerCol${n + 1}DescriptionAr`, item?.description?.ar)
                    })
                    setValue('newsLetterTextEn', data?.[0]?.footer?.newsLetter?.text?.en)
                    setValue('newsLetterTextAr', data?.[0]?.footer?.newsLetter?.text?.ar)
                    setValue('newsLetterPlaceholderEn', data?.[0]?.footer?.newsLetter?.placeholder?.en)
                    setValue('newsLetterPlaceholderAr', data?.[0]?.footer?.newsLetter?.placeholder?.ar)
                    setValue('doYouNeedHelpEn', data?.[0]?.footer?.doYouNeedHelp?.en)
                    setValue('doYouNeedHelpAr', data?.[0]?.footer?.doYouNeedHelp?.ar)
                    setValue('followUsEn', data?.[0]?.footer?.followUs?.en)
                    setValue('followUsAr', data?.[0]?.footer?.followUs?.ar)
                    setValue('poweredByEn', data?.[0]?.footer?.poweredBy?.en)
                    setValue('poweredByAr', data?.[0]?.footer?.poweredBy?.ar)
                    
                }
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }, [])

    const {
        handleSubmit,
        control,
        setValue,
        formState: { errors },
    }: any = useForm<any>()

    const convertValuesReducer = (result:any, item:any)=>{
        let key = Object.keys(item)[0];
        result[key] = item[key];
        return result
    } 

    const onSubmit = (values: any) => {
        console.log(values)

        const formFieldsArray = formFields.map((item)=> ({
            [item.db]: {
                en: values[`formFields${item.value}En`],
                ar: values[`formFields${item.value}Ar`]
            }
        }))

        const productListArray = productList.map((item)=>({
            [item.db]: {
                en: values[`productListing${item.value}En`],
                ar: values[`productListing${item.value}Ar`]
            }
        }))

        const productPageArray = productPage.map((item)=>({
            [item.db]: {
                en: values[`productPage${item.value}En`],
                ar: values[`productPage${item.value}Ar`]
            }
        }))

        const myAccountArray = myAccount.map((item)=>({
            [item.db]: {
                en: values[`myAccount${item.value}En`],
                ar: values[`myAccount${item.value}Ar`],
            }
        }))

        const cartPageArray = cartPage.map((item)=>({
            [item.db]: {
                en: values[`cartPage${item.value}En`],
                ar: values[`cartPage${item.value}Ar`],
            }
        }))

        const orderPageArray = orderPage.map((item)=>({
            [item.db]: {
                en: values[`orderPage${item.value}En`],
                ar: values[`orderPage${item.value}Ar`],
            }
        }))

        const popupArray = popups.map((item)=>({
            [item.db]: {
                en: values[`popup${item.value}En`],
                ar: values[`popup${item.value}Ar`],
            }
        }))

        const loginArray = login.map((item)=>({
            [item.db]: {
                en: values[`login${item.value}En`],
                ar: values[`login${item.value}Ar`],
            }
        }))

        const otherArray = otherPage.map((item)=>({
            [item.db]: {
                en: values[`other${item.value}En`],
                ar: values[`other${item.value}Ar`],
            }
        }))

        const searchArray = searchPage.map((item)=>({
            [item.db]: {
                en: values[`search${item.value}En`],
                ar: values[`search${item.value}Ar`],
            }
        }))

        const breadCrumpArray = breadCrump.map((item)=>({
            [item.db]: {
                en: values[`breadCrump${item.value}En`],
                ar: values[`breadCrump${item.value}Ar`],
            }
        }))

        const compareArray = compare.map((item)=>({
            [item.db]: {
                en: values[`compare${item.value}En`],
                ar: values[`compare${item.value}Ar`],
            }
        }))

        const policyArray = policy.map((item)=>({
            [item.db]: {
                en: values[`policy${item.value}En`],
                ar: values[`policy${item.value}Ar`],
            }
        }));

        const homeTryArray = homeTry.map((item)=>({
            [item.db]: {
                en: values[`homeTry${item.value}En`],
                ar: values[`homeTry${item.value}Ar`],
            }
        }));



        const data = {
            footer: {
                top: [1, 2, 3, 4].map((n: number) => ({
                    title: {
                        en: values[`footerCol${n}TitleEn`],
                        ar: values[`footerCol${n}TitleAr`]
                    },
                    description: {
                        en: values[`footerCol${n}DescriptionEn`],
                        ar: values[`footerCol${n}DescriptionAr`]
                    }
                })),
                newsLetter: {
                    text: {
                        en: values.newsLetterTextEn,
                        ar: values.newsLetterTextAr,
                    },
                    placeholder: {
                        en: values.newsLetterPlaceholderEn,
                        ar: values.newsLetterPlaceholderAr,
                    }
                },
                doYouNeedHelp: {
                    en: values.doYouNeedHelpEn,
                    ar: values.doYouNeedHelpAr,
                },
                poweredBy: {
                    en: values.poweredByEn,
                    ar: values.poweredByAr,
                },
                followUs: {
                    en: values.followUsEn,
                    ar: values.followUsAr,
                }
            },
            formFields: formFieldsArray.reduce(convertValuesReducer, {}),
            productListing: productListArray.reduce(convertValuesReducer, {}),
            productPage: productPageArray.reduce(convertValuesReducer, {}),
            myAccount: myAccountArray.reduce(convertValuesReducer, {}),
            cartPage: cartPageArray.reduce(convertValuesReducer, {}),
            orderPage: orderPageArray.reduce(convertValuesReducer, {}),
            popup: popupArray.reduce(convertValuesReducer, {}),
            login: loginArray.reduce(convertValuesReducer, {}),
            other: otherArray.reduce(convertValuesReducer, {}),
            search: searchArray.reduce(convertValuesReducer, {}),
            breadCrump: breadCrumpArray.reduce(convertValuesReducer, {}),
            policy: policyArray.reduce(convertValuesReducer, {}),
            homeTry: homeTryArray.reduce(convertValuesReducer, {}),
            compare: compareArray.reduce(convertValuesReducer, {}),
        }

        api.put(endpoints.translation, data)
            .then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification
                            type="success"
                            title={res.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                }
            })
            .catch((err) => {
                toast.push(
                    <Notification
                        type="warning"
                        title={err.response.data.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )
            })
    }

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <h3 className="mb-2">Texts</h3>
            <Breadcrumb items={breadcrumbItems} />

            <Tabs defaultValue="formFields" className='relative'>
                <TabList className='sticky top-16 bg-white dark:bg-gray-800 z-20'>
                    <TabNav className='whitespace-nowrap' value="formFields">Form Fields</TabNav>
                    <TabNav className='whitespace-nowrap' value="footer">Footer</TabNav>
                    <TabNav className='whitespace-nowrap' value="productListing">Product Listing</TabNav>
                    <TabNav className='whitespace-nowrap' value="productPage">Product Page</TabNav>
                    <TabNav className='whitespace-nowrap' value="myAccount">My Account</TabNav>
                    <TabNav className='whitespace-nowrap' value="cartPage">Cart Page</TabNav>
                    <TabNav className='whitespace-nowrap' value="orderPage">Order Page</TabNav>
                    <TabNav className='whitespace-nowrap' value="popups">Popups</TabNav>
                    <TabNav className='whitespace-nowrap' value="login">Login</TabNav>
                    <TabNav className='whitespace-nowrap' value="search">Search</TabNav>
                    <TabNav className='whitespace-nowrap' value="breadCrump">Bread Crump</TabNav>
                    <TabNav className='whitespace-nowrap' value="policy">Policy</TabNav>
                    <TabNav className='whitespace-nowrap' value="homeTry">Home Try</TabNav>
                    <TabNav className='whitespace-nowrap' value="compare">Compare</TabNav>
                    <TabNav className='whitespace-nowrap' value="other">Other</TabNav>
                </TabList>
                <div className="pt-4">
                    <TabContent value='formFields'>
                        <FormFieldsTab control={control} errors={errors} />
                    </TabContent>
                    <TabContent value='footer'>
                        <FooterTab control={control} errors={errors} />
                    </TabContent>
                    <TabContent value='productListing'>
                        <ProductListingTab control={control} errors={errors} />
                    </TabContent>
                    <TabContent value='productPage'>
                        <ProductPageTab control={control} errors={errors} />
                    </TabContent>
                    <TabContent value='myAccount'>
                        <MyAccountTab control={control} errors={errors} />
                    </TabContent>
                    <TabContent value='cartPage'>
                        <CartTab control={control} errors={errors} />
                    </TabContent>
                    <TabContent value='orderPage'>
                        <OrderTab control={control} errors={errors} />
                    </TabContent>
                    <TabContent value='popups'>
                        <PopupTab control={control} errors={errors} />
                    </TabContent>
                    <TabContent value='login'>
                        <LoginTab control={control} errors={errors} />
                    </TabContent>
                    <TabContent value='search'>
                        <SearchTab control={control} errors={errors} />
                    </TabContent>
                    <TabContent value='breadCrump'>
                        <BreadCrumpTab control={control} errors={errors} />
                    </TabContent>
                    <TabContent value='policy'>
                        <PolicyTab control={control} errors={errors} />
                    </TabContent>
                    <TabContent value='homeTry'>
                        <HomeTry control={control} errors={errors} />
                    </TabContent>
                    <TabContent value='compare'>
                        <Compare control={control} errors={errors} />
                    </TabContent>
                    <TabContent value='other'>
                        <OtherTab control={control} errors={errors} />
                    </TabContent>
                </div>
            </Tabs>

            <Button
                className="float-right mt-4"
                variant="solid"
                type="submit"
                icon={<AiOutlineSave />}
            >
                Save
            </Button>
        </form>
    )
}
