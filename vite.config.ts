import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";
import dynamicImport from 'vite-plugin-dynamic-import'

// https://vitejs.dev/config/
export default defineConfig((command) => {
  if(command.command==="serve")
  return {
    
      plugins: [react({
        babel: {
          plugins: [
            'babel-plugin-macros'
          ]
        }
      }),
      
      dynamicImport()],
      assetsInclude: ['**/*.md'],
      resolve: {
        alias: {
          '@': path.join(__dirname, 'src'),
        },
      },
      build: {
        outDir: 'build'
      }
    
  }
  else return {
    plugins: [react({
      babel: {
        plugins: [
          'babel-plugin-macros'
        ]
      }
    }),
    
    dynamicImport()],
    assetsInclude: ['**/*.md'],
    resolve: {
      alias: {
        '@': path.join(__dirname, 'src'),
      },
    },
    esbuild: {
      drop: ['console', 'debugger'],
    },
    build: {
      outDir: 'build'
    }
  }
});
