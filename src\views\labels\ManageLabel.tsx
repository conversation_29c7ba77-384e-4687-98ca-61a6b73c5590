/* eslint-disable */
import { Button, FormItem, Select, toast } from '@/components/ui'
import endpoints from '@/endpoints'
import api from '@/services/api.interceptor'
import { Controller, useForm } from 'react-hook-form'
import { AiOutlineSave } from 'react-icons/ai'
import Notification from '@/components/ui/Notification'
import { useNavigate, useParams } from 'react-router-dom'
import Input from '@/components/ui/Input'
import { useEffect } from 'react'
import Breadcrumb from '../modals/BreadCrumb'

export default function ManageLabel() {
    const navigate = useNavigate()
    const params = useParams()

    const options = [
        { value: true, label: 'Yes' },
        { value: false, label: 'No' },
    ]

    const breadcrumbItems = [
        { title: 'Labels', url: '/label' },
        { title: params?.id ? 'Update Label' : 'Create Label', url: '' },
    ]

    const getlabelDetail = async () => {
        api.get(endpoints.labels + params.id)
            .then((res) => {
                if (res?.status == 200) {
                    const data = res?.data?.result
                    setValue('nameEn', data?.name?.en)
                    setValue('nameAr', data?.name?.ar)
                    setValue('isActive', {
                        value: data?.isActive,
                        label: data?.isActive ? 'Yes' : 'No',
                    })
                }
            })
            .catch((error) => {
                navigate('/access-denied')
                console.error('Error fetching data: ', error)
            })
    }

    useEffect(() => {
        if (params.id) {
            getlabelDetail()
        }
    }, [])

    const {
        handleSubmit,
        control,
        setValue,
        formState: { errors },
    } = useForm<any>()

    const onSubmit = (value: any) => {
        const data: any = {
            name: {
                en: value.nameEn,
                ar: value.nameAr,
            },
        }

        if (params?.id) {
            data['isActive'] = value.isActive.value
            api.put(endpoints.labels + params.id, data)
                .then((res) => {
                    if (res.status == 200) {
                        toast.push(
                            <Notification
                                type="success"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                        navigate('/label')
                    }
                })
                .catch((err) => {
                    toast.push(
                        <Notification
                            type="warning"
                            title={err.response.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                })
        } else {
            api.post(endpoints.createLabel, data)
                .then((res) => {
                    if (res.status == 200) {
                        toast.push(
                            <Notification
                                type="success"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                        navigate('/label')
                    }
                })
                .catch((err) => {
                    toast.push(
                        <Notification
                            type="warning"
                            title={err.response.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                })
        }
    }

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <h3 className="mb-2">{params?.id ? 'Update' : 'Create'} Label</h3>
            <Breadcrumb items={breadcrumbItems} />

            <div className="grid grid-cols-2 gap-4 mt-4">
                <FormItem label="Name (English)">
                    <Controller
                        control={control}
                        name="nameEn"
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {errors.nameEn && (
                        <small className="text-red-600 py-3">
                            {' '}
                            {errors.nameEn.message as string}{' '}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Name (Arabic)">
                    <Controller
                        control={control}
                        name="nameAr"
                        // rules={{ required: 'Field Required' }}
                        render={({ field }) => (
                            <Input dir="rtl" type="text" {...field} />
                        )}
                    />
                    {/* {errors.nameAr && (
                        <small className="text-red-600 py-3">
                            {' '}
                            {errors.nameAr.message as string}{' '}
                        </small>
                    )} */}
                </FormItem>
            </div>

            <div className="grid grid-cols-2 gap-4">
                {params?.id && (
                    <FormItem label="Is Active ?">
                        <Controller
                            control={control}
                            name="isActive"
                            render={({ field }) => (
                                <Select options={options} {...field} />
                            )}
                        />
                    </FormItem>
                )}
            </div>

            <Button
                className="float-right mt-4"
                variant="solid"
                type="submit"
                icon={<AiOutlineSave />}
            >
                {params?.id ? 'Update' : 'Save'}
            </Button>
        </form>
    )
}
