import { Button, FormItem, Select } from '@/components/ui'
import endpoints from '@/endpoints'
import api from '@/services/api.interceptor'
import { useEffect, useState } from 'react'

interface FilterSectionProps {
    selectedCategories: any,
    setSelectedCategories: any,
    selectedFrameTypes: any,
    setSelectedFrameTypes: any,
    selectedFrameShapes: any,
    setSelectedFrameShapes: any,
    selectedSizes: any,
    setSelectedSizes: any,
    selectedFrontMaterials: any,
    setSelectedFrontMaterials: any,
    selectedColors: any,
    setSelectedColors: any,
    selectedType: any,
    setSelectedType: any,
    selectedLensMaterials: any,
    setSelectedLensMaterials: any,
    onApplyFilter: () => void;
    onClearFilter: () => void;
}

function FilterSection(
    {
        selectedCategories,
        setSelectedCategories,
        selectedFrameTypes,
        setSelectedFrameTypes,
        selectedFrameShapes,
        setSelectedFrameShapes,
        selectedSizes,
        setSelectedSizes,
        selectedFrontMaterials,
        setSelectedFrontMaterials,
        selectedColors,
        setSelectedColors,
        selectedType,
        setSelectedType,
        selectedLensMaterials,
        setSelectedLensMaterials,
        onApplyFilter,
        onClearFilter }
        :
        FilterSectionProps) {
    const [categories, setCategories] = useState([])
    const [frameTypes, setFrameTypes] = useState([])
    const [frameShapes, setFrameShapes] = useState([])
    const [sizes, setSizes] = useState([])
    const [frontMaterials, setFrontMaterials] = useState([])
    const [colors, setColors] = useState([])
    const [type, setType] = useState([])
    const [lensMaterials, setLensMaterials] = useState([])

    const handleCategoryChange = (selectedOptions: any) => {
        setSelectedCategories(selectedOptions)
    }

    const handleFrameTypeChange = (selectedOptions: any) => {
        setSelectedFrameTypes(selectedOptions)
    }

    const handleFrameShapeChange = (selectedOptions: any) => {
        setSelectedFrameShapes(selectedOptions)
    }

    const handleFrameSizeChange = (selectedOptions: any) => {
        setSelectedSizes(selectedOptions)
    }

    const handleFrontMaterialChange = (selectedOptions: any) => {
        setSelectedFrontMaterials(selectedOptions)
    }

    const handleColorChange = (selectedOptions: any) => {
        setSelectedColors(selectedOptions)
    }

    const handleTypeChange = (selectedOptions: any) => {
        setSelectedType(selectedOptions)
    }

    const handleLensMaterialChange = (selectedOptions: any) => {
        setSelectedLensMaterials(selectedOptions)
    }

    const getCategories = () => {
        api.get(endpoints.categories)
            .then((res) => {
                if (res?.status == 200) {
                    const categories = res?.data?.result || []
                    const activeCategories = categories.filter((category: any) => category.isActive)
                    const newCategoryOptions = activeCategories.map((category: any) => ({
                        value: category._id,
                        label: category.name.en
                    }))
                    setCategories(newCategoryOptions)
                }
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }

    const getFrameTypes = () => {
        api.get(endpoints.frameTypes)
            .then((res) => {
                if (res?.status == 200) {
                    const frameTypes = res?.data?.result || []
                    const activeFrameTypes = frameTypes.filter((frameType: any) => frameType.isActive)
                    const newFrameTypeOptions = activeFrameTypes.map((frameType: any) => ({
                        value: frameType._id,
                        label: frameType.name.en
                    }))
                    setFrameTypes(newFrameTypeOptions)
                }
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }

    const getFrameShapes = () => {
        api.get(endpoints.frameShapes)
            .then((res) => {
                if (res?.status == 200) {
                    const frameShapes = res?.data?.result || []
                    const activeFrameShapes = frameShapes.filter((frameShape: any) => frameShape.isActive)
                    const newFrameShapeOptions = activeFrameShapes.map((frameShape: any) => ({
                        value: frameShape._id,
                        label: frameShape.name.en
                    }))
                    setFrameShapes(newFrameShapeOptions)
                }
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }

    const getSizes = () => {
        api.post(endpoints.sizes, { isActive: true }).then((res) => {
            if (res.status == 200) {
                const sizes = res?.data?.result || []
                const activeOptions = sizes.filter((size: any) => size.isActive)
                const newSizeOptions = activeOptions.map((size: any) => ({
                    value: size._id,
                    label: size.name
                }))
                setSizes(newSizeOptions)
            }
        })
    }

    const getFrontMaterials = () => {
        api.get(endpoints.frontMaterials).then((res) => {
            if (res?.status == 200) {
                const frontMaterials = res?.data?.result || []
                const activeOptions = frontMaterials.filter((frontMaterial: any) => frontMaterial.isActive)
                const newFrontMaterialOptions = activeOptions.map((frontMaterial: any) => ({
                    value: frontMaterial._id,
                    label: frontMaterial.name?.en
                }))
                setFrontMaterials(newFrontMaterialOptions)
            }
        })
    }

    const getColors = () => {
        api.post(endpoints.colors, {}).then((res) => {
            if (res?.status == 200) {
                const colors = res?.data?.result || []
                const activeOptions = colors.filter((color: any) => color.isActive)
                const newColorOptions = activeOptions.map((color: any) => ({
                    value: color._id,
                    label: color.name?.en
                }))
                setColors(newColorOptions)
            }
        })
    }

    const getTypes = () => {
        api.get(endpoints.types).then((res) => {
            if (res?.status == 200) {
                const type = res?.data?.result || []
                const activeOptions = type.filter((types: any) => types.isActive)
                const newTypeOptions = activeOptions.map((types: any) => ({
                    value: types._id,
                    label: types.name?.en
                }))
                setType(newTypeOptions)
            }
        })
    }

    const getLensMaterials = () => {
        api.get(endpoints.lensMaterials).then((res) => {
            if (res?.status == 200) {
                const lensMaterials = res?.data?.result || []
                const activeOptions = lensMaterials.filter((lensMaterial: any) => lensMaterial.isActive)
                const newLensMaterialOptions = activeOptions.map((lensMaterial: any) => ({
                    value: lensMaterial._id,
                    label: lensMaterial.name?.en
                }))
                setLensMaterials(newLensMaterialOptions)
            }
        })
    }

    useEffect(() => {
        getCategories()
        getFrameTypes()
        getFrameShapes()
        getSizes()
        getFrontMaterials()
        getColors()
        getTypes()
        getLensMaterials()
    }, [])


    return (
        <div>
            <div className='grid grid-cols-2 gap-4 mb-3'>
                <Button
                    onClick={onClearFilter}
                    variant="solid"
                    color="red-600"
                >
                    Clear Filter
                </Button>
                <Button
                    variant="solid"
                    color="green-600"
                    onClick={onApplyFilter}
                >
                    Apply Filter
                </Button>
            </div>
            <FormItem label="Categories">
                <Select
                    placeholder="Select Categories"
                    options={categories}
                    value={selectedCategories}
                    onChange={handleCategoryChange}
                    isMulti
                />
            </FormItem>

            <FormItem label="Frame Types">
                <Select
                    placeholder="Select Frame Types"
                    options={frameTypes}
                    value={selectedFrameTypes}
                    onChange={handleFrameTypeChange}
                    isMulti
                />
            </FormItem>

            <FormItem label="Frame Shapes">
                <Select
                    placeholder="Select Frame Shapes"
                    options={frameShapes}
                    value={selectedFrameShapes}
                    onChange={handleFrameShapeChange}
                    isMulti
                />
            </FormItem>

            <FormItem label="Frame Size">
                <Select
                    placeholder="Select Frame size"
                    options={sizes}
                    value={selectedSizes}
                    onChange={handleFrameSizeChange}
                    isMulti
                />
            </FormItem>

            <FormItem label="Front Material">
                <Select
                    placeholder="Select Front Material"
                    options={frontMaterials}
                    value={selectedFrontMaterials}
                    onChange={handleFrontMaterialChange}
                    isMulti
                />
            </FormItem>

            <FormItem label="Frame Color">
                <Select
                    placeholder="Select Frame Color"
                    options={colors}
                    value={selectedColors}
                    onChange={handleColorChange}
                    isMulti
                />
            </FormItem>

            <FormItem label="Type">
                <Select
                    placeholder="Select Type"
                    options={type}
                    value={selectedType}
                    onChange={handleTypeChange}
                    isMulti
                />
            </FormItem>

            <FormItem label="Lens Type">
                <Select
                    placeholder="Select Lens Type"
                    options={lensMaterials}
                    value={selectedLensMaterials}
                    onChange={handleLensMaterialChange}
                    isMulti
                />
            </FormItem>

        </div>
    )
}

export default FilterSection