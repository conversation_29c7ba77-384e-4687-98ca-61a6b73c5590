import { FormItem, Input } from '@/components/ui'
import React from 'react'
import { Controller } from 'react-hook-form'


const order: { label: string, value: string, db: string }[] = [
    {
        label: "Order No",
        value: "OrderNo",
        db: "orderNo",
    },
    {
        label: "Order Date",
        value: "OrderDate",
        db: "orderDate",
    },
    {
        label: "Estimated Delivery",
        value: "EstimatedDelivery",
        db: "estimatedDelivery",
    },
    {
        label: "Order Status",
        value: "OrderStatus",
        db: "orderStatus",
    },
    {
        label: "Payment Method",
        value: "PaymentMethod",
        db: "paymentMethod",
    },
    {
        label: "Payment Status",
        value: "PaymentStatus",
        db: "paymentStatus",
    },
    {
        label: "View Detail",
        value: "ViewDetail",
        db: "viewDetail",
    },
    {
        label: "Customer Details",
        value: "CustomerDetails",
        db: "customerDetails",
    },
]

const orderStatus: { label: string, value: string, db: string }[] = [
    {
        label: "Order Placed",
        value: "OrderPlaced",
        db: "orderPlaced",
    },
    {
        label: "Confirm",
        value: "Confirm",
        db: "confirm",
    },
    {
        label: "Shipped",
        value: "Shipped",
        db: "shipped",
    },
    {
        label: "Delivered",
        value: "Delivered",
        db: "delivered",
    },
    {
        label: "Cancelled",
        value: "Cancelled",
        db: "cancelled",
    },
    {
        label: "Invoice Summary",
        value: "InvoiceSummary",
        db: "invoiceSummary",
    },
]

const other: { label: string, value: string, db: string }[] = [
    {
        label: "Cod",
        value: "Cod",
        db: "cod",
    },
    {
        label: "Online",
        value: "Online",
        db: "online",
    },
    {
        label: "Pending",
        value: "Pending",
        db: "pending",
    },
    {
        label: "Paid",
        value: "Paid",
        db: "paid",
    },
    {
        label: "Download Invoice",
        value: "DownloadInvoice",
        db: "downloadInvoice",
    },
]

export const orderPage: { label: string, value: string, db: string }[] = [
    ...order,
    ...orderStatus,
    ...other
]

function OrderTab({ control, errors }: any) {
    return (
        <>
            <h3>Order Page</h3>
            {order.map((item) => (
                <Forms key={item.value} control={control} errors={errors} item={item} />
            ))}
            <h3>Order Status</h3>
            {orderStatus.map((item) => (
                <Forms key={item.value} control={control} errors={errors} item={item} />
            ))}
            <h3>Other</h3>
            {other.map((item) => (
                <Forms key={item.value} control={control} errors={errors} item={item} />
            ))}
        </>
    )
}

function Forms({ item, control, errors }: any) {
    return (
        <div className="mt-2">
            <div className="grid grid-cols-2 gap-4">
                <FormItem label={`${item.label} English`}>
                    <Controller
                        control={control}
                        name={`orderPage${item.value}En`}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {errors[`orderPage${item.value}En`] && (
                        <small className="text-red-600 py-3">
                            {' '}
                            {errors[`orderPage${item.value}En`].message as string}{' '}
                        </small>
                    )}
                </FormItem>
                <FormItem label={`${item.label} Arabic`}>
                    <Controller
                        control={control}
                        name={`orderPage${item.value}Ar`}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => <Input dir='rtl' type="text" {...field} />}
                    />
                    {errors[`orderPage${item.value}Ar`] && (
                        <small className="text-red-600 py-3">
                            {' '}
                            {errors[`orderPage${item.value}Ar`].message as string}{' '}
                        </small>
                    )}
                </FormItem>
            </div>
        </div>
    )
}

export default OrderTab