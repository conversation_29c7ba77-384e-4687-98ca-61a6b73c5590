/*eslint-disable*/
import { useState, useMemo, useEffect, InputHTMLAttributes } from 'react'
import Table from '@/components/ui/Table'
import Pagination from '@/components/ui/Pagination'
import Select from '@/components/ui/Select'
import { useReactTable, getCoreRowModel, getFilteredRowModel, getPaginationRowModel, flexRender, getSortedRowModel, } from '@tanstack/react-table'
import type { ColumnDef, ColumnFiltersState, FilterFn, } from '@tanstack/react-table'
import Input from '@/components/ui/Input'
import { rankItem } from '@tanstack/match-sorter-utils'
import api from '@/services/api.interceptor'
import endpoints from '@/endpoints'
import { MdOutlineRemoveRedEye } from 'react-icons/md'
import Breadcrumb from '../modals/BreadCrumb'
import { Button, Dialog } from '@/components/ui'
import { Link, useNavigate } from 'react-router-dom'
import { HiPencilSquare } from 'react-icons/hi2'

interface DebouncedInputProps
    extends Omit<
        InputHTMLAttributes<HTMLInputElement>,
        'onChange' | 'size' | 'prefix'
    > {
    value: string | number
    onChange: (value: string | number) => void
    debounce?: number
}

const { Tr, Th, Td, THead, TBody, Sorter } = Table

function DebouncedInput({
    value: initialValue,
    onChange,
    debounce = 500,
    ...props
}: DebouncedInputProps) {
    const [value, setValue] = useState(initialValue)

    useEffect(() => {
        setValue(initialValue)
    }, [initialValue])

    useEffect(() => {
        const timeout = setTimeout(() => {
            onChange(value)
        }, debounce)

        return () => clearTimeout(timeout)
    }, [value])

    return (
        <div className="flex justify-end">
            <div className="flex items-center mb-4">
                <Input
                    {...props}
                    value={value}
                    onChange={(e) => setValue(e.target.value)}
                />
            </div>
        </div>
    )
}

type Option = {
    value: number
    label: string
}

const pageSizeOption = [
    { value: 10, label: '10 / page' },
    { value: 20, label: '20 / page' },
    { value: 30, label: '30 / page' },
    { value: 40, label: '40 / page' },
    { value: 50, label: '50 / page' },
]

const breadcrumbItems = [
    { title: 'Lens Enquiries', url: '' },
];

const LensEnquiries = () => {
    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
    const [globalFilter, setGlobalFilter] = useState('')
    const [dialogIsOpen, setIsOpen] = useState(false)
    const [prescriptionData, setPrescriptionData] = useState<any>({})
    const navigate = useNavigate()

    const openDialog = (prescription: any) => {
        setPrescriptionData(prescription);
        setIsOpen(true)
    }

    const onDialogClose = (e: any) => {
        console.log('onDialogClose', e)
        setIsOpen(false)
    }

    const baseUrl = import.meta.env.VITE_ASSET_URL

    const columns = useMemo<ColumnDef<any>[]>(
        () => [
            {
                header: 'Sl No.',
                accessorKey: 'slNo',
                cell: (info) => <td className='sticky left-0'>{info.row.index + 1}</td>,
                enableSorting: false,
                
                
            },
            {
                header: 'Name',
                accessorKey: 'userDetails.name',
                enableSorting: false,
            },
            {
                header: 'Mobile',
                accessorKey: 'userDetails.phone',
                enableSorting: false,
            },
            {
                header: 'Address',
                accessorKey: 'userDetails.message',
                enableSorting: false,
            },
            {
                header: 'Actions',
                accessorKey: 'refid',
                cell: (info) => {
                    return (
                        info.row.original?.refid ? <div className="flex items-center">
                            <Link
                                to={`/lens-enquiries/${info.getValue()}`}
                            >
                                <MdOutlineRemoveRedEye size={25} className="cursor-pointer" />
                            </Link>
                        </div>: ""
                    )
                },
                enableSorting: false
            },
        ],
        []
    )

    const [data, setData] = useState<any[]>([])
    const totalData = data?.length


    const fuzzyFilter: FilterFn<any> = (row, columnId, value, addMeta) => {
        const itemRank = rankItem(row.getValue(columnId), value)

        addMeta({
            itemRank,
        })

        return itemRank.passed
    }

    const table = useReactTable({
        data,
        columns,
        filterFns: {
            fuzzy: fuzzyFilter,
        },
        state: {
            columnFilters,
            globalFilter,
        },
        onColumnFiltersChange: setColumnFilters,
        onGlobalFilterChange: setGlobalFilter,
        globalFilterFn: fuzzyFilter,
        getSortedRowModel: getSortedRowModel(),
        getCoreRowModel: getCoreRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
    })

    const onPaginationChange = (page: number) => {
        table.setPageIndex(page - 1)
    }

    const onSelectChange = (value = 0) => {
        table.setPageSize(Number(value))
    }

    useEffect(() => {
        api
            .get(endpoints.lensEnquiries)
            .then((res) => {
                setData(res?.data?.result)
            })
            .catch((error) => {
                navigate('/access-denied')
                console.error('Error fetching data: ', error)
            })
    }, [])

    return (
        <div>
            <div className='mb-4 flex'>
                <h2>Lens Enquiries</h2>
            </div>
            <Breadcrumb items={breadcrumbItems} />
            <div className="mb-4 flex space-x-2 justify-end">
                <DebouncedInput
                    value={globalFilter ?? ''}
                    className="p-2 font-lg shadow border border-block"
                    placeholder="Search all columns..."
                    onChange={(value) => setGlobalFilter(String(value))}
                />
            </div>
            <Table>
                <THead>
                    {table.getHeaderGroups().map((headerGroup) => (
                        <Tr key={headerGroup.id}>
                            {headerGroup.headers.map((header) => {
                                return (
                                    <Th
                                        key={header.id}
                                        colSpan={header.colSpan}
                                    >
                                        {header.column.getCanSort() ? (
                                            <div
                                                className="cursor-pointer select-none"
                                                onClick={header.column.getToggleSortingHandler()}
                                            >
                                                {flexRender(
                                                    header.column.columnDef.header,
                                                    header.getContext()
                                                )}
                                                <Sorter sort={header.column.getIsSorted()} />
                                            </div>
                                        ) : (
                                            <div>
                                                {flexRender(
                                                    header.column.columnDef.header,
                                                    header.getContext()
                                                )}
                                            </div>
                                        )}
                                    </Th>
                                )
                            })}
                        </Tr>
                    ))}
                </THead>
                <TBody>
                    {table.getRowModel().rows.map((row) => {
                        return (
                            <Tr key={row.id}>
                                {row.getVisibleCells().map((cell) => {
                                    return (
                                        <Td key={cell.id}>
                                            {flexRender(
                                                cell.column.columnDef.cell,
                                                cell.getContext()
                                            )}
                                        </Td>
                                    )
                                })}
                            </Tr>
                        )
                    })}
                </TBody>
            </Table>
            <div className="flex items-center justify-between mt-4">
                <Pagination
                    pageSize={table.getState().pagination.pageSize}
                    currentPage={table.getState().pagination.pageIndex + 1}
                    total={totalData}
                    onChange={onPaginationChange}
                />
                <div style={{ minWidth: 130 }}>
                    <Select<Option>
                        size="sm"
                        isSearchable={false}
                        value={pageSizeOption.filter(
                            (option) =>
                                option.value ===
                                table.getState().pagination.pageSize
                        )}
                        options={pageSizeOption}
                        onChange={(option) => onSelectChange(option?.value)}
                    />
                </div>
            </div>

            <Dialog
                isOpen={dialogIsOpen}
                onClose={onDialogClose}
                onRequestClose={onDialogClose}
            >
                <h5 className="mb-4">Lens Specifications</h5>
                <div className="grid grid-cols-2 gap-4">
                    <p>left Sph : {prescriptionData?.leftSph?.name}</p>
                    <p>Right Sph : {prescriptionData?.rightSph?.name}</p>
                </div>
                <div className="grid grid-cols-2 gap-4">
                    <p>left Cyl : {prescriptionData?.leftCyl?.name}</p>
                    <p>Right Cyl : {prescriptionData?.rightCyl?.name}</p>
                </div>
                <div className="grid grid-cols-2 gap-4">
                    <p>left Axis : {prescriptionData?.leftAxis?.name}</p>
                    <p>Right Axis : {prescriptionData?.rightAxis?.name}</p>
                </div>
            </Dialog>
        </div>
    )
}

export default LensEnquiries