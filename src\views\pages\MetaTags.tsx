/* eslint-disable */
import { Button, FormItem, Tag, toast } from '@/components/ui'
import endpoints from '@/endpoints'
import api from '@/services/api.interceptor'
import { Controller, useForm } from 'react-hook-form'
import { AiOutlineSave } from 'react-icons/ai'
import Notification from '@/components/ui/Notification'
import Input from '@/components/ui/Input'
import { useEffect, useState } from 'react'
import { Dropzone } from '@/components/shared/Dropzone'
import Breadcrumb from '../modals/BreadCrumb'
import { HiX } from 'react-icons/hi'

export default function MetaTags() {
    const [isUpdate, setIsUpdate] = useState(false)
    const [imageFile, setImageFile] = useState<string[]>([])
    const [keywords, setKeywords] = useState<string[]>([])
    const [keywordsError, setKeywordsError] = useState('')

    const breadcrumbItems = [{ title: 'Meta Tags', url: '' }]

    const getMetaTags = async () => {
        api.get(endpoints.metaTags)
            .then((res) => {
                if (res?.status == 200 && res.data.result) {
                    const data = res?.data?.result
                    setValue('title', data?.title)
                    setValue('description', data?.description)
                    setKeywords(data?.keywords)
                    setIsUpdate(true)
                    setImageFile([data?.image])
                }
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }

    const onkeyword = (e: any) => {
        if (e.key === 'Enter') {
            e.preventDefault()
            const newKeyword = e.currentTarget.value.trim()
            if (newKeyword) {
                setKeywords([...keywords, newKeyword])
                console.log(keywords)
                // setValue('keywords', [...keywords, newKeyword])
                setKeywordsError('') // Clear the validation error
                e.currentTarget.value = ''
            }
        }
    }

    useEffect(() => {
        getMetaTags()
    }, [])

    const {
        handleSubmit,
        control,
        setValue,
        formState: { errors },
    } = useForm<any>()

    const onSubmit = (value: any) => {
        console.log(value)
        if (value.keywords.length === 0)
            setKeywordsError('At least one keyword is required')
        else setKeywordsError('')

        const payload = {
            title: value.title,
            description: value.description,
            keywords: keywords,
            // image : imageFile[0]
        }

        if (isUpdate) {
            api.put(endpoints.metaTags, payload)
                .then((res) => {
                    if (res.status == 200) {
                        toast.push(
                            <Notification
                                type="success"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                    }
                })
                .catch((err) => {
                    toast.push(
                        <Notification
                            type="warning"
                            title={err.response.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                })
        } else {
            api.post(endpoints.metaTags, payload)
                .then((res) => {
                    if (res.status == 200) {
                        toast.push(
                            <Notification
                                type="success"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                    }
                })
                .catch((err) => {
                    toast.push(
                        <Notification
                            type="warning"
                            title={err.response.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                })
        }
    }

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <h3 className="mb-2">Meta Tags</h3>
            <Breadcrumb items={breadcrumbItems} />
            <div className="grid grid-cols-2 gap-4">
                <FormItem label="Title">
                    <Controller
                        control={control}
                        name="title"
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {errors.title && (
                        <small className="text-red-600 py-3">
                            {' '}
                            {errors.title.message as string}{' '}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Description">
                    <Controller
                        control={control}
                        name="description"
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => (
                            <Input type="text" textArea {...field} />
                        )}
                    />
                    {keywordsError && (
                        <small className="text-red-600 py-3">
                            {keywordsError}
                        </small>
                    )}
                </FormItem>
            </div>
            <div>
                <FormItem label="Keywords">
                    <Controller
                        control={control}
                        name="keywords"
                        render={({ field }) => (
                            <div>
                                <Input
                                    type="text"
                                    {...field}
                                    onKeyPress={(e) => {
                                        onkeyword(e)
                                    }}
                                />
                                <div className="flex flex-wrap gap-2 mt-2">
                                    {keywords.map((keyword, index) => (
                                        <Tag
                                            key={index}
                                            className="bg-emerald-100 text-emerald-600 dark:bg-emerald-500/20 dark:text-emerald-100 border-0 rounded"
                                            suffix={
                                                <HiX
                                                    className="ml-1 rtl:mr-1 cursor-pointer"
                                                    onClick={() => {
                                                        const updatedKeywords =
                                                            keywords.filter(
                                                                (_, i) =>
                                                                    i !== index
                                                            )
                                                        setKeywords(
                                                            updatedKeywords
                                                        )
                                                        setValue(
                                                            'keywords',
                                                            updatedKeywords
                                                        )
                                                    }}
                                                />
                                            }
                                        >
                                            {keyword}
                                        </Tag>
                                    ))}
                                </div>
                            </div>
                        )}
                    />
                </FormItem>
            </div>{' '}
            {/* <div>
                <FormItem label="Image">
                    <Dropzone
                        previews={imageFile}
                        onFileUrlChange={(e) => {
                            setImageFile([e])
                        }}
                    />
                </FormItem>
            </div> */}
            <Button
                className="float-right mt-4"
                variant="solid"
                type="submit"
                icon={<AiOutlineSave />}
            >
                {isUpdate ? 'Update' : 'Save'}
            </Button>
        </form>
    )
}
