/* eslint-disable */
import { <PERSON><PERSON>, FormItem, Select, toast } from "@/components/ui"
import { Input } from "@/components/ui/Input"
import { useEffect, useState } from "react"
import { Controller, useFieldArray, useForm } from "react-hook-form"
import { AiOutlinePlus } from "react-icons/ai"
import { IoIosCloseCircleOutline } from "react-icons/io"
import { pages } from "./pageoptions"
import api from "@/services/api.interceptor"
import endpoints from "@/endpoints"
import Notification from '@/components/ui/Notification'

export default function Footer() {
  const [pageOptions, setPageOptions] = useState<any>(pages)
  const [isUpdate, setIsUpdate] = useState(false)
  const [refid, setRefid] = useState<any>()

  const {
    handleSubmit,
    control,
    setValue,
    formState: { errors },
  } = useForm<any>()

  const {
    fields: firstColFields,
    append: firstColAppend,
    remove: firstColRemove,
  } = useFieldArray({
    control,
    name: 'firstCol',
  })

  const {
    fields: secondColFields,
    append: secondColAppend,
    remove: secondColRemove,
  } = useFieldArray({
    control,
    name: 'secondCol',
  })

  const {
    fields: thirdColFields,
    append: thirdColAppend,
    remove: thirdColRemove,
  } = useFieldArray({
    control,
    name: 'thirdCol',
  })

  const {
    fields: fourthColFields,
    append: fourthColAppend,
    remove: fourthColRemove,
  } = useFieldArray({
    control,
    name: 'fourthCol',
  })

  const getFooter = () => {
    api.get(endpoints.footer).then((res: any) => {
      if (res.status == 200) {
        setValue('firstColtitle', res?.data?.result?.firstCol.title?.en)
        setValue('firstColAr', res?.data?.result?.firstCol.title?.ar)
        setValue('secondColtitle', res?.data?.result?.secondCol.title?.en)
        setValue('secondColAr', res?.data?.result?.secondCol.title?.ar)
        setValue('thirdColtitle', res?.data?.result?.thirdCol.title?.en)
        setValue('thirdColAr', res?.data?.result?.thirdCol.title?.ar)
        setValue('fourthColtitle', res?.data?.result?.fourthCol.title?.en)
        setValue('fourthColAr', res?.data?.result?.fourthCol.title?.ar)
        setValue('firstCol', res?.data?.result?.firstCol.links.map((item: any) => ({ title: item.title?.en, titleAr: item.title?.ar, link: item.link })) || [])
        setValue('secondCol', res?.data?.result?.secondCol.links.map((item: any) => ({ title: item.title?.en, titleAr: item.title?.ar, link: item.link})) || [])
        setValue('thirdCol', res?.data?.result?.thirdCol.links.map((item: any) => ({ title: item.title?.en, titleAr: item.title?.ar, link: item.link })) || [])
        setValue('fourthCol', res?.data?.result?.fourthCol.links.map((item: any) => ({ title: item.title.en, titleAr: item.title.ar, link: item.link })) || [])
        setIsUpdate(true)
        setRefid(res?.data?.result?.refid)
      }
    })
  }

  useEffect(() => {
    getFooter()
  }, [])

  const onSubmit = (data: any) => {
    console.log("data" ,data)
    const payload = {
      firstCol: {
        title:{
          en: data.firstColtitle,
          ar: data.firstColAr
        },
        links: data.firstCol.map((item: any) => ({
          title: {
            en: item.title,
            ar: item.titleAr
          },
          link: item.link,
          label: item?.link?.label
        }))
      },
      secondCol: {
        title:{
          en: data.secondColtitle,
          ar: data.secondColAr
        } ,
        links: data.secondCol.map((item: any) => ({
          title: {
            en: item.title,
            ar: item.titleAr
          },
          link: item.link,
          label: item?.link?.label
        })),
      },
      thirdCol: {
        title: {
          en: data.thirdColtitle,
          ar: data.thirdColAr
        },
        links: data.thirdCol.map((item: any) => ({
          title: {
            en: item.title,
            ar: item.titleAr
          },
          link: item.link,
          label: item?.link?.label
        })),
      },
      fourthCol: {
        title: {
          en: data.fourthColtitle,
          ar: data.fourthColAr
        },
        links: data.fourthCol.map((item: any) => ({
          title: {
            en: item.title,
            ar: item.titleAr
          },
          link: item.link,
          label: item?.link?.label
        })),
      }
    }

    console.log("payload" ,payload)

    if (isUpdate) {
      api.put(endpoints.footer + refid, payload).then((res) => {
        if (res.status == 200) {
          toast.push(<Notification type="success" title="Footer updated successfully." />, {
            placement: 'top-center'
          })
          getFooter()
        }
      }).catch((err) => {
        toast.push(<Notification type="warning" title={err.response.data.message} />, {
          placement: 'top-center'
        })
      })
    }
    else {
      api.post(endpoints.footer, payload).then((res) => {
        if (res.status == 200) {
          toast.push(<Notification type="success" title="Footer updated successfully." />, {
            placement: 'top-center'
          })
          getFooter()
        }
      }).catch((err) => {
        toast.push(<Notification type="warning" title={err.response.data.message} />, {
          placement: 'top-center'
        })
      })
    }
  }

  return (
    <form className="text-white p-10" onSubmit={handleSubmit(onSubmit)}>
      <div className="grid grid-cols-4 gap-8">

        <div>
          <Controller
            name="firstColtitle"
            control={control}
            defaultValue=""
            render={({ field }) => <Input {...field} className="border-b text-black border-black mb-1" placeholder="Products" />}
          />
          <Controller 
            name="firstColAr"
            control={control}
            defaultValue=""
            render={({ field }) => <Input {...field} dir="rtl" className="border-b text-black border-black mb-1" placeholder="Title Arabic" />}
          />
          <ul>
            {firstColFields.map((item, index) => {
              return (
                <li key={item.id} className="mb-3 bg-blue-100 rounded-md mt-2 py-6 px-3 relative">
                  <Controller
                    name={`firstCol[${index}].title`}
                    control={control}
                    defaultValue=""
                    render={({ field }) => <Input {...field} className="border-b text-black border-black mb-1" placeholder="Title" />}
                  />
                  <Controller
                    name={`firstCol[${index}].titleAr`}
                    control={control}
                    defaultValue=""
                    render={({ field }) => <Input {...field} dir="rtl" className="border-b text-black border-black mb-1" placeholder="Title Arabic" />}
                  />
                  <Controller
                    name={`firstCol[${index}].link`}
                    control={control}
                    defaultValue=""
                    render={({ field }) => <Input type="text" {...field} className="border-b text-black border-black" placeholder="Link" />}
                  />

                  {/* <Controller
                    name={`firstCol[${index}].link`}
                    control={control}
                    defaultValue=""
                    render={({ field }) => <Select options={pageOptions} {...field} className="border-b text-black border-black" placeholder="Link" />}
                  /> */}

                  <div className="absolute right-0 top-0">
                    <IoIosCloseCircleOutline size={20} color="red" onClick={() => firstColRemove(index)} />
                  </div>
                </li>
              )
            })}
          </ul>

          <Button className='my-3' variant="solid" type="button" icon={<AiOutlinePlus />} onClick={() => {
            firstColAppend({});
          }}>
            Add More
          </Button>

        </div>

        <div>
          {/* <h3 className="font-bold text-lg mb-4">Account</h3> */}
          <Controller
            name="secondColtitle"
            control={control}
            defaultValue=""
            render={({ field }) => <Input {...field} className="border-b text-black border-black mb-1" placeholder="Account" />}
          />
          <Controller
            name="secondColAr"
            control={control}
            defaultValue=""
            render={({ field }) => <Input {...field} dir="rtl" className="border-b text-black border-black mb-1" placeholder="Title Arabic" />}
          />

          <ul>
            {secondColFields.map((item, index) => {
              return (
                <li key={item.id} className="mb-3 bg-blue-100 rounded-md mt-2 py-6 px-3 relative">
                  <Controller
                    name={`secondCol[${index}].title`}
                    control={control}
                    defaultValue=""
                    render={({ field }) => <Input {...field} className="border-b text-black border-black mb-1" placeholder="Title" />}
                  />
                  <Controller
                    name={`secondCol[${index}].titleAr`}
                    control={control}
                    defaultValue=""
                    render={({ field }) => <Input {...field} dir="rtl" className="border-b text-black border-black mb-1" placeholder="Title Arabic" />}
                  />
                   <Controller
                    name={`secondCol[${index}].link`}
                    control={control}
                    defaultValue=""
                    render={({ field }) => <Input type="text" {...field} className="border-b text-black border-black" placeholder="Link" />}
                  />

                  {/* <Controller
                    name={`secondCol[${index}].link`}
                    control={control}
                    defaultValue=""
                    render={({ field }) => <Select options={pageOptions} {...field} className="border-b text-black border-black" placeholder="Link" />}
                  /> */}

                  <div className="absolute right-0 top-0">
                    <IoIosCloseCircleOutline size={20} color="red" onClick={() => secondColRemove(index)} />
                  </div>
                </li>
              )
            })}
          </ul>

          <Button className='my-3' variant="solid" type="button" icon={<AiOutlinePlus />} onClick={() => {
            secondColAppend({});
          }}>
            Add More
          </Button>

        </div>
        <div>
          {/* <h3 className="font-bold text-lg mb-4">Our Company</h3> */}
          <Controller
            name="thirdColtitle"
            control={control}
            defaultValue=""
            render={({ field }) => <Input {...field} className="border-b text-black border-black mb-1" placeholder="Our Company" />}
          />
          <Controller
            name="thirdColAr"
            control={control}
            defaultValue=""
            render={({ field }) => <Input {...field} dir="rtl" className="border-b text-black border-black mb-1" placeholder="Title Arabic" />}
          />
          <ul>
            {thirdColFields.map((item, index) => {
              return (
                <li key={item.id} className="mb-3 bg-blue-100 rounded-md mt-2 py-6 px-3 relative">
                  <Controller
                    name={`thirdCol[${index}].title`}
                    control={control}
                    defaultValue=""
                    render={({ field }) => <Input {...field} className="border-b text-black border-black mb-1" placeholder="Title" />}
                  />
                  <Controller
                    name={`thirdCol[${index}].titleAr`}
                    control={control}
                    defaultValue=""
                    render={({ field }) => <Input {...field} dir="rtl" className="border-b text-black border-black mb-1" placeholder="Title Arabic" />}
                  />
                  <Controller
                    name={`thirdCol[${index}].link`}
                    control={control}
                    defaultValue=""
                    render={({ field }) => <Input type="text" {...field} className="border-b text-black border-black" placeholder="Link" />}
                  />

                  {/* <Controller
                    name={`thirdCol[${index}].link`}
                    control={control}
                    defaultValue=""
                    render={({ field }) => <Select options={pageOptions} {...field} className="border-b text-black border-black" placeholder="Link" />}
                  /> */}

                  <div className="absolute right-0 top-0">
                    <IoIosCloseCircleOutline size={20} color="red" onClick={() => thirdColRemove(index)} />
                  </div>
                </li>
              )
            })}
          </ul>

          <Button className='my-3' variant="solid" type="button" icon={<AiOutlinePlus />} onClick={() => {
            thirdColAppend({});
          }}>
            Add More
          </Button>

        </div>
        <div>
          {/* <h3 className="font-bold text-lg mb-4">Help</h3> */}
          <Controller
            name="fourthColtitle"
            control={control}
            defaultValue=""
            render={({ field }) => <Input {...field} className="border-b text-black border-black mb-1" placeholder="Help" />}
          />
          <Controller
            name="fourthColAr"
            control={control}
            defaultValue=""
            render={({ field }) => <Input {...field} dir="rtl" className="border-b text-black border-black mb-1" placeholder="Title Arabic" />}
          />
          <ul>
            {fourthColFields.map((item, index) => {
              return (
                <li key={item.id} className="mb-3 bg-blue-100 rounded-md mt-2 py-6 px-3 relative">
                  <Controller
                    name={`fourthCol[${index}].title`}
                    control={control}
                    defaultValue=""
                    render={({ field }) => <Input {...field} className="border-b text-black border-black mb-1" placeholder="Title" />}
                  />
                  <Controller
                    name={`fourthCol[${index}].titleAr`}
                    control={control}
                    defaultValue=""
                    render={({ field }) => <Input {...field} dir="rtl" className="border-b text-black border-black mb-1" placeholder="Title Arabic" />}
                  />
                  <Controller
                    name={`fourthCol[${index}].link`}
                    control={control}
                    defaultValue=""
                    render={({ field }) => <Input type="text" {...field} className="border-b text-black border-black" placeholder="Link" />}
                  />

                  {/* <Controller
                    name={`fourthCol[${index}].link`}
                    control={control}
                    defaultValue=""
                    render={({ field }) => <Select options={pageOptions} {...field} className="border-b text-black border-black" placeholder="Link" />}
                  /> */}

                  <div className="absolute right-0 top-0">
                    <IoIosCloseCircleOutline size={20} color="red" onClick={() => fourthColRemove(index)} />
                  </div>
                </li>
              )
            })}
          </ul>

          <Button className='my-3' variant="solid" type="button" icon={<AiOutlinePlus />} onClick={() => {
            fourthColAppend({});
          }}>
            Add More
          </Button>

        </div>
      </div>

      <div className="flex justify-center mt-3">
        <Button type="submit" variant="solid" color="green">
          Save
        </Button>
      </div>
    </form >
  )
}