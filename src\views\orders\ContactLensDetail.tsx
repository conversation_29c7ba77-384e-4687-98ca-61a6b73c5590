import { Dialog } from '@/components/ui'

interface LensDetailsModalProps {
    isOpen: boolean
    onClose: () => void
    lensDetails: any
}

const ContactLensDetails = ({
    isOpen,
    onClose,
    lensDetails,
}: LensDetailsModalProps) => {
    return (
        <Dialog
            className={'overflow-hidden'}
            width={600}
            height={400}
            isOpen={isOpen}
            onClose={onClose}
        >
            <div className="p-4">
                <h2 className="text-xl font-bold mb-4">Contact Lens Details</h2>
                <div className="grid grid-cols-2 gap-4">
                    <div className="flex flex-col gap-1">
                        <p>Left Sph: {lensDetails?.sphLeft?.name || 'N/A'}</p>
                        <p>Left Cyl: {lensDetails?.cylLeft?.name || 'N/A'}</p>
                        <p>Left Axis: {lensDetails?.axisLeft?.name || 'N/A'}</p>
                        <p>Right Sph: {lensDetails?.sphRight?.name || 'N/A'}</p>
                        <p>Right Cyl: {lensDetails?.cylRight?.name || 'N/A'}</p>
                        <p>Right Axis: {lensDetails?.axisRight?.name || 'N/A'}</p>
                        <p>Addition : {lensDetails?.multiFocal || 'N/A'}</p>
                    </div>
                </div>
            </div>
        </Dialog>
    )
}

export default ContactLensDetails
