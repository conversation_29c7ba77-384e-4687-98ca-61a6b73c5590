
import Button from '@/components/ui/Button'
import Dialog from '@/components/ui/Dialog'
import type { MouseEvent } from 'react'

interface DeleteModalProps {
    isOpen: boolean
    onClose: (e: MouseEvent) => void
    onConfirm: (e: MouseEvent) => void
    title: string
    content: string
}

const DeleteModal: React.FC<DeleteModalProps> = ({
    isOpen,
    onClose,
    onConfirm,
    title,
    content,
}) => {
    return (
        <Dialog isOpen={isOpen} onClose={onClose} onRequestClose={onClose}>
            <h5 className="mb-4">{title}</h5>
            <p>{content}</p>
            <div className="text-right mt-6">
                <Button
                    className="ltr:mr-2 rtl:ml-2 border-black"
                    onClick={onClose}
                >
                    No
                </Button>
                <Button variant="solid" color="red" onClick={onConfirm}>
                    Yes
                </Button>
            </div>
        </Dialog>
    )
}

export default DeleteModal

