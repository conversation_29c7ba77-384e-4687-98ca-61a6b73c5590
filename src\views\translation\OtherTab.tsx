import { FormItem, Input } from '@/components/ui'
import React from 'react'
import { Controller } from 'react-hook-form'

const other: { label: string, value: string, db: string }[] = [
    {
        label: "Home",
        value: "Home",
        db: "home"
    },
    {
        label: "Category",
        value: "Category",
        db: "category"
    },
    {
        label: "Account",
        value: "Account",
        db: "account"
    },
    {
        label: "Cart",
        value: "Cart",
        db: "cart"
    },
    {
        label: "Store Locator",
        value: "StoreLocator",
        db: "storeLocator"
    },
    {
        label: "Store Locator Text",
        value: "StoreLocatorText",
        db: "storeLocatorText"
    },
    {
        label: "Share",
        value: "Share",
        db: "share"
    },
    {
        label: "Our brands",
        value: "OurBrands",
        db: "ourBrands"
    },
    {
        label: "Nothing More To Load",
        value: "NothingMoreToLoad",
        db: "nothingMoreToLoad"
    },
    {
        label: "Loading",
        value: "Loading",
        db: "loading"
    },
    {
        label: "Enquire With Us",
        value: "EnquireWithUs",
        db: "enquireWithUs"
    },
    {
        label: "Available Stores",
        value: "AvailableStores",
        db: "availableStores"
    },
    {
        label: "Download",
        value: "Download",
        db: "download"
    },
]


export const otherPage: { label: string, value: string, db: string }[] = [
    ...other
]

function OtherTab({ control, errors }: any) {
    return (
        <>
            <h3>Other</h3>
            {other.map((item) => (
                <Forms key={item.value} control={control} errors={errors} item={item} />
            ))}
        </>
    )
}

function Forms({ item, control, errors }: any) {
    return (
        <div className="mt-2">
            <div className="grid grid-cols-2 gap-4">
                <FormItem label={`${item.label} English`}>
                    <Controller
                        control={control}
                        name={`other${item.value}En`}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {errors[`other${item.value}En`] && (
                        <small className="text-red-600 py-3">
                            {' '}
                            {errors[`other${item.value}En`].message as string}{' '}
                        </small>
                    )}
                </FormItem>
                <FormItem label={`${item.label} Arabic`}>
                    <Controller
                        control={control}
                        name={`other${item.value}Ar`}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => <Input dir='rtl' type="text" {...field} />}
                    />
                    {errors[`other${item.value}Ar`] && (
                        <small className="text-red-600 py-3">
                            {' '}
                            {errors[`other${item.value}Ar`].message as string}{' '}
                        </small>
                    )}
                </FormItem>
            </div>
        </div>
    )
}

export default OtherTab