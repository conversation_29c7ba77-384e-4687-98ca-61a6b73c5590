/* eslint-disable */
import { Button, FormItem, Tag, toast } from '@/components/ui'
import endpoints from '@/endpoints'
import api from '@/services/api.interceptor'
import { Controller, useForm } from 'react-hook-form'
import { AiOutlineSave } from 'react-icons/ai'
import Notification from '@/components/ui/Notification'
import Input from '@/components/ui/Input'
import { useEffect, useState } from 'react'
import Breadcrumb from './modals/BreadCrumb'

export default function AdditionalFees() {

    const breadcrumbItems = [{ title: 'Additional Fees', url: '' }]

    useEffect(() => {
        api.get(endpoints.paymentMethodFees)
        .then((res) => {
            if (res?.status == 200 && res.data.result) {
                const data = res?.data?.result
                let values:any = {}
                data.forEach((method:any) => {
                    setValue(method.type, method.fee)
                });
            }
        })
        .catch((error) => {
            console.error('Error fetching data: ', error)
        })
    }, [])

    const {
        handleSubmit,
        control,
        setValue,
        formState: { errors },
    } = useForm<any>()

    const onSubmit = (values: any) => {
        const methods = Object.keys(values).map((type:any)=>({type, fee: Number(values[type])}))
        console.log(values)
        api.put(endpoints.updatePaymnetMethodFees, {methods})
        .then((res) => {
            if (res.status == 200) {
                toast.push(
                    <Notification
                        type="success"
                        title={res.data.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )
            }
        })
        .catch((err) => {
            toast.push(
                <Notification
                    type="warning"
                    title={err.response.data.message}
                />,
                {
                    placement: 'top-center',
                }
            )
        })
    }

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <h3 className="mb-2">Additional Fees</h3>
            <Breadcrumb items={breadcrumbItems} />
            <div className="grid grid-cols-2 gap-4">
                <FormItem label="Cash on Delivery Fee">
                    <Controller
                        control={control}
                        name="COD"
                        rules={{ required: 'Field Required', min: {value: 0, message: "Values cannot be less than 0" } }}
                        render={({ field }) => <Input type="number" {...field} />}
                    />
                    {errors.COD && (
                        <small className="text-red-600 py-3">
                            {' '}
                            {errors.COD.message as string}{' '}
                        </small>
                    )}
                </FormItem>
            </div>
            <div className="grid grid-cols-2 gap-4">
                <FormItem label="Card Fee">
                    <Controller
                        control={control}
                        name="creditCard"
                        rules={{ required: 'Field Required', min: {value: 0, message: "Values cannot be less than 0" } }}
                        render={({ field }) => <Input type="number" {...field} />}
                    />
                    {errors.creditCard && (
                        <small className="text-red-600 py-3">
                            {' '}
                            {errors.creditCard.message as string}{' '}
                        </small>
                    )}
                </FormItem>
            </div>
            <div className="grid grid-cols-2 gap-4">
                <FormItem label="VAT Percentage">
                    <Controller
                        control={control}
                        name="vat"
                        rules={{ required: 'Field Required', min: {value: 0, message: "Values cannot be less than 0" } }}
                        render={({ field }) => <Input type="number" {...field} />}
                    />
                    {errors.vat && (
                        <small className="text-red-600 py-3">
                            {' '}
                            {errors.vat.message as string}{' '}
                        </small>
                    )}
                </FormItem>
            </div>
            <Button
                className="float-right mt-4"
                variant="solid"
                type="submit"
                icon={<AiOutlineSave />}
            >
                Save
            </Button>
        </form>
    )
}
