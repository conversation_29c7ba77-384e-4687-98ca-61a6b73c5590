/* eslint-disable */
import { useState, useMemo, useEffect, InputHTMLAttributes } from 'react'
import Table from '@/components/ui/Table'
import Pagination from '@/components/ui/Pagination'
import Select from '@/components/ui/Select'
import {
    useReactTable,
    getCoreRowModel,
    getFilteredRowModel,
    getPaginationRowModel,
    flexRender,
    getSortedRowModel,
    getFacetedRowModel,
    getFacetedUniqueValues,
} from '@tanstack/react-table'
import type {
    ColumnDef,
    ColumnFiltersState,
    FilterFn,
} from '@tanstack/react-table'
import Input from '@/components/ui/Input'
import { rankItem } from '@tanstack/match-sorter-utils'
import Button from '@/components/ui/Button'
import { AiOutlinePlus, AiOutlineUpload } from 'react-icons/ai'
import api from '@/services/api.interceptor'
import endpoints from '@/endpoints'
import { Link, useParams, useLocation, useSearchParams } from 'react-router-dom'
import { HiPencilSquare } from 'react-icons/hi2'
import Tag from '@/components/ui/Tag'
import {
    MdDeleteOutline,
    MdOutlineFileDownload,
    MdOutlineRemoveRedEye,
} from 'react-icons/md'
import { Dialog, Drawer, FormItem, toast } from '@/components/ui'
import { IoIosAddCircleOutline } from 'react-icons/io'
import Breadcrumb from '@/views/modals/BreadCrumb'
import Bulk from './Bulk'
import Filter from '@/components/shared/TableFilter'
import { mkConfig, generateCsv, download } from 'export-to-csv'
import { CiFilter } from 'react-icons/ci'
import FilterSection from './FilterSection'
import Notification from '@/components/ui/Notification'
import DeleteModal from '@/views/modals/DeleteModal'
import TotalProductsCard from './TotalCount'
import { FaLayerGroup } from 'react-icons/fa'

interface DebouncedInputProps
    extends Omit<
        InputHTMLAttributes<HTMLInputElement>,
        'onChange' | 'size' | 'prefix'
    > {
    value: string | number
    onChange: (value: string | number) => void
    debounce?: number
}

const csvConfig = mkConfig({
    fieldSeparator: ',',
    decimalSeparator: '.',
    useKeysAsHeaders: true,
})

const { Tr, Th, Td, THead, TBody, Sorter } = Table
const imageUrl = import.meta.env.VITE_ASSET_URL

function DebouncedInput({
    value: initialValue,
    onChange,
    debounce = 500,
    ...props
}: DebouncedInputProps) {
    const [value, setValue] = useState(initialValue)

    useEffect(() => {
        setValue(initialValue)
    }, [initialValue])

    useEffect(() => {
        const timeout = setTimeout(() => {
            onChange(value)
        }, debounce)

        return () => clearTimeout(timeout)
    }, [value])

    return (
        <div className="flex justify-end">
            <div className="flex items-center mb-4">
                <Input
                    {...props}
                    value={value}
                    onChange={(e) => setValue(e.target.value)}
                />
            </div>
        </div>
    )
}

type Option = {
    value: number
    label: string
}

const pageSizeOption = [
    { value: 10, label: '10 / page' },
    { value: 20, label: '20 / page' },
    { value: 30, label: '30 / page' },
    { value: 40, label: '40 / page' },
    { value: 50, label: '50 / page' },
]

const breadcrumbItems = [{ title: 'Products', url: '' }]

const Products = () => {
    const [showImportDialog, setShowImportDialog] = useState(false)
    const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
    const [globalFilter, setGlobalFilter] = useState('')
    const [filterOpen, setFilterOpen] = useState(false)
    const [selectedCategories, setSelectedCategories] = useState([])
    const [selectedFrameTypes, setSelectedFrameTypes] = useState([])
    const [selectedFrameShapes, setSelectedFrameShapes] = useState([])
    const [selectedSizes, setSelectedSizes] = useState([])
    const [selectedFrontMaterials, setSelectedFrontMaterials] = useState([])
    const [selectedColors, setSelectedColors] = useState([])
    const [selectedType, setSelectedType] = useState([])
    const [selectedLensMaterials, setSelectedLensMaterials] = useState([])
    const [deleteModalOpen, setDeleteModalOpen] = useState(false)
    const [itemToDelete, setItemToDelete] = useState(null)
    const [productHead, setProductHead] = useState<any>("")

    const [params] = useSearchParams()
    const location = useLocation()

    const openDeleteModal = (refid: any) => {
        setDeleteModalOpen(true)
        setItemToDelete(refid)
    }

    const closeDeleteModal = () => {
        setDeleteModalOpen(false)
    }

    const confirmDelete = () => {
        api.put(endpoints.deleteProduct + itemToDelete)
            .then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification
                            type="success"
                            title={res.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    closeDeleteModal()
                    getProducts()
                }
            })
            .catch((err) => {
                toast.push(
                    <Notification
                        type="warning"
                        title={err.response.data.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )
                closeDeleteModal()
            })
    }

    const openDrawer = () => {
        setFilterOpen(true)
    }

    const onDrawerClose = () => {
        getProducts()
        setFilterOpen(false)
    }

    const [totalData, setTotalData] = useState(0)
    const [page, setPage] = useState(params.get("page") ?? 1)
    const [limit, setLimit] = useState(params.get("limit") ?? 10)

    const [exportLoading, setExportLoading] = useState(false)
  

    const columns = useMemo<ColumnDef<any>[]>(
        () => [
            {
                header: 'Sl No.',
                accessorKey: 'slNo',
                cell: (info) => info.row.index + 1 + ((Number(page) - 1) * (Number(limit))),
                enableColumnFilter: false,
            },
            {
                header: 'SKU',
                accessorKey: 'sku',
                enableSorting: false,
            },
            {
                header: 'Name',
                accessorKey: 'name.en',
                enableSorting: false,
            },
            {
                header: 'Brand',
                accessorKey: 'brand.name.en',
                enableSorting: false,
            },
            {
                header: 'Category',
                accessorKey: 'category',
                cell: (info) => {
                    const categories = info.getValue() as any[]
                    const categoryNames = categories
                        .map((cat) => cat.name.en)
                        .join(', ')
                    return <div>{categoryNames}</div>
                },
                enableColumnFilter: false,
                enableSorting: false,
            },
            {
                header: 'Sub Category',
                accessorKey: 'subCategory',
                cell: (info) => {
                    const categories = info.getValue() as any[]
                    const categoryNames = categories
                        .map((cat) => cat.name.en)
                        .join(', ')
                    return <div>{categoryNames}</div>
                },
                enableColumnFilter: false,
                enableSorting: false,
            },
            // {
            //     header: 'Variants',
            //     accessorKey: 'refid',
            //     cell: (info) => {
            //         const isCustomizable = info.row.original.customizable
            //         const isDefault = info.row.original.isDefaultVariant
            //         const main = info.row.original.mainProduct
            //         if (isCustomizable) {
            //             if (isDefault) {
            //                 return (
            //                     <div className="flex gap-2">
            //                         <Link
            //                             to={`/products/variants/${info.getValue()}`}
            //                         >
            //                             <MdOutlineRemoveRedEye size={25} />
            //                         </Link>
            //                         <Link
            //                             to={`/catalog/manage-products/Add-Variant/${info.getValue()}`}
            //                         >
            //                             <IoIosAddCircleOutline size={25} />
            //                         </Link>
            //                     </div>
            //                 )
            //             } else {
            //                 return (
            //                 <div className='flex flex-col items-center'>
            //                     <Link
            //                         title='View Parent product & variants'
            //                         to={`/products/variants/${main?.refid}`}
            //                     >
            //                         <MdOutlineRemoveRedEye size={25} />
            //                     </Link>
            //                 </div>
            //                 )
            //             }
            //         } else {
            //             return <span>Not Customizable</span>
            //         }
            //     },
            //     enableColumnFilter: false,
            //     enableSorting: false,
            // },
            // {
            //     header: 'Stock',
            //     accessorKey: 'stock',
            //     enableColumnFilter: false,
            //     enableSorting: false,
            // },
            // {
            //     header: 'Supplier SKU',
            //     accessorKey: 'supplierSku',
            //     enableSorting: false,
            // },
            // {
            //     header: 'UPC',
            //     accessorKey: 'upc',
            //     enableSorting: false,
            // },
            // {
            //     header: 'Position',
            //     accessorKey: 'position',
            //     enableSorting: true,
            // },
            {
                header: 'Status',
                accessorKey: 'isActive',
                cell: (info) => {
                    const isActive = info.getValue()
                    return (
                        <div>
                            <Tag
                                className={
                                    isActive
                                        ? 'bg-emerald-100 text-emerald-600 dark:bg-emerald-500/20 dark:text-emerald-100 border-0 rounded'
                                        : 'text-red-600 bg-red-100 dark:text-red-100 dark:bg-red-500/20 border-0 rounded'
                                }
                            >
                                {isActive ? 'Active' : 'Inactive'}
                            </Tag>
                        </div>
                    )
                },
                enableColumnFilter: false,
                enableSorting: false,
            },
            {
                header: 'Actions',
                accessorKey: 'refid',
                cell: (info) => {
                    return (
                        <div className="flex gap-4">
                            <Link
                                to={`/catalog/manage-products/Edit-Product/${info.getValue()}`}
                            >
                                <HiPencilSquare size={25} />
                            </Link>
                            <Link
                                className={`inline-flex items-center ${info.row.original?.parent?.refid ? '' : 'cursor-not-allowed opacity-50'}`}
                                title='Go to Product Head'
                                to={`/catalog/manage-product-head/Edit-Product/${info.row.original?.parent?.refid}`}
                            >
                                <FaLayerGroup size={20} />
                            </Link>
                            <div
                                onClick={() => openDeleteModal(info.getValue())}
                            >
                                <MdDeleteOutline size={25} />
                            </div>
                        </div>
                    )
                },
                enableColumnFilter: false,
                enableSorting: false,
            },
        ],
        [limit, page]
    )

    const [data, setData] = useState<any[]>([])

      console.log(data)

    const toggleImportDialog = () => {
        setShowImportDialog((prev) => !prev)
    }

    const fuzzyFilter: FilterFn<any> = (row, columnId, value, addMeta) => {
        const itemRank = rankItem(row.getValue(columnId), value)

        addMeta({
            itemRank,
        })
        return itemRank.passed
    }

    const table = useReactTable({
        data,
        initialState: {
            pagination: {
                // pageIndex: Number(page),
                pageSize: Number(limit)
            }
        },
        columns,
        filterFns: {
            fuzzy: fuzzyFilter,

        },
        state: {
            columnFilters,
            // globalFilter,
        },
        onColumnFiltersChange: setColumnFilters,
        // onGlobalFilterChange: setGlobalFilter,
        // globalFilterFn: fuzzyFilter,
        getSortedRowModel: getSortedRowModel(),
        getCoreRowModel: getCoreRowModel(),
        getFilteredRowModel: getFilteredRowModel(),
        getPaginationRowModel: getPaginationRowModel(),
        getFacetedRowModel: getFacetedRowModel(),
        getFacetedUniqueValues: getFacetedUniqueValues(),
    })

    const onPaginationChange = (page: number) => {
        table.setPageIndex(page - 1)
        setPage(page)
    }

    const onSelectChange = (value = 0) => {
        table.setPageSize(Number(value))
    }

    const handleApplyFilter = () => {
        onDrawerClose()
    }

    const handleClearFilter = () => {
        setSelectedCategories([])
        setSelectedFrameTypes([])
        setSelectedFrameShapes([])
        setSelectedSizes([])
        setSelectedFrontMaterials([])
        setSelectedColors([])
        setSelectedType([])
        setSelectedLensMaterials([])
    }

    const getProducts = (controller?: any) => {
        const query = {
            // isDefaultVariant: true,
            // productType: 'frame',
            category: selectedCategories.map((category: any) => category.value),
            frameType: selectedFrameTypes.map(
                (frameType: any) => frameType.value
            ),
            frameShape: selectedFrameShapes.map(
                (frameShape: any) => frameShape.value
            ),
            size: selectedSizes.map((size: any) => size.value),
            frontMaterial: selectedFrontMaterials.map(
                (frontMaterial: any) => frontMaterial.value
            ),
            color: selectedColors.map((color: any) => color.value),
            type: selectedType.map((type: any) => type.value),
            lensMaterial: selectedLensMaterials.map(
                (lensMaterial: any) => lensMaterial.value
            ),
            keyword: globalFilter,
            productHead: productHead
        }

        api.post(endpoints.products + `?page=${page}&limit=${limit}`, query, {
            signal: controller?.signal
        })
            .then((res) => {
                setData(res?.data?.result?.products)
                setTotalData(res?.data?.result?.total)
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
            })
    }

    useEffect(() => {
        const controller = new AbortController();
        getProducts(controller)
        const newParams = new URLSearchParams(params.toString());
        if (!newParams.get("page")) {
            newParams.append("page", `${page}`)
        } else {
            newParams.delete("page")
            newParams.append("page", `${page}`)
        }
        if (!newParams.get("limit")) {
            newParams.append("limit", `${limit}`)
        } else {
            newParams.delete("limit")
            newParams.append("limit", `${limit}`)
        }
        window.history.pushState(null, '', `${location.pathname}?${newParams.toString()}`)

        return () => {
            controller.abort()
        }
    }, [page, limit, globalFilter, productHead])

    // useEffect(() => {
    //     const filteredData = data.filter((item) => {
    //         // Implement your search logic here. This is a simple example.
    //         // You might need to adjust this based on your actual search criteria.
    //         return item.name.en
    //             .toLowerCase()
    //             .includes(globalFilter.toLowerCase())
    //     })
    //     // setTotalData(filteredData.length)
    // }, [data, globalFilter])

    interface Category {
        _id: string;
        name: {
            en: string;
        };
        parent?: string; // Optional because top-level categories don't have parentId
    }

    function getAllHierarchies(categories: Category[]): any {
        // Create a map for quick lookup by id
        const categoryMap = new Map<string, Category>();
        categories.forEach(category => categoryMap.set(category._id, category));

        // Create a set to track processed categories
        const processedCategories = new Set<string>();

        // Recursive function to build hierarchy string
        function buildHierarchy(subcategoryId: string): string {
            const category = categoryMap.get(subcategoryId);
            if (!category) return '';

            let hierarchy = category.name?.en;

            if (category.parent) {
                const parentHierarchy = buildHierarchy(category.parent);
                if (parentHierarchy) {
                    hierarchy = `${parentHierarchy}>${hierarchy}`;
                }
            } else {
                hierarchy = category.name?.en
            }

            return hierarchy;
        }

        // Find all root categories and build hierarchies
        const hierarchies = new Set<string>();

        categories.forEach((category: any) => {
            if (category.isRoot) {
                // If it has no parentId, it is a root category
                // Traverse all its descendants
                const queue: string[] = [category._id];

                while (queue.length > 0) {
                    const currentId = queue.shift()!;
                    const currentCategory = categoryMap.get(currentId);
                    if (currentCategory && !processedCategories.has(currentId)) {
                        // Process this category
                        processedCategories.add(currentId);
                        const hierarchy = buildHierarchy(currentId);
                        hierarchies.add(hierarchy);

                        // Add children to the queue
                        categories.forEach((cat: any) => {
                            if (cat.parent === currentId) {
                                queue.push(cat._id);
                            }
                        });
                    }
                }
            }
        });

        return Array.from(hierarchies)
    }

    function removeSubstrings(arr: any) {
        // Sort array by length, longest first, to ensure that substrings are removed first
        arr.sort((a: any, b: any) => b.length - a.length);

        // Create a new array to hold the unique strings
        const uniqueArr: any = [...arr];

        // Iterate over each string in the array
        for (let i = 0; i < arr.length; i++) {
            let isSubstring = false;
            let j = 0
            // Check if the current string is a substring of any string already in uniqueArr
            for (j = 0; j < uniqueArr.length; j++) {
                if (arr[i].includes(uniqueArr[j]) && uniqueArr[j] !== arr[i]) {
                    isSubstring = true;
                    break;
                }
            }

            // If the string is not a substring, add it to the uniqueArr
            if (isSubstring) {
                uniqueArr.splice(j, 1)
            }
        }

        return uniqueArr;
    }


    const handleExportRows = (rows: any) => {
        // const rowData = rows
        //     .map((row: any) => {
        //         const strings = getAllHierarchies([...row.original?.category, ...row.original?.subCategory])
        //         const result = removeSubstrings(strings);
        //         let sphValues, cylValues, axisValues, addValues;
        //         if (row.original.productType == "frame") {
        //             sphValues = row.original?.sphValues?.map((power: any) => power?.name).join(",");
        //             cylValues = row.original?.cylValues?.map((power: any) => power?.name).join(",");
        //             axisValues = row.original?.axisValues?.map((power: any) => power?.name).join(",");
        //             addValues = row.original?.addValues?.map((power: any) => power?.name).join(",");
        //         } else {
        //             sphValues = row.original?.contactSph?.map((power: any) => power?.name).join(",");
        //             cylValues = row.original?.contactCyl?.map((power: any) => power?.name).join(",");
        //             axisValues = row.original?.contactAxis?.map((power: any) => power?.name).join(",");
        //         }
        //         return {
        //             slug: row.original.slug || "",
        //             sku: row.original.sku || "",
        //             name_en: row.original.name.en || "",
        //             name_ar: row.original.name.ar || "",
        //             brand: row.original.brand.name.en || "",
        //             categories: result.join(','),
        //             product_type: row.original.productType || "",
        //             description_en: row.original?.description?.en || "",
        //             description_ar: row.original?.description?.ar || "",
        //             description_images: row.original?.descriptionImages?.map((item: any) => item.split("/")[2])?.join(","),
        //             price: row.original?.price?.aed || "",
        //             offer_price: row.original?.offerPrice?.aed || "",
        //             thumb_image: row.original?.thumbnail?.split("/")?.[2],
        //             product_images: row.original?.images?.map((item: any) => item.split("/")?.[2])?.join(","),
        //             colour: row.original?.color?.name?.en || "",
        //             colour_value: row.original?.color?.color?.join(","),
        //             stock: row.original.stock || "",
        //             frame_shape: row.original?.frameShape?.name?.en || "",
        //             frame_type: row.original?.frameType?.name?.en || "",
        //             is_new_arrival: row.original.isNewArrival ? 1 : 0,
        //             is_returnable: row.original.isReturnable ? 1 : 0,
        //             label: row.original.label?.name?.en || "",
        //             virtual_try_available: row.original.isVirtualTry ? 1 : 0,
        //             is_active: row.original.isActive ? 1 : 0,
        //             is_default_variant: row.original.isDefaultVariant ? 1 : 0,
        //             main_product: row.original.mainProduct?.sku || "",
        //             variants: row.original.variants
        //                 .map((item: any) => item.sku)
        //                 .join(','),
        //             supplier_sku: row.original?.supplierSku || "",
        //             upc: row.original?.upc || "",
        //             sph: row.original?.sph ? "TRUE" : "",
        //             cyl: row.original?.cyl ? "TRUE" : "",
        //             axis: row.original?.axis ? "TRUE" : "",
        //             sphValues: sphValues || "",
        //             cylValues: cylValues || "",
        //             axisValues: axisValues || "",
        //             addValues: addValues || "",
        //             power_price: row.original?.powerPrice || "",
        //             front_material: row.original?.frontMaterial?.map((item: any) => item?.name?.en)?.join(","),
        //             lens_material: row.original?.lensMaterial?.map((item: any) => item?.name?.en)?.join(","),
        //             type: row.original?.type?.map((item: any) => item?.name?.en)?.join(","),
        //             gender: row.original?.gender?.join(","),
        //             weight: row.original?.weight || "",
        //             recommended_products: row.original.recommendedProducts?.map((item: any) => item.sku)?.join(','),
        //             bought_together: row.original.boughtTogether?.map((item: any) => item.sku)?.join(','),
        //             add_to_cart: row.original.isAddToCart ? 1 : 0,
        //             choose_lens: row.original.isChooseLens ? 1 : 0,
        //             position: row.original?.position || "",
        //             multi_focal: row.original.multiFocal ? "TRUE" : "",
        //             show_discount_percentage: row.original.showDiscountPercentage ? 1 : 0,
        //             isTaxIncluded: row.original.isTaxIncluded ? 1 : 0,
        //             isCashbackEnabled: row.original.isCashbackEnabled ? 1 : 0,
        //             cashbackPercentage: row.original.cashbackPercentage ?? 0
        //         }
        //     });
        // for (const i in rows) {
        //     // console.log(rows?.[i]?.original?.sizes)
        //     for (let [j, size] of rows?.[i]?.original?.sizes.entries()) {
        //         rowData[i][`size_${j + 1}`] = size?.size?.name || ""
        //         rowData[i][`stock_${j + 1}`] = size?.stock || ""
        //         rowData[i][`price_${j + 1}`] = size?.price || ""
        //         rowData[i][`offer_price_${j + 1}`] = size?.offerPrice || ""

        //         // The first column need to be defined for all possible columns
        //         if (!rowData[0][`size_${j + 1}`]) rowData[0][`size_${j + 1}`] = ""
        //         if (!rowData[0][`stock_${j + 1}`]) rowData[0][`stock_${j + 1}`] = ""
        //         if (!rowData[0][`price_${j + 1}`]) rowData[0][`price_${j + 1}`] = ""
        //         if (!rowData[0][`offer_price_${j + 1}`]) rowData[0][`offer_price_${j + 1}`] = ""
        //     }
        // }

        // // for (const i in rows) {
        // //     rows?.[i]?.original.technicalInfo.forEach((info: any, j: number) => {
        // //         rowData[i][`t${j + 1}_en`] = info?.title?.en
        // //         rowData[i][`t${j + 1}_ar`] = info?.title?.ar
        // //         rowData[i][`d${j + 1}_en`] = info?.description?.en
        // //         rowData[i][`d${j + 1}_ar`] = info?.description?.ar
        // //     })
        // // }
        // const csv = generateCsv(csvConfig)(rowData)
        // // console.log(csv)
        // download(csvConfig)(csv)
        setExportLoading(true)
        api.get(endpoints.exportProducts)
            .then((res) => {
                const link = document.createElement('a')
                link.href = `data:text/csv;charset=utf-8,${escape(res.data)}`
                link.setAttribute('download', "products.csv")
                document.body.appendChild(link)
                link.click()
                link.remove()
                setExportLoading(false)
            })
            .catch((error) => {
                console.error('Error fetching data: ', error)
                setExportLoading(false)
            })

    }

    const onSearch = (value: any) => {
        setGlobalFilter(String(value))
        if (value) {
            setPage(1)
        }
    }

    return (
        <div>
            <div className="mb-4 flex">
                <h2>Products</h2>

                <Link
                    className="mr-2 mb-2 ml-auto"
                    to="/catalog/manage-products/Add-Product"
                >
                    <Button
                        variant="twoTone"
                        color="green-600"
                        icon={<AiOutlinePlus />}
                    >
                        Add Product
                    </Button>
                </Link>
                <Button
                    variant="twoTone"
                    color="indigo-600"
                    className="mr-2 mb-2"
                    //export all data that is currently in the table (ignore pagination, sorting, filtering, etc.)
                    onClick={() =>
                        handleExportRows(table.getPrePaginationRowModel().rows)
                    }
                    loading={exportLoading}
                    icon={<MdOutlineFileDownload />}
                >
                    Export Products
                </Button>
                <Button
                    className="mr-2 mb-2"
                    onClick={toggleImportDialog}
                    variant="twoTone"
                    color="blue-600"
                    icon={<AiOutlineUpload />}
                >
                    Import Products
                </Button>
                <Button
                    variant="twoTone"
                    color="red-600"
                    icon={<CiFilter />}
                    onClick={openDrawer}
                >
                    Filters
                </Button>
            </div>

            <Breadcrumb items={breadcrumbItems} />

            <div className="grid grid-cols-3 gap-4">
                <TotalProductsCard count={totalData} />
            </div>

            <div className="mb-4 flex space-x-2 justify-end">
                <FormItem label="Product Head" className='mb-8'>
                    <Select
                        className='w-[200px]'
                        placeholder=""
                        options={[
                            {
                                value: "all",
                                label: "All",
                            },
                            {
                                value: "true",
                                label: "With Product Head"
                            },
                            {
                                value: "false",
                                label: "Without Product Head"
                            },
                        ]}
                        defaultValue={{
                            value: "all",
                            label: "All"
                        }}
                        onChange={(option: any) => setProductHead(option.value === "true" ? true : option.value === "false" ? false : "all")}
                    />
                </FormItem>
                <DebouncedInput
                    value={globalFilter ?? ''}
                    className="p-2 font-lg shadow border border-block"
                    placeholder="Search all columns..."
                    onChange={onSearch}
                />
            </div>
            <Table style={{ maxWidth: "600px" }}>
                <THead>
                    {table.getHeaderGroups().map((headerGroup) => (
                        <Tr key={headerGroup.id}>
                            {headerGroup.headers.map((header) => {
                                return (
                                    <Th
                                        key={header.id}
                                        colSpan={header.colSpan}
                                    >
                                        <div
                                            {...{
                                                className:
                                                    header.column.getCanSort()
                                                        ? 'cursor-pointer select-none'
                                                        : '',
                                                onClick:
                                                    header.column.getToggleSortingHandler(),
                                            }}
                                        >
                                            {flexRender(
                                                header.column.columnDef.header,
                                                header.getContext()
                                            )}
                                            {
                                                // <Sorter
                                                //     sort={header.column.getIsSorted()}
                                                // />
                                            }
                                        </div>
                                        {/* <div>
                                            {header.column.getCanFilter() ? (
                                                <div>
                                                    <Filter
                                                        column={header.column}
                                                        table={table}
                                                    />
                                                </div>
                                            ) : null}
                                        </div> */}
                                    </Th>
                                )
                            })}
                        </Tr>
                    ))}
                </THead>
                <TBody>
                    {table.getRowModel().rows.map((row) => {
                        return (
                            <Tr key={row.id}>
                                {row.getVisibleCells().map((cell) => {
                                    return (
                                        <Td key={cell.id}>
                                            {flexRender(
                                                cell.column.columnDef.cell,
                                                cell.getContext()
                                            )}
                                        </Td>
                                    )
                                })}
                            </Tr>
                        )
                    })}
                </TBody>
            </Table>
            <div className="flex items-center justify-between mt-4">
                <Pagination
                    pageSize={Number(limit)}
                    currentPage={Number(page)}
                    total={totalData}
                    onChange={onPaginationChange}
                />
                <div style={{ minWidth: 130 }}>
                    <Select<Option>
                        size="sm"
                        isSearchable={false}
                        value={pageSizeOption.filter(
                            (option) =>
                                option.value ===
                                Number(limit)
                        )}
                        options={pageSizeOption}
                        onChange={(option) => {
                            onSelectChange(option?.value)
                            setLimit(option?.value ?? 10)
                        }}
                    />
                </div>
            </div>

            <Dialog isOpen={showImportDialog} onClose={toggleImportDialog}>
                <Bulk
                    onClose={toggleImportDialog}
                    getProducts={getProducts}
                    type="Products"
                />
            </Dialog>

            <Drawer
                title="Filters"
                isOpen={filterOpen}
                onClose={onDrawerClose}
                onRequestClose={onDrawerClose}
            >
                <FilterSection
                    selectedCategories={selectedCategories}
                    setSelectedCategories={setSelectedCategories}
                    selectedFrameTypes={selectedFrameTypes}
                    setSelectedFrameTypes={setSelectedFrameTypes}
                    selectedFrameShapes={selectedFrameShapes}
                    setSelectedFrameShapes={setSelectedFrameShapes}
                    selectedSizes={selectedSizes}
                    setSelectedSizes={setSelectedSizes}
                    selectedFrontMaterials={selectedFrontMaterials}
                    setSelectedFrontMaterials={setSelectedFrontMaterials}
                    selectedColors={selectedColors}
                    setSelectedColors={setSelectedColors}
                    selectedType={selectedType}
                    setSelectedType={setSelectedType}
                    selectedLensMaterials={selectedLensMaterials}
                    setSelectedLensMaterials={setSelectedLensMaterials}
                    onApplyFilter={handleApplyFilter}
                    onClearFilter={handleClearFilter}
                />
            </Drawer>

            <DeleteModal
                isOpen={deleteModalOpen}
                title="Delete Product"
                content="Are you sure you want to delete this product?"
                onClose={closeDeleteModal}
                onConfirm={confirmDelete}
            />
        </div>
    )
}

export default Products
