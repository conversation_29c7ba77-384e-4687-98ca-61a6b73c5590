/* eslint-disable */
import { Button, FormItem, toast, Upload } from '@/components/ui'
import endpoints from '@/endpoints'
import api from '@/services/api.interceptor'
import { Controller, useForm } from 'react-hook-form'
import { AiOutlineSave } from 'react-icons/ai'
import Notification from '@/components/ui/Notification'
import { useNavigate } from 'react-router-dom'
import Input from '@/components/ui/Input'
import { useEffect, useState } from 'react'
import Breadcrumb from '../modals/BreadCrumb'

export default function ContactBanner() {
    const [isUpdate, setIsUpdate] = useState(false);
    const [formSubmitted, setFormSubmitted] = useState(false);
    const [imageFile, setImageFile] = useState<any>([])
    const [ogImageFile, setOgImageFile] = useState<any>([])
    const [imageError, setImageError] = useState<any>(null);
    const [ogImageError, setOgImageError] = useState<any>(null);
    const navigate = useNavigate()

    const imageUrl = import.meta.env.VITE_ASSET_URL

    const breadcrumbItems = [
        { title: 'Contact Us Page', url: '' },
    ]

    useEffect(() => {
        api.get(endpoints.contactBanner).then((res) => {
            if (res?.status == 200) {
                if (res?.data && res?.data?.result) {
                    const data = res?.data?.result
                    console.log(data, "contact banner data")
                    setValue('pageTitleEn', data.pageTitle?.en)
                    setValue('pageTitleAr', data.pageTitle?.ar)
                    setValue('formTitleEn', data.formTitle?.en)
                    setValue('formTitleAr', data.formTitle?.ar)
                    setValue('titleEn', data.title?.en)
                    setValue('titleAr', data.title?.ar)
                    setValue('descriptionEn', data.description?.en)
                    setValue('descriptionAr', data.description?.ar)
                    setValue('buttonTextEn', data.buttonText?.en)
                    setValue('buttonTextAr', data.buttonText?.ar)
                    setValue('link', data.link)
                    if (data?.image) {
                        setImageFile([data.image])
                    }
                    setValue('officeHoursTitleEn', data?.officeHoursTitle?.en)
                    setValue('officeHoursTitleAr', data?.officeHoursTitle?.ar)
                    setValue('officeHoursEn', data?.officeHours?.en)
                    setValue('officeHoursAr', data?.officeHours?.ar)
                    setValue('storeOneTitleEn', data?.storeOneTitle?.en)
                    setValue('storeOneTitleAr', data?.storeOneTitle?.ar)
                    setValue('storeOneAddressEn', data?.storeOneAddress?.en)
                    setValue('storeOneAddressAr', data?.storeOneAddress?.ar)
                    setValue('storeTwoTitleEn', data?.storeTwoTitle?.en)
                    setValue('storeTwoTitleAr', data?.storeTwoTitle?.ar)
                    setValue('storeTwoAddressEn', data?.storeTwoAddress?.en)
                    setValue('storeTwoAddressAr', data?.storeTwoAddress?.ar)
                    setValue('metaTitleEn', data?.seoDetails?.title?.en)
                    setValue('metaTitleAr', data?.seoDetails?.title?.ar)
                    setValue('metaDescriptionEn', data?.seoDetails?.description?.en)
                    setValue('metaDescriptionAr', data?.seoDetails?.description?.ar)
                    setValue('metaKeywordsEn', data?.seoDetails?.keywords?.en)
                    setValue('metaKeywordsAr', data?.seoDetails?.keywords?.ar)
                    setValue('metaCanonicalUrl', data?.seoDetails?.canonical?.en)
                    setValue('metaCanonicalUrlAr', data?.seoDetails?.canonical?.ar)
                    if (data?.seoDetails?.ogImage) {
                        setOgImageFile([imageUrl + data.seoDetails.ogImage])
                    }
                    setIsUpdate(true)
                }
            }
        }).catch((error) => {
            console.error('Error fetching data: ', error)
        })
    }, [formSubmitted])


    const handleImageUpload = (files: any) => {
        if (files && files.length > 0) {
            setImageFile(files)
        }
    }

    const handleOgImageUpload = (files: any) => {
        if (files && files.length > 0) {
            setOgImageFile(files)
        }
    }

    const {
        handleSubmit,
        control,
        setValue,
        formState: { errors },
    } = useForm<any>()

    const onSubmit = (value: any) => {
        if (imageFile.length == 0) {
            setImageError('Image is required')
            return;
        }
        if (ogImageFile.length == 0) {
            setOgImageError('OG Image is required')
            return;
        }
        setImageError(null)
        setOgImageError(null)

        const formData = new FormData()
        formData.append('pageTitle[en]', value.pageTitleEn)
        formData.append('pageTitle[ar]', value.pageTitleAr)
        formData.append('formTitle[en]', value.formTitleEn)
        formData.append('formTitle[ar]', value.formTitleAr)
        formData.append('title[en]', value.titleEn)
        formData.append('title[ar]', value.titleAr)
        formData.append('description[en]', value.descriptionEn)
        formData.append('description[ar]', value.descriptionAr)
        formData.append('buttonText[en]', value.buttonTextEn)
        formData.append('buttonText[ar]', value.buttonTextAr)
        formData.append('link', value.link)
        formData.append('officeHoursTitle[en]', value.officeHoursTitleEn)
        formData.append('officeHoursTitle[ar]', value.officeHoursTitleAr)
        formData.append('officeHours[en]', value.officeHoursEn)
        formData.append('officeHours[ar]', value.officeHoursAr)
        formData.append('storeOneTitle[en]', value.storeOneTitleEn)
        formData.append('storeOneTitle[ar]', value.storeOneTitleAr)
        formData.append('storeOneAddress[en]', value.storeOneAddressEn)
        formData.append('storeOneAddress[ar]', value.storeOneAddressAr)
        formData.append('storeTwoTitle[en]', value.storeTwoTitleEn)
        formData.append('storeTwoTitle[ar]', value.storeTwoTitleAr)
        formData.append('storeTwoAddress[en]', value.storeTwoAddressEn)
        formData.append('storeTwoAddress[ar]', value.storeTwoAddressAr)
        formData.append('seoDetails[title][en]', value.metaTitleEn)
        formData.append('seoDetails[title][ar]', value.metaTitleAr)
        formData.append('seoDetails[description][en]', value.metaDescriptionEn)
        formData.append('seoDetails[description][ar]', value.metaDescriptionAr)
        formData.append('seoDetails[keywords][en]', value.metaKeywordsEn)
        formData.append('seoDetails[keywords][ar]', value.metaKeywordsAr)
        formData.append('seoDetails[canonical][en]', value.metaCanonicalUrl)
        formData.append('seoDetails[canonical][ar]', value.metaCanonicalUrlAr)
        
        if (imageFile.length > 0) formData.append('image', imageFile[0])
        if (ogImageFile.length > 0) formData.append('ogImage', ogImageFile[0])

        if (isUpdate) {
            api.post(endpoints.updateContactBanner, formData).then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification
                            type="success"
                            title={res.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    navigate('/contact-banner')
                    setFormSubmitted(true);
                }
            }).catch((err) => {
                console.log(err)
            })
        }
        else {
            api.post(endpoints.addContactBanner, formData)
                .then((res) => {
                    if (res.status == 200) {
                        toast.push(
                            <Notification
                                type="success"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                        navigate('/contact-banner')
                        setFormSubmitted(true);
                    }
                })
                .catch((err) => {
                    console.log(err)
                })
        }
    }

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <h3 className="mb-6">Contact Us Page</h3>
            <Breadcrumb items={breadcrumbItems} />
            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Page Title English">
                    <Controller
                        name="pageTitleEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Title is required' }}
                        render={({ field }) => (
                            <input
                                type="text"
                                className={`${errors.pageTitleEn
                                    ? ' input input-md h-11 input-invalid'
                                    : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                    }`}
                                {...field}
                            />
                        )}
                    />
                    {errors.pageTitleEn && (
                        <small className="text-red-600 py-3">
                            {errors.pageTitleEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Page Title Arabic">
                    <Controller
                        name="pageTitleAr"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Title is required' }}
                        render={({ field }) => (
                            <input
                                dir='rtl'
                                type="text"
                                className={`${errors.pageTitleAr
                                    ? ' input input-md h-11 input-invalid'
                                    : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                    }`}
                                {...field}
                            />
                        )}
                    />
                    {errors.pageTitleAr && (
                        <small className="text-red-600 py-3">
                            {errors.pageTitleAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>
            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Form Title English">
                    <Controller
                        name="formTitleEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Title is required' }}
                        render={({ field }) => (
                            <input
                                type="text"
                                className={`${errors.formTitleEn
                                    ? ' input input-md h-11 input-invalid'
                                    : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                    }`}
                                {...field}
                            />
                        )}
                    />
                    {errors.formTitleEn && (
                        <small className="text-red-600 py-3">
                            {errors.formTitleEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Form Title Arabic">
                    <Controller
                        name="formTitleAr"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Title is required' }}
                        render={({ field }) => (
                            <input
                                dir='rtl'
                                type="text"
                                className={`${errors.formTitleAr
                                    ? ' input input-md h-11 input-invalid'
                                    : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                    }`}
                                {...field}
                            />
                        )}
                    />
                    {errors.formTitleAr && (
                        <small className="text-red-600 py-3">
                            {errors.formTitleAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>
            <h5 className='mb-2'>Image Section</h5>
            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Title English">
                    <Controller
                        name="titleEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Title is required' }}
                        render={({ field }) => (
                            <input
                                type="text"
                                className={`${errors.titleEn
                                    ? ' input input-md h-11 input-invalid'
                                    : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                    }`}
                                {...field}
                            />
                        )}
                    />
                    {errors.titleEn && (
                        <small className="text-red-600 py-3">
                            {errors.titleEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Title Arabic">
                    <Controller
                        name="titleAr"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Title is required' }}
                        render={({ field }) => (
                            <input
                                dir='rtl'
                                type="text"
                                className={`${errors.titleAr
                                    ? ' input input-md h-11 input-invalid'
                                    : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                    }`}
                                {...field}
                            />
                        )}
                    />
                    {errors.titleAr && (
                        <small className="text-red-600 py-3">
                            {errors.titleAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div>
                <FormItem label="Description English">
                    <Controller
                        name="descriptionEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'description is required' }}
                        render={({ field }) => (
                            <input
                                type="text"
                                className={`${errors.descriptionEn
                                    ? ' input input-md h-11 input-invalid'
                                    : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                    }`}
                                {...field}
                            />
                        )}
                    />
                    {errors.descriptionEn && (
                        <small className="text-red-600 py-3">
                            {errors.descriptionEn.message as string}
                        </small>
                    )}
                </FormItem>
            </div>
            <div>
                <FormItem label="Description Arabic">
                    <Controller
                        name="descriptionAr"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'description is required' }}
                        render={({ field }) => (
                            <input
                                type="text"
                                dir='rtl'
                                className={`${errors.descriptionAr
                                    ? ' input input-md h-11 input-invalid'
                                    : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                    }`}
                                {...field}
                            />
                        )}
                    />
                    {errors.descriptionAr && (
                        <small className="text-red-600 py-3">
                            {errors.descriptionAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className="grid grid-cols-2 gap-4">
                <FormItem label="Button Text English">
                    <Controller
                        name="buttonTextEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Button Text is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                className={`${errors.buttonTextEn
                                    ? 'input input-md h-11 input-invalid'
                                    : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                    }`}
                                {...field}
                            />
                        )}
                    />
                    {errors.buttonTextEn && (
                        <small className="text-red-600 py-3 ms-24">
                            {errors.buttonTextEn.message as string}{' '}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Button Text Arabic">
                    <Controller
                        name="buttonTextAr"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Button Text is required' }}
                        render={({ field }) => (
                            <Input
                            dir='rtl'
                                type="text"
                                className={`${errors.buttonTextAr
                                    ? 'input input-md h-11 input-invalid'
                                    : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                    }`}
                                {...field}
                            />
                        )}
                    />
                    {errors.buttonTextAr && (
                        <small className="text-red-600 py-3 ms-24">
                            {errors.buttonTextAr.message as string}{' '}
                        </small>
                    )}
                </FormItem>
            </div>
            <div className="grid grid-cols-2 gap-4">
            <FormItem label="Link">
                    <Controller
                        name="link"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Link is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                className={`${errors.link
                                    ? 'input input-md h-11 input-invalid'
                                    : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                    }`}
                                {...field}
                            />
                        )}
                    />
                    {errors.link && (
                        <small className="text-red-600 py-3">
                            {errors.link.message as string}
                        </small>
                    )}
                    <div className='bg-purple-300 p-2 rounded text-xs mt-1'>
                        Please  provide <b>tel:</b>  or <b>mailto:</b>
                    </div>
                </FormItem>
            </div>

            <div>
                <FormItem label="Image">
                    <Upload
                        draggable
                        uploadLimit={1}
                        accept="image/*"
                        fileList={imageFile}
                        onChange={handleImageUpload}
                        ratio={[1126, 1200]}
                    />
                    {imageError && <small className="text-red-600">{imageError}</small>}
                </FormItem>
            </div>

            <h5 className='mb-2'>Office Hours Section</h5>
            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Title English">
                    <Controller
                        name="officeHoursTitleEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.officeHoursTitleEn && (
                        <small className="text-red-600 py-3">
                            {errors.officeHoursTitleEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Title Arabic">
                    <Controller
                        name="officeHoursTitleAr"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir='rtl'
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.officeHoursTitleAr && (
                        <small className="text-red-600 py-3">
                            {errors.officeHoursTitleAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>
            <div className="grid grid-cols-2 gap-4">
            <FormItem label="Description English">
                    <Controller
                        name="officeHoursEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                                textArea
                            />
                        )}
                    />
                    {errors.officeHoursEn && (
                        <small className="text-red-600 py-3">
                            {errors.officeHoursEn.message as string}
                        </small>
                    )}
                </FormItem>
            <FormItem label="Description Arabic">
                    <Controller
                        name="officeHoursAr"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                            dir="rtl"
                                type="text"
                                {...field}
                                textArea
                            />
                        )}
                    />
                    {errors.officeHoursAr && (
                        <small className="text-red-600 py-3">
                            {errors.officeHoursAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <h5>First Store Address </h5>
            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Title English">
                    <Controller
                        name="storeOneTitleEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.storeOneTitleEn && (
                        <small className="text-red-600 py-3">
                            {errors.storeOneTitleEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Title Arabic">
                    <Controller
                        name="storeOneTitleAr"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.storeOneTitleAr && (
                        <small className="text-red-600 py-3">
                            {errors.storeOneTitleAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className="grid grid-cols-2 gap-4">
            <FormItem label="Description English">
                    <Controller
                        name="storeOneAddressEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                                textArea
                            />
                        )}
                    />
                    {errors.storeOneAddressEn && (
                        <small className="text-red-600 py-3">
                            {errors.storeOneAddressEn.message as string}
                        </small>
                    )}
                </FormItem>
            <FormItem label="Description Arabic">
                    <Controller
                        name="storeOneAddressAr"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                                textArea
                            />
                        )}
                    />
                    {errors.storeOneAddressAr && (
                        <small className="text-red-600 py-3">
                            {errors.storeOneAddressAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <h5>Second Store Address</h5>
            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Title English">
                    <Controller
                        name="storeTwoTitleEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.storeTwoTitleEn && (
                        <small className="text-red-600 py-3">
                            {errors.storeTwoTitleEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Title Arabic">
                    <Controller
                        name="storeTwoTitleAr"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.storeTwoTitleAr && (
                        <small className="text-red-600 py-3">
                            {errors.storeTwoTitleAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className="grid grid-cols-2 gap-4">
            <FormItem label="Description English">
                    <Controller
                        name="storeTwoAddressEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                                textArea
                            />
                        )}
                    />
                    {errors.storeTwoAddressEn && (
                        <small className="text-red-600 py-3">
                            {errors.storeTwoAddressEn.message as string}
                        </small>
                    )}
                </FormItem>
            <FormItem label="Description Arabic">
                    <Controller
                        name="storeTwoAddressAr"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                                textArea
                            />
                        )}
                    />
                    {errors.storeTwoAddressAr && (
                        <small className="text-red-600 py-3">
                            {errors.storeTwoAddressAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

             <h5>Seo Section</h5>
            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Title English">
                    <Controller
                        name="metaTitleEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaTitleEn && (
                        <small className="text-red-600 py-3">
                            {errors.metaTitleEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Meta Title Arabic">
                    <Controller
                        name="metaTitleAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaTitleAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaTitleAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Description English">
                    <Controller
                        name="metaDescriptionEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaDescriptionEn && (
                        <small className="text-red-600 py-3">
                            {errors.metaDescriptionEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Meta Description Arabic">
                    <Controller
                        name="metaDescriptionAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaDescriptionAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaDescriptionAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Keywords English">
                    <Controller
                        name="metaKeywordsEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaKeywordsEn && (
                        <small className="text-red-600 py-3">
                            {errors.metaKeywordsEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Meta Keywords Arabic">
                    <Controller
                        name="metaKeywordsAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaKeywordsAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaKeywordsAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Canonical URL English">
                    <Controller
                        name="metaCanonicalUrl"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaCanonicalUrl && (
                        <small className="text-red-600 py-3">
                            {errors.metaCanonicalUrl.message as string}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Meta Canonical URL Arabic">
                    <Controller
                        name="metaCanonicalUrlAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaCanonicalUrlAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaCanonicalUrlAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
               <FormItem label="Meta OG Image">
                    <Upload
                        draggable
                        uploadLimit={1}
                        accept="image/*"
                        fileList={ogImageFile}
                        onChange={handleOgImageUpload}
                        ratio={[1126, 1200]}
                    />
                    {ogImageError && <small className="text-red-600">{ogImageError}</small>}
                </FormItem>
                
            </div>

            <Button
                className="float-right mt-4"
                variant="solid"
                type="submit"
                icon={<AiOutlineSave />}
            >
                Save
            </Button>
        </form>
    )
}
