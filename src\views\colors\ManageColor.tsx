import { Button, FormItem, Input, Select, Tag, toast } from '@/components/ui'
import endpoints from '@/endpoints'
import api from '@/services/api.interceptor'
import { Controller, useForm } from 'react-hook-form'
import { AiOutlineSave } from 'react-icons/ai'
import Notification from '@/components/ui/Notification'
import { useNavigate, useParams } from 'react-router-dom'
import { useEffect, useState } from 'react'
import { HiX } from 'react-icons/hi'
import Breadcrumb from '../modals/BreadCrumb'

/* eslint-disable */
export default function ManageColor() {
    const navigate = useNavigate()
    const params = useParams()
    const [selectedColor, setSelectedColor] = useState<any>([])
    const [selectedType, setSelectedType] = useState<any>()

    const breadcrumbItems = [
        { title: 'Colors', url: '/colors' },
        { title: params.id ? 'Edit Color' : 'Add Color', url: '' },
    ]

    const options: any = [
        { value: 'true', label: 'True' },
        { value: 'false', label: 'False' },
    ]

    const typeOptions: any = [
        { value: 'Single color', label: 'Single Color' },
        { value: 'Multiple color', label: 'Multiple Color' },
    ]

    const getColors = (value: any) => {
        const selectedTypeOption = typeOptions.find(
            (option: any) => option.value === selectedType
        )
        if (!selectedColor.includes(value) && value) {
            if (
                selectedTypeOption &&
                selectedTypeOption.value === 'Multiple color'
            ) {
                if (selectedColor.length < 2) {
                    setSelectedColor((prevColors: any) => [
                        ...prevColors,
                        value,
                    ])
                }
            } else {
                setSelectedColor([value])
            }
        }
    }

    const {
        handleSubmit,
        control,
        setValue,
        formState: { errors },
    } = useForm<any>()

    useEffect(() => {
        if (params.id) {
            api.get(endpoints.colorDetail + params.id)
                .then((res) => {
                    if (res.status == 200) {
                        const data = res.data?.result
                        setValue('name', data?.name?.en)
                        setValue('nameAr', data?.name?.ar)
                        setValue('position', data?.position)
                        setValue('isActive', {
                            value: data?.isActive.toString(),
                            label: data?.isActive ? 'True' : 'False',
                        })
                        const newType =
                            data?.color.length === 1
                                ? 'Single color'
                                : 'Multiple color'
                        setValue('type', { value: newType, label: newType })
                        setSelectedType(newType)
                        if (newType === 'Multiple color')
                            setSelectedColor(data?.color)
                        else setSelectedColor([data?.color[0]])
                        setValue('color', data?.color)
                    }
                })
                .catch((error) => {
                    if(error?.response?.status == 422) navigate('/access-denied');
                    console.log(error)
                })
        }
    }, [params.id])

    const onSubmit = (data: any) => {
        const values: any = {
            name: { en: data?.name, ar: data?.nameAr },
            color: selectedColor,
            position: parseInt(data?.position),
        }

        console.log(data.position)

        if (params.id) {
            values.isActive = data?.isActive?.value == 'true' ? true : false

            api.put(endpoints.updateColor + params.id, values).then((res) => {
                if (res.status == 200) {
                    if (res?.data?.errorCode == 0) {
                        toast.push(
                            <Notification
                                type="success"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                        navigate('/colors')
                    }
                } else {
                    toast.push(
                        <Notification
                            type="warning"
                            title={res.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                }
            })
        } else {
            api.post(endpoints.createColor, values)
                .then((res) => {
                    if (res.status == 200) {
                        toast.push(
                            <Notification
                                type="success"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                        navigate('/colors')
                    }
                })
                .catch((err) => {
                    console.log(err)
                })
        }
    }

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <h3 className="mb-2">{params.id ? 'Edit' : 'Add '} Colours</h3>
            <Breadcrumb items={breadcrumbItems} />
            <div className="grid grid-cols-2 gap-4 mt-4">
                <FormItem label="Name">
                    <Controller
                        name="name"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Name is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                className={`${
                                    errors.name
                                        ? ' input input-md h-11 input-invalid'
                                        : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                }`}
                                {...field}
                            />
                        )}
                    />
                    {errors.name && (
                        <small className="text-red-600 py-3">
                            {errors.name.message as string}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Name Arabic">
                    <Controller
                        name="nameAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Name is required' }}
                        render={({ field }) => (
                            <Input
                                dir='rtl'
                                type="text"
                                className={`${
                                    errors.name
                                        ? ' input input-md h-11 input-invalid'
                                        : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                }`}
                                {...field}
                            />
                        )}
                    />
                    {errors.nameAr && (
                        <small className="text-red-600 py-3">
                            {errors.nameAr.message as string}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Position">
                    <Controller
                        name="position"
                        control={control}
                        defaultValue={0}
                        // rules={{ required: 'Name is required' }}
                        render={({ field }) => (
                            <Input
                                type="number"
                                className={`${
                                    errors.position
                                        ? ' input input-md h-11 input-invalid'
                                        : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                }`}
                                {...field}
                            />
                        )}
                    />
                    {errors.position && (
                        <small className="text-red-600 py-3">
                            {errors.position.message as string}
                        </small>
                    )}
                </FormItem>

                {params.id && (
                    <FormItem label="Is Active?">
                        <Controller
                            name="isActive"
                            control={control}
                            render={({ field }) => (
                                <Select options={options} {...field} />
                            )}
                        />
                    </FormItem>
                )}
            </div>

            <div className="grid grid-cols-2 gap-4">
                <FormItem label="Type">
                    <Controller
                        name="type"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Type is required' }}
                        render={({ field }) => (
                            <Select
                                isSearchable={false}
                                options={typeOptions}
                                onChange={(e: any) => {
                                    const newType = e.value
                                    setSelectedType(newType)
                                    if (
                                        selectedType == 'Multiple color' &&
                                        newType == 'Single color'
                                    ) {
                                        setSelectedColor([selectedColor[0]])
                                    }
                                    field.onChange(e)
                                }}
                                value={field.value}
                            />
                        )}
                    />
                    {errors.type && (
                        <small className="text-red-600 py-3">
                            {errors.type.message as string}
                        </small>
                    )}
                </FormItem>

                {selectedType && (
                    <FormItem label="Colours">
                        <Controller
                            name="color"
                            control={control}
                            defaultValue=""
                            rules={{ required: 'Color is required' }}
                            render={({ field }) => (
                                <Input
                                    type="color"
                                    className="input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600"
                                    {...field}
                                    onChange={(e) => {
                                        getColors(e.target.value)
                                        field.onChange(e)
                                    }}
                                    value={field.value}
                                />
                            )}
                        />
                        {selectedColor.length > 0 && (
                            <div className="flex flex-wrap mt-2 ">
                                {selectedColor.map((color: any, index: any) => (
                                    <Tag
                                        suffix={
                                            <HiX
                                                size={16}
                                                className="ml-1 rtl:mr-1 cursor-pointer"
                                                onClick={() => {
                                                    const updatedColors = [
                                                        ...selectedColor,
                                                    ]
                                                    updatedColors.splice(
                                                        index,
                                                        1
                                                    )
                                                    setSelectedColor(
                                                        updatedColors
                                                    )
                                                }}
                                            />
                                        }
                                        key={index}
                                        style={{
                                            backgroundColor: color,
                                            width: 90,
                                            height: 30,
                                        }}
                                    >
                                        {' '}
                                    </Tag>
                                ))}
                            </div>
                        )}
                        {errors.color && (
                            <small className="text-red-600 py-3">
                                {errors.color.message as string}
                            </small>
                        )}
                    </FormItem>
                )}
            </div>

            <Button
                className="float-right mt-4"
                variant="solid"
                type="submit"
                icon={<AiOutlineSave />}
            >
                Save
            </Button>
        </form>
    )
}
