import { useState, useMemo, useEffect, InputHTMLAttributes } from 'react'
import Table from '@/components/ui/Table'
import Pagination from '@/components/ui/Pagination'
import Select from '@/components/ui/Select'
import { useReactTable, getCoreRowModel, getFilteredRowModel, getPaginationRowModel, flexRender, getSortedRowModel, } from '@tanstack/react-table'
import type { ColumnDef, ColumnFiltersState, FilterFn, } from '@tanstack/react-table'
import Input from '@/components/ui/Input'
import { rankItem } from '@tanstack/match-sorter-utils'
import Button from '@/components/ui/Button'
import { AiOutlinePlus } from 'react-icons/ai'
import api from '@/services/api.interceptor'
import endpoints from '@/endpoints'
import { Link } from 'react-router-dom'
import { HiPencilSquare } from 'react-icons/hi2'
import Tag from '@/components/ui/Tag'
import { toast } from '@/components/ui'
import Notification from '@/components/ui/Notification'
import { MdDeleteOutline } from 'react-icons/md'
import DeleteModal from '@/views/modals/DeleteModal'
import Breadcrumb from '@/views/modals/BreadCrumb'

/*eslint-disable */

interface DebouncedInputProps
  extends Omit<
    InputHTMLAttributes<HTMLInputElement>,
    'onChange' | 'size' | 'prefix'
  > {
  value: string | number
  onChange: (value: string | number) => void
  debounce?: number
}

const { Tr, Th, Td, THead, TBody, Sorter } = Table

function DebouncedInput({
  value: initialValue,
  onChange,
  debounce = 500,
  ...props
}: DebouncedInputProps) {
  const [value, setValue] = useState(initialValue)

  useEffect(() => {
    setValue(initialValue)
  }, [initialValue])

  useEffect(() => {
    const timeout = setTimeout(() => {
      onChange(value)
    }, debounce)

    return () => clearTimeout(timeout)
  }, [value])

  return (
    <div className="flex justify-end">
      <div className="flex items-center mb-4">
        <Input
          {...props}
          value={value}
          onChange={(e) => setValue(e.target.value)}
        />
      </div>
    </div>
  )
}

type Option = {
  value: number
  label: string
}

const pageSizeOption = [
  { value: 10, label: '10 / page' },
  { value: 20, label: '20 / page' },
  { value: 30, label: '30 / page' },
  { value: 40, label: '40 / page' },
  { value: 50, label: '50 / page' },
]

const breadcrumbItems = [
  { title: 'Age Group', url: '' },
];

const AgeGroup = () => {
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([])
  const [globalFilter, setGlobalFilter] = useState('')
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState(null);

  const openDeleteModal = (refid: any) => {
    setDeleteModalOpen(true);
    setItemToDelete(refid);
  };

  const closeDeleteModal = () => {
    setDeleteModalOpen(false);
  };

  const confirmDelete = () => {
    api.put(endpoints.deleteAgeGroup + itemToDelete).then((res) => {
      if (res.status == 200) {
        toast.push(
          <Notification type="success" title={res.data.message} />, {
          placement: 'top-center',
        })
        closeDeleteModal();
        getAgeGroups();
      }
    }).catch((err) => {
      toast.push(
        <Notification type="warning" title={err.response.data.message} />, {
        placement: 'top-center',
      })
      closeDeleteModal();
    })
  };

  const columns = useMemo<ColumnDef<any>[]>(
    () => [
      {
        header: 'Sl No.',
        accessorKey: 'slNo',
        cell: (info) => info.row.index + 1,
        enableSorting: false,
      },
      {
        header: 'Name',
        accessorKey: 'name',
        enableSorting: false,
      },
      {
        header: 'Status',
        accessorKey: 'isActive',
        enableSorting: false,
        cell: (info) => {
          const isActive = info.getValue();
          return (
            <div>
              <Tag className={isActive ? 'bg-emerald-100 text-emerald-600 dark:bg-emerald-500/20 dark:text-emerald-100 border-0 rounded' : 'text-red-600 bg-red-100 dark:text-red-100 dark:bg-red-500/20 border-0 rounded'} >
                {isActive ? 'Active' : 'Inactive'}
              </Tag>
            </div>
          )
        },
      },
      {
        header: 'Actions',
        accessorKey: 'refid',
        enableSorting: false,
        cell: (info) => {
          return (
            <div className='flex items-center'>
              <Link
                to={`/catalog/manage-age-group/${info.getValue()}`}
              >
                <HiPencilSquare size={25} />
              </Link>
              <div onClick={() => openDeleteModal(info.getValue())}>
                <MdDeleteOutline size={25} className='ml-4' />
              </div>
            </div>
          )
        },
      }
    ],
    []
  )

  const [data, setData] = useState<any[]>([])
  const totalData = data?.length


  const fuzzyFilter: FilterFn<any> = (row, columnId, value, addMeta) => {
    const itemRank = rankItem(row.getValue(columnId), value)

    addMeta({
      itemRank,
    })

    return itemRank.passed
  }

  const table = useReactTable({
    data,
    columns,
    filterFns: {
      fuzzy: fuzzyFilter,
    },
    state: {
      columnFilters,
      globalFilter,
    },
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    globalFilterFn: fuzzyFilter,
    getSortedRowModel: getSortedRowModel(),
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  })

  const onPaginationChange = (page: number) => {
    table.setPageIndex(page - 1)
  }

  const onSelectChange = (value = 0) => {
    table.setPageSize(Number(value))
  }

  const getAgeGroups = () => {
    api
      .post(endpoints.ageGroups, {})
      .then((res) => {
        setData(res?.data?.result)
      })
      .catch((error) => {
        console.error('Error fetching data: ', error)
      })
  }

  useEffect(() => {
    getAgeGroups()
  }, [])

  return (
    <div>
      <div className='mb-4 flex'>
        <h2>Age Groups</h2>
        <Link className="mr-2 mb-2 ml-auto" to='/catalog/manage-age-group'>
          <Button variant="twoTone" color="green-600" icon={<AiOutlinePlus />}>
            Add Age Group
          </Button>
        </Link>
      </div>
      <Breadcrumb items={breadcrumbItems} />
      <div className="mb-4 flex space-x-2 justify-end">
        <DebouncedInput
          value={globalFilter ?? ''}
          className="p-2 font-lg shadow border border-block"
          placeholder="Search all columns..."
          onChange={(value) => setGlobalFilter(String(value))}
        />
      </div>
      <Table>
        <THead>
          {table.getHeaderGroups().map((headerGroup) => (
            <Tr key={headerGroup.id}>
              {headerGroup.headers.map((header) => {
                return (
                  <Th
                    key={header.id}
                    colSpan={header.colSpan}
                  >
                    {header.column.getCanSort() ? (
                      <div
                        className="cursor-pointer select-none"
                        onClick={header.column.getToggleSortingHandler()}
                      >
                        {flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                        <Sorter sort={header.column.getIsSorted()} />
                      </div>
                    ) : (
                      <div>
                        {flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                      </div>
                    )}
                  </Th>
                )
              })}
            </Tr>
          ))}
        </THead>
        <TBody>
          {table.getRowModel().rows.map((row) => {
            return (
              <Tr key={row.id}>
                {row.getVisibleCells().map((cell) => {
                  return (
                    <Td key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </Td>
                  )
                })}
              </Tr>
            )
          })}
        </TBody>
      </Table>
      <div className="flex items-center justify-between mt-4">
        <Pagination
          pageSize={table.getState().pagination.pageSize}
          currentPage={table.getState().pagination.pageIndex + 1}
          total={totalData}
          onChange={onPaginationChange}
        />
        <div style={{ minWidth: 130 }}>
          <Select<Option>
            size="sm"
            isSearchable={false}
            value={pageSizeOption.filter(
              (option) =>
                option.value ===
                table.getState().pagination.pageSize
            )}
            options={pageSizeOption}
            onChange={(option) => onSelectChange(option?.value)}
          />
        </div>
      </div>

      <DeleteModal
        isOpen={deleteModalOpen}
        onClose={closeDeleteModal}
        onConfirm={confirmDelete}
        title="Delete Age Group"
        content="Are you sure you want to delete this Age Group?"
      />

    </div>
  )
}

export default AgeGroup