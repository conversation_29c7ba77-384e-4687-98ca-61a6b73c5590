import { Button, FormItem, toast, Select, Upload, Input } from '@/components/ui'
import endpoints from '@/endpoints'
import api from '@/services/api.interceptor'
import { Controller, useForm } from 'react-hook-form'
import { AiOutlineSave } from 'react-icons/ai'
import Notification from '@/components/ui/Notification'
import { useNavigate, useParams } from 'react-router-dom'
import { useEffect, useState } from 'react'
import Breadcrumb from '@/views/modals/BreadCrumb'
import { Dropzone } from '@/components/shared/Dropzone'

/* eslint-disable */

export default function ManageCollections() {
    const [productOptions, setProductOptions] = useState([])
    const [bannerFiles, setbannerFiles] = useState<any>([])
    const [thumbnailFiles, setThumbnailFiles] = useState<any>([])
    const [thumbnailError, setThumbnailError] = useState<any>(null)
    const [bannerError, setBannerError] = useState<any>(null)

    const [productValue, setProductValue] = useState("")
    const [isLoading, setIsLoading] = useState(true)

    const navigate = useNavigate()
    const params = useParams()

    const breadcrumbItems = [
        { title: 'Collections', url: '/catalog/collections' },
        { title: 'Manage Collections', url: '' },
    ]

    const handleBannerUpload = (files: any) => {
        setbannerFiles(files)
    }

    const handleThumbnailUpload = (files: any) => {
        setThumbnailFiles(files)
    }

    const imageUrl = import.meta.env.VITE_ASSET_URL


    useEffect(() => {
        if (params.id) {
            api.get(endpoints.collectionDetail + params.id)
                .then((res) => {
                    if (res?.status == 200) {
                        const data = res?.data?.result
                        setValue('titleEn', data?.title?.en)
                        setValue('titleAr', data?.title?.ar)
                        setValue('metaTitleEn', data?.seoDetails?.title?.en)
                        setValue('metaTitleAr', data?.seoDetails?.title?.ar)
                        setValue('metaDescriptionEn', data?.seoDetails?.description?.en)
                        setValue('metaDescriptionAr', data?.seoDetails?.description?.ar)
                        setValue('metaKeywordsEn', data?.seoDetails?.keywords?.en)
                        setValue('metaKeywordsAr', data?.seoDetails?.keywords?.ar)
                        setValue('metaCanonicalUrl', data?.seoDetails?.canonical?.en)
                        setValue('metaCanonicalUrlAr', data?.seoDetails?.canonical?.ar)
                        setOgImageFile([imageUrl + data?.seoDetails?.ogImage])
                        if (data?.products?.length > 0)
                            setValue(
                                'products',
                                data?.products?.map((product: any) => ({
                                    value: product?._id,
                                    label: product?.name?.en,
                                }))
                            )
                        setValue('isActive', {
                            value: data?.isActive,
                            label: data?.isActive ? 'True' : 'False',
                        })
                        setThumbnailFiles([data?.image])
                        setbannerFiles([data?.banner])
                    }
                })
                .catch((error) => {
                    if(error?.response?.status == 422) navigate('/access-denied');
                    console.error('Error fetching data: ', error)
                })
        }
    }, [])

    useEffect(() => {
        const controller = new AbortController()
        const limit = productValue? 60: 30;
        setIsLoading(true)
        api.post(endpoints.products + `?page=1&limit=${limit}`, { isActive: true, keyword: productValue }, {
            signal: controller.signal
        }).then((res) => {
            if (res?.status == 200) {
                const products = res?.data?.result?.products || []
                const newproductOptions = products.map((product: any) => ({
                    value: product._id,
                    label: product.name.en,
                }))
                setProductOptions(newproductOptions)
            }
            setIsLoading(false)
        }).catch((error) => {
            console.error('Error fetching data: ', error)
            // setIsLoading(false)
        })
        return () => {
            controller.abort()
        }
    }, [productValue])

    const options: any = [
        { value: 'true', label: 'True' },
        { value: 'false', label: 'False' },
    ]

    const {
        handleSubmit,
        control,
        setValue,
        formState: { errors },
    } = useForm<any>()

    const [ogImageFile, setOgImageFile] = useState<string[]>([])

    const handleOgImageUpload = (file: any) => {
        setOgImageFile(file)
    }

    const onSubmit = (value: any) => {
        // let hasError = false;
        // if (thumbnailFiles.length == 0) {
        //   setThumbnailError('Thumbnail image is required')
        //   hasError = true;
        // } else setThumbnailError(null)

        // if (bannerFiles.length == 0) {
        //   setBannerError('Banner image is required')
        //   hasError = true;
        // } else setBannerError(null)

        // if (hasError) return;

        const formData = new FormData()
        formData.append('title[en]', value.titleEn)
        formData.append('title[ar]', value.titleAr)
        formData.append('seoDetails[title][en]', value.metaTitleEn)
        formData.append('seoDetails[title][ar]', value.metaTitleAr)
        formData.append('seoDetails[description][en]', value.metaDescriptionEn)
        formData.append('seoDetails[description][ar]', value.metaDescriptionAr)
        formData.append('seoDetails[keywords][en]', value.metaKeywordsEn)
        formData.append('seoDetails[keywords][ar]', value.metaKeywordsAr)
        formData.append('seoDetails[canonical][en]', value.metaCanonicalUrl)
        formData.append('seoDetails[canonical][ar]', value.metaCanonicalUrlAr)
        formData.append('ogImage', ogImageFile[0])
        for (let i = 0; i < value.products.length; i++) {
            formData.append('products', value.products[i].value)
        }
        if (bannerFiles.length > 0) formData.append('banner', bannerFiles[0])
        if (thumbnailFiles.length > 0)
            formData.append('image', thumbnailFiles[0])
        if (params.id) formData.append('isActive', value.isActive.value)

        if (params.id) {
            api.put(endpoints.updateCollection + params.id, formData).then(
                (res) => {
                    if (res.status == 200) {
                        if (res?.data?.errorCode == 0) {
                            toast.push(
                                <Notification
                                    type="success"
                                    title={res.data.message}
                                />,
                                {
                                    placement: 'top-center',
                                }
                            )
                            navigate('/catalog/collections')
                        }
                    } else {
                        toast.push(
                            <Notification
                                type="warning"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                    }
                }
            )
        } else {
            api.post(endpoints.createCollection, formData)
                .then((res) => {
                    if (res.status == 200) {
                        if (res?.data?.errorCode == 0) {
                            toast.push(
                                <Notification
                                    type="success"
                                    title={res.data.message}
                                />,
                                {
                                    placement: 'top-center',
                                }
                            )
                            navigate('/catalog/collections')
                        }
                    } else {
                        toast.push(
                            <Notification
                                type="warning"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                    }
                })
                .catch((err) => {
                    console.log(err)
                })
        }
    }

    return (
        <form onSubmit={handleSubmit(onSubmit)}>
            <h3 className="mb-2">
                {params.id ? 'Edit Collection' : 'Add Collection'}
            </h3>
            <Breadcrumb items={breadcrumbItems} />
            <h5>Title</h5>
            <div className="grid grid-cols-2 gap-4 mt-2">
                <FormItem label="English">
                    <Controller
                        name="titleEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <input
                                type="text"
                                className={`${errors.titleEn
                                    ? ' input input-md h-11 input-invalid'
                                    : 'input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600'
                                    }`}
                                {...field}
                            />
                        )}
                    />
                    {errors.titleEn && (
                        <small className="text-red-600 py-3">
                            {errors.titleEn.message as string}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Arabic">
                    <Controller
                        name="titleAr"
                        control={control}
                        defaultValue=""
                        // rules={{ required: 'Field is required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {/* {errors.titleAr && (
                        <small className="text-red-600 py-3">
                            {errors.titleAr.message as string}
                        </small>
                    )} */}
                </FormItem>
            </div>

            <div className="grid grid-cols-2 gap-4">
                <FormItem label="Products">
                    <Controller
                        name="products"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Select
                                onInputChange={(value) => setProductValue(value)}
                                isMulti
                                options={productOptions}
                                isLoading={isLoading}
                                {...field}
                            />
                        )}
                    />
                    {errors.products && (
                        <small className="text-red-600 py-3">
                            {errors.products.message as string}
                        </small>
                    )}
                </FormItem>

                {params.id && (
                    <FormItem label="is Active ?">
                        <Controller
                            name="isActive"
                            control={control}
                            defaultValue=""
                            render={({ field }) => (
                                <Select options={options} {...field} />
                            )}
                        />
                    </FormItem>
                )}
            </div>

            {/* <div className="grid grid-cols-2 gap-4">
        <FormItem label="Thumbnail">
          <Upload draggable uploadLimit={1} accept="image/*" fileList={thumbnailFiles} onChange={handleThumbnailUpload} />
          {thumbnailError && <small className="text-red-600 py-3">{thumbnailError}</small>}
        </FormItem>

        <FormItem label="Banner">
          <Upload draggable uploadLimit={1} accept="image/*" fileList={bannerFiles} onChange={handleBannerUpload} />
          {bannerError && <small className="text-red-600 py-3">{bannerError}</small>}
        </FormItem>
      </div> */}

      {/* <h5>Seo Section</h5>
            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Title English">
                    <Controller
                        name="metaTitleEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaTitleEn && (
                        <small className="text-red-600 py-3">
                            {errors.metaTitleEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Meta Title Arabic">
                    <Controller
                        name="metaTitleAr"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaTitleAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaTitleAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Description English">
                    <Controller
                        name="metaDescriptionEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaDescriptionEn && (
                        <small className="text-red-600 py-3">
                            {errors.metaDescriptionEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Meta Description Arabic">
                    <Controller
                        name="metaDescriptionAr"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaDescriptionAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaDescriptionAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Keywords English">
                    <Controller
                        name="metaKeywordsEn"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaKeywordsEn && (
                        <small className="text-red-600 py-3">
                            {errors.metaKeywordsEn.message as string}
                        </small>
                    )}
                </FormItem>
                <FormItem label="Meta Keywords Arabic">
                    <Controller
                        name="metaKeywordsAr"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                dir="rtl"
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaKeywordsAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaKeywordsAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
                <FormItem label="Meta Canonical URL English">
                    <Controller
                        name="metaCanonicalUrl"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaCanonicalUrl && (
                        <small className="text-red-600 py-3">
                            {errors.metaCanonicalUrl.message as string}
                        </small>
                    )}
                </FormItem>

                <FormItem label="Meta Canonical URL Arabic">
                    <Controller
                        name="metaCanonicalUrlAr"
                        control={control}
                        defaultValue=""
                        rules={{ required: 'Field is required' }}
                        render={({ field }) => (
                            <Input
                                type="text"
                                {...field}
                            />
                        )}
                    />
                    {errors.metaCanonicalUrlAr && (
                        <small className="text-red-600 py-3">
                            {errors.metaCanonicalUrlAr.message as string}
                        </small>
                    )}
                </FormItem>
            </div>

            <div className='grid grid-cols-2 gap-4'>
               <FormItem label="Meta OG Image">
                    <Upload
                        draggable
                        uploadLimit={1}
                        accept="image/*"
                        fileList={ogImageFile}
                        onChange={handleOgImageUpload}
                        ratio={[1126, 1200]}
                    />
                </FormItem>
            </div> */}


            <Button
                className="float-right mt-6"
                variant="solid"
                type="submit"
                icon={<AiOutlineSave />}
            >
                Save
            </Button>
        </form>
    )
}
