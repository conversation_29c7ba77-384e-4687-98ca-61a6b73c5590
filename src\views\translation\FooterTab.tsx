import { FormItem, Input } from '@/components/ui'
import { Controller } from 'react-hook-form'

const footer = [
    {
        title: "One",
        value: 1,
    },
    {
        title: "Two",
        value: 2,
    },
    {
        title: "Three",
        value: 3,
    },
    {
        title: "Four",
        value: 4,
    },
]

export default function FooterTab({ control, errors }: any) {
    return (
        <>
            <h3>Footer</h3>
            <div className="mt-2">
                {footer.map((item: any) => (
                    <>
                        <h5 className=''>Column {item.title}</h5>
                        <div className="grid grid-cols-2 gap-4">
                            <FormItem label="Title English">
                                <Controller
                                    control={control}
                                    name={`footerCol${item.value}TitleEn`}
                                    rules={{ required: 'Field Required' }}
                                    render={({ field }) => <Input type="text" {...field} />}
                                />
                                {errors[`footerCol${item.value}TitleEn`] && (
                                    <small className="text-red-600 py-3">
                                        {' '}
                                        {errors[`footerCol${item.value}TitleEn`].message as string}{' '}
                                    </small>
                                )}
                            </FormItem>
                            <FormItem label="Title Arabic">
                                <Controller
                                    control={control}
                                    name={`footerCol${item.value}TitleAr`}
                                    rules={{ required: 'Field Required' }}
                                    render={({ field }) => <Input dir='rtl' type="text" {...field} />}
                                />
                                {errors[`footerCol${item.value}TitleAr`] && (
                                    <small className="text-red-600 py-3">
                                        {' '}
                                        {errors[`footerCol${item.value}TitleAr`].message as string}{' '}
                                    </small>
                                )}
                            </FormItem>
                        </div>
                        <div className="grid grid-cols-2 gap-4">
                            <FormItem label="Description English">
                                <Controller
                                    control={control}
                                    name={`footerCol${item.value}DescriptionEn`}
                                    rules={{ required: 'Field Required' }}
                                    render={({ field }) => <Input type="text" {...field} />}
                                />
                                {errors[`footerCol${item.value}DescriptionEn`] && (
                                    <small className="text-red-600 py-3">
                                        {' '}
                                        {errors[`footerCol${item.value}DescriptionEn`].message as string}{' '}
                                    </small>
                                )}
                            </FormItem>
                            <FormItem label="Description Arabic">
                                <Controller
                                    control={control}
                                    name={`footerCol${item.value}DescriptionAr`}
                                    rules={{ required: 'Field Required' }}
                                    render={({ field }) => <Input dir='rtl' type="text" {...field} />}
                                />
                                {errors[`footerCol${item.value}DescriptionAr`] && (
                                    <small className="text-red-600 py-3">
                                        {' '}
                                        {errors[`footerCol${item.value}DescriptionAr`].message as string}{' '}
                                    </small>
                                )}
                            </FormItem>
                        </div>
                    </>
                ))}
            </div>
            <hr />
            <h3 className='my-4'>News Letter</h3>
            <div className="mt-2">
                <div className="grid grid-cols-2 gap-4">
                    <FormItem label="News Letter Text English">
                        <Controller
                            control={control}
                            name={`newsLetterTextEn`}
                            rules={{ required: 'Field Required' }}
                            render={({ field }) => <Input type="text" {...field} />}
                        />
                        {errors[`newsLetterTextEn`] && (
                            <small className="text-red-600 py-3">
                                {' '}
                                {errors[`newsLetterTextEn`].message as string}{' '}
                            </small>
                        )}
                    </FormItem>
                    <FormItem label="News Letter Text Arabic">
                        <Controller
                            control={control}
                            name={`newsLetterTextAr`}
                            rules={{ required: 'Field Required' }}
                            render={({ field }) => <Input dir='rtl' type="text" {...field} />}
                        />
                        {errors[`newsLetterTextAr`] && (
                            <small className="text-red-600 py-3">
                                {' '}
                                {errors[`newsLetterTextAr`].message as string}{' '}
                            </small>
                        )}
                    </FormItem>
                </div>
            </div>
            <div className="mt-2">
                <div className="grid grid-cols-2 gap-4">
                    <FormItem label="News Letter Placeholder English">
                        <Controller
                            control={control}
                            name={`newsLetterPlaceholderEn`}
                            rules={{ required: 'Field Required' }}
                            render={({ field }) => <Input type="text" {...field} />}
                        />
                        {errors[`newsLetterPlaceholderEn`] && (
                            <small className="text-red-600 py-3">
                                {' '}
                                {errors[`newsLetterPlaceholderEn`].message as string}{' '}
                            </small>
                        )}
                    </FormItem>
                    <FormItem label="News Letter Placeholder Arabic">
                        <Controller
                            control={control}
                            name={`newsLetterPlaceholderAr`}
                            rules={{ required: 'Field Required' }}
                            render={({ field }) => <Input dir='rtl' type="text" {...field} />}
                        />
                        {errors[`newsLetterPlaceholderAr`] && (
                            <small className="text-red-600 py-3">
                                {' '}
                                {errors[`newsLetterPlaceholderAr`].message as string}{' '}
                            </small>
                        )}
                    </FormItem>
                </div>
            </div>
            <hr  />
            <h3 className='my-4'>Other</h3>
            <div className="mt-2">
                <div className="grid grid-cols-2 gap-4">
                    <FormItem label="Do you need help English">
                        <Controller
                            control={control}
                            name={`doYouNeedHelpEn`}
                            rules={{ required: 'Field Required' }}
                            render={({ field }) => <Input type="text" {...field} />}
                        />
                        {errors[`doYouNeedHelpEn`] && (
                            <small className="text-red-600 py-3">
                                {' '}
                                {errors[`doYouNeedHelpEn`].message as string}{' '}
                            </small>
                        )}
                    </FormItem>
                    <FormItem label="Do you need help Arabic">
                        <Controller
                            control={control}
                            name={`doYouNeedHelpAr`}
                            rules={{ required: 'Field Required' }}
                            render={({ field }) => <Input dir='rtl' type="text" {...field} />}
                        />
                        {errors[`doYouNeedHelpAr`] && (
                            <small className="text-red-600 py-3">
                                {' '}
                                {errors[`doYouNeedHelpAr`].message as string}{' '}
                            </small>
                        )}
                    </FormItem>
                </div>
            </div>
            <div className="mt-2">
                <div className="grid grid-cols-2 gap-4">
                    <FormItem label="Powered By English">
                        <Controller
                            control={control}
                            name={`poweredByEn`}
                            rules={{ required: 'Field Required' }}
                            render={({ field }) => <Input type="text" {...field} />}
                        />
                        {errors[`poweredByEn`] && (
                            <small className="text-red-600 py-3">
                                {' '}
                                {errors[`poweredByEn`].message as string}{' '}
                            </small>
                        )}
                    </FormItem>
                    <FormItem label="Powered By Arabic">
                        <Controller
                            control={control}
                            name={`poweredByAr`}
                            rules={{ required: 'Field Required' }}
                            render={({ field }) => <Input dir='rtl' type="text" {...field} />}
                        />
                        {errors[`poweredByAr`] && (
                            <small className="text-red-600 py-3">
                                {' '}
                                {errors[`poweredByAr`].message as string}{' '}
                            </small>
                        )}
                    </FormItem>
                </div>
            </div>
            <div className="mt-2">
                <div className="grid grid-cols-2 gap-4">
                    <FormItem label="Follow Us English">
                        <Controller
                            control={control}
                            name={`followUsEn`}
                            rules={{ required: 'Field Required' }}
                            render={({ field }) => <Input type="text" {...field} />}
                        />
                        {errors[`followUsEn`] && (
                            <small className="text-red-600 py-3">
                                {' '}
                                {errors[`followUsEn`].message as string}{' '}
                            </small>
                        )}
                    </FormItem>
                    <FormItem label="Follow Us Arabic">
                        <Controller
                            control={control}
                            name={`followUsAr`}
                            rules={{ required: 'Field Required' }}
                            render={({ field }) => <Input dir='rtl' type="text" {...field} />}
                        />
                        {errors[`followUsAr`] && (
                            <small className="text-red-600 py-3">
                                {' '}
                                {errors[`followUsAr`].message as string}{' '}
                            </small>
                        )}
                    </FormItem>
                </div>
            </div>
        </>
    )
}
