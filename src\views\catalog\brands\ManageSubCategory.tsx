import { Controller, useForm } from 'react-hook-form'
import Button from '@/components/ui/Button'
import { AiOutlineSave } from 'react-icons/ai'
import { FormItem, Select, } from '@/components/ui'
import api from '@/services/api.interceptor'
import endpoints from '@/endpoints'
import { useEffect, useState } from 'react'
import toast from '@/components/ui/toast'
import Notification from '@/components/ui/Notification'
import { useNavigate } from 'react-router-dom'

/* eslint-disable */
interface Inputs {
    nameEn: any
    nameAr: any
    isRoot: any
    parentCategory: any
    isActive: any
}

const imageUrl = import.meta.env.VITE_ASSET_URL

export default function ManageSubCategory({ id, handleClose, parent, getTrigger }: any) {
    const navigate = useNavigate() 
    const options: any = [
        { value: 'true', label: 'True' },
        { value: 'false', label: 'False' },
    ]

    useEffect(() => {
        if (id) {
            api.get(endpoints.brandDetail + id)
                .then((res) => {
                    if (res?.status == 200) {
                        const data = res?.data?.result
                        setValue('nameEn', data?.name?.en)
                        setValue('nameAr', data?.name?.ar)
                        setValue('isActive', {
                            value: data?.isActive,
                            label: data?.isActive ? 'True' : 'False',
                        })
                    }
                })
                .catch((error) => {
                    console.error('Error fetching data: ', error)
                })
        }
    }, [])

    const {
        handleSubmit,
        setValue,
        register,
        control,
        formState: { errors },
    } = useForm<Inputs>()

    const onSubmit = handleSubmit(async (data: any) => {
        let formData: any = {};
        formData.name = {
            en: data.nameEn,
            ar: data.nameAr,
        }

        // return;
        // formData.parentRefId = parentRef

        if (id) {
            // formData.append('isActive', data?.isActive?.value)
            formData.refid = id
            api.post(endpoints.updateBrand, formData)
                .then((res) => {
                    if (res?.status == 200) {
                        toast.push(
                            <Notification
                                type="success"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                        getTrigger()
                        handleClose()
                    }
                })
                .catch((error) => {
                    console.error('Error fetching data: ', error)
                })
        } else {
            formData.isMain = false
            formData.parent = parent
            api.post(endpoints.createBrand, formData)
                .then((res) => {
                    if (res?.status == 200) {
                        toast.push(
                            <Notification
                                type="success"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                        getTrigger()
                        handleClose()
                    }
                })
                .catch((error) => {
                    console.error('Error fetching data: ', error)
                })
        }
    })

    return (
        <form>
            <h3>{id ? 'Edit' : 'Add'} Sub Brand</h3>

            <h5 className="mt-4">Name</h5>

            <div className="grid grid-cols-2 gap-4 mt-2">
                <div>
                    <label
                        className={`block form-label mb-2 ${errors.nameEn ? 'error' : ''
                            }`}
                        htmlFor="nameEn"
                    >
                        English
                    </label>
                    <input
                        type="text"
                        id="nameEn"
                        placeholder="Enter name in English....."
                        className={`input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600 ${errors.nameEn
                                ? ' focus:ring-red-600 focus-within:ring-red-600 focus-within:border-red-600 focus:border-red-600'
                                : ''
                            }`}
                        {...register('nameEn', {
                            required: 'Required Field',
                        })}
                    />
                    {errors.nameEn && (
                        <small className="text-red-600 py-3">
                            {errors.nameEn.message as string}
                        </small>
                    )}
                </div>
                <div>
                    <label
                        className={`block form-label mb-2 ${errors.nameAr ? 'error' : ''
                            }`}
                        htmlFor="nameAr"
                    >
                        Arabic
                    </label>
                    <input
                        dir="rtl"
                        type="text"
                        id="nameAr"
                        placeholder="Enter name in Arabic....."
                        className={`input input-md h-11 focus:ring-indigo-600 focus-within:ring-indigo-600 focus-within:border-indigo-600 focus:border-indigo-600 ${errors.nameAr
                                ? ' focus:ring-red-600 focus-within:ring-red-600 focus-within:border-red-600 focus:border-red-600'
                                : ''
                            }`}
                        {...register('nameAr', {
                            // required: 'Required Field',
                        })}
                    />
                </div>
            </div>

            {/* {id && (
                <div className="grid grid-cols-2 gap-4 mt-2">
                    <FormItem label="Is Active?">
                        <Controller
                            name="isActive"
                            control={control}
                            defaultValue=""
                            render={({ field }) => (
                                <Select {...field} options={options} />
                            )}
                        />
                    </FormItem>
                </div>
            )} */}
            <Button
                className="float-right mt-4"
                variant="solid"
                type="button"
                onClick={onSubmit}
                icon={<AiOutlineSave />}
            >
                Save
            </Button>
        </form>
    )
}
