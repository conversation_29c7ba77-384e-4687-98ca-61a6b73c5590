/*eslint-disable */
import { FormItem } from '@/components/ui/Form'
import Input from '@/components/ui/Input'
import Button from '@/components/ui/Button'
import Upload from '@/components/ui/Upload'
import { useCallback, useEffect, useState } from 'react'
import api from '@/services/api.interceptor'
import endpoints from '@/endpoints'
import toast from '@/components/ui/toast'
import Notification from '@/components/ui/Notification'
import { useNavigate, useParams } from 'react-router-dom'
import Breadcrumb from '@/views/modals/BreadCrumb'
import { Controller, useForm } from 'react-hook-form'
import { Dialog, Select, Switcher } from '@/components/ui'
import { AiOutlineSave } from 'react-icons/ai'
import DeleteModal from '@/views/modals/DeleteModal'
import ManageSubCategory from './ManageSubCategory'
import { HiPencilSquare } from 'react-icons/hi2'
import { MdDeleteOutline } from 'react-icons/md'

import { closestCenter, DndContext, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { arrayMove, SortableContext, sortableKeyboardCoordinates, useSortable, verticalListSortingStrategy, } from '@dnd-kit/sortable';
import { CSS } from "@dnd-kit/utilities";
import { MdDragIndicator } from "react-icons/md";
import { LaptopMockup, PhoneMockup } from '@/components/shared/Mockup'
import ManageBanner from './ManageBanner'
import { RichTextEditor } from '@/components/shared'
import ManageContent from './ManageContent'
import ManageCarousel from './ManageCarousel'
import ManageOfferBanner from './OfferBanner'

const imageUrl = import.meta.env.VITE_ASSET_URL

const breadcrumbItems = [
    { title: 'Brands', url: '/catalog/brands' },
    { title: 'Manage Brands', url: '' },
]

let pageSectionsOption = [
    {
        id: 1,
        type: "banner",
        bannerType: "image",
        mobileSrc: "",
        src: "",
        isActive: true,
        title: "Banner/Video",
        banner: {
            en: "",
            ar: ""
        },
    },
    {
        id: 2,
        type: "content",
        content: {
            en: "English Content",
            ar: "Arabic Content"
        },
        isActive: true,
        title: "Content"
    },
    {
        id: 3,
        type: "carousel",
        products: [],
        isActive: true,
        title: "Featured Products"
    },
    {
        id: 4,
        type: "offerBanner",
        bannerEn: "",
        bannerAr: "",
        isActive: true,
        title: "Offer Banner"
    }
]

const ManageBrand = () => {
    const navigate = useNavigate()
    const params = useParams()
    const [stores, setStores] = useState([] as any)
    const [brandId, setBrandId] = useState("")
    const [brand, setBrand] = useState({} as any)
    const [subPop, setSubPop] = useState(false)
    const [trigger, setTrigger] = useState(false)
    const [subBrands, setSubBrands] = useState([] as any)

    const [bannerFiles, setbannerFiles] = useState([] as any)
    const [bannerFilesAr, setbannerFilesAr] = useState([] as any)

    const [brandFile, setBrandFile] = useState([] as any)

    const [ogImageFile, setOgImageFile] = useState([] as any)
    const [ogImageError, setOgImageError] = useState<any>(null)

    const [posterFile, setPosterFile] = useState([] as any)
    const [posterFileAr, setPosterFileAr] = useState([] as any)

    const [section, setSection] = useState("details")
    const [pageSections, setPageSections] = useState(pageSectionsOption as any)
    const [pageSection, setPageSection] = useState<any>(null)
    const [showPagePopup, setShowPagePopup] = useState(false)

    const handleDrag = useCallback((event: any) => {
        const { active, over }: any = event;
        if (active?.id !== over?.id) {
            const oldIndex = pageSections.findIndex(((item: any) => item?.id == active.id));
            const newIndex = pageSections.findIndex(((item: any) => item?.id == over.id));
            let newCats = arrayMove(pageSections, oldIndex, newIndex);
            setPageSections(newCats)
        }
    }, [pageSections]);

    const handleActive = (checked: any, i: number) => {
        let newCats = [...pageSections];
        newCats[i].isActive = !checked;
        setPageSections(newCats);
    }

    const options: any = [
        { value: 'true', label: 'True' },
        { value: 'false', label: 'False' },
    ]

    const sensors = useSensors(
        useSensor(PointerSensor, {
            activationConstraint: {
                distance: 5
            }
        }),
        useSensor(KeyboardSensor, {
            coordinateGetter: sortableKeyboardCoordinates,
        })
    );

    const {
        handleSubmit,
        setValue,
        register,
        control,
        formState: { errors },
    } = useForm<any>()

    const getStores = () => {
        api.get(endpoints.stores)
            .then((res) => {
                if (res?.status == 200) {
                    const stores = res.data.result || []
                    const newStoreOptions = stores.map((store: any) => ({
                        value: store._id,
                        label: store.name.en,
                    }))
                    setStores(newStoreOptions)
                }
            })
            .catch((error) => {
                navigate('/access-denied')
                console.error('Error fetching data: ', error)
            })
    }

    const handleBannerUpload = (files: any) => {
        setbannerFiles(files)
    }
    const handleBannerUploadAr = (files: any) => {
        setbannerFilesAr(files)
    }

    const handleImageUpload = (files: any) => {
        setBrandFile(files)
    }

    const handlePosterUpload = (files: any) => {
        setPosterFile(files)
    }
    const handlePosterUploadAr = (files: any) => {
        setPosterFileAr(files)
    }

    const handleOgImageUpload = (files: any) => {
        setOgImageFile(files)
    }

    useEffect(() => {
        getStores()
        if (params.id)
            api.get(endpoints.brandDetail + params.id)
                .then((res) => {
                    if (res?.status == 200) {
                        const brandData = res.data.result
                        setBrand(brandData)
                        console.log(brandData, "brand data")
                        setBrandId(brandData?.brandId)
                        setValue('nameEn', brandData.name?.en)
                        setValue('nameAr', brandData.name?.ar)
                        setValue('overviewEn', brandData?.overview?.en)
                        setValue('overviewAr', brandData?.overview?.ar)
                        setValue(
                            'stores',
                            brandData?.store?.map((store: any) => ({
                                value: store?._id,
                                label: store.name?.en,
                            }))
                        )
                        setValue('isActive', {
                            value: brandData.isActive,
                            label: brandData.isActive ? 'True' : 'False',
                        })
                        setValue('isCashbackEnabled', {
                            value: brandData?.isCashbackEnabled,
                            label: brandData?.isCashbackEnabled ? 'True' : 'False',
                        })
                        setValue('cashbackPercentage', brandData?.cashbackPercentage)
                        setValue('position', brandData?.position)
                        setValue('metaTitleEn', brandData?.seoDetails?.title?.en)
                        setValue('metaTitleAr', brandData?.seoDetails?.title?.ar)
                        setValue('metaDescriptionEn', brandData?.seoDetails?.description?.en)
                        setValue('metaDescriptionAr', brandData?.seoDetails?.description?.ar)
                        setValue('metaKeywordsEn', brandData?.seoDetails?.keywords?.en)
                        setValue('metaKeywordsAr', brandData?.seoDetails?.keywords?.ar)
                        setValue('metaCanonicalUrl', brandData?.seoDetails?.canonical?.en)
                        setValue('metaCanonicalUrlAr', brandData?.seoDetails?.canonical?.ar)

                        setBrandFile([imageUrl + brandData?.image])
                        setOgImageFile([imageUrl + brandData?.seoDetails?.ogImage])
                        setbannerFiles([imageUrl + brandData?.banner?.en])
                        setbannerFilesAr([imageUrl + brandData?.banner?.ar])
                        setPosterFile([imageUrl + brandData?.poster?.en])
                        setPosterFileAr([imageUrl + brandData?.poster?.ar])
                        setSubBrands(brandData?.subBrands ?? [])

                        if (brandData?.page?.length > 0) setPageSections(brandData?.page?.map((item: any) => (
                            item?.type == "carousel" ? {
                                ...item,
                                id: item?._id,
                                products: item?.products?.map((product: any) => ({
                                    value: (product?._id ?? product),
                                    label: (product.name?.en ?? product),
                                }))
                            } : {
                                ...item,
                                id: item?._id
                            }
                        )))
                    }
                })
                .catch((error) => {
                    if (error?.response?.status == 422) navigate('/access-denied');
                    console.error('Error fetching data: ', error)
                })
    }, [params, trigger])

    const onSubmit = handleSubmit(async (data: any) => {
        if (brandFile.length == 0) {
            toast.push(
                <Notification
                    type="warning"
                    title="Please Upload Brand Image"
                />
            )
            return
        }

        if (bannerFiles.length == 0) {
            toast.push(
                <Notification
                    type="warning"
                    title="Please Upload Banner Image"
                />
            )
            return
        }

        if (posterFile.length == 0) {
            toast.push(
                <Notification
                    type="warning"
                    title="Please Upload Poster Image"
                />
            )
            return
        }

        if (ogImageFile.length == 0) {
            setOgImageError('OG Image is required')
            return
        }

        const formData = new FormData()
        const name = {
            en: data.nameEn?.trim(),
            ar: data.nameAr?.trim(),
        }

        formData.append('name[en]', name.en)
        formData.append('name[ar]', name.ar)
        formData.append('overview[en]', data.overviewEn)
        formData.append('overview[ar]', data.overviewAr)
        formData.append('seoDetails[title][en]', data.metaTitleEn ?? "")
        formData.append('seoDetails[title][ar]', data.metaTitleAr ?? "")
        formData.append('seoDetails[description][en]', data.metaDescriptionEn ?? "")
        formData.append('seoDetails[description][ar]', data.metaDescriptionAr ?? "")
        formData.append('seoDetails[keywords][en]', data.metaKeywordsEn ?? "")
        formData.append('seoDetails[keywords][ar]', data.metaKeywordsAr ?? "")
        formData.append('seoDetails[canonical][en]', data.metaCanonicalUrl ?? "")
        formData.append('seoDetails[canonical][ar]', data.metaCanonicalUrlAr ?? "")

        if (pageSections.length > 0) {
            for (let i = 0; i < pageSections.length; i++) {
                formData.append(`page`, JSON.stringify(
                    pageSections[i]?.type == "carousel" ? {
                        ...pageSections[i],
                        products: pageSections[i]?.products?.map((item: any) => item?.value)
                    } : pageSections[i]
                ))
            }
        }

        if (data?.stores?.length > 0) {
            for (let i = 0; i < data?.stores?.length; i++) {
                formData.append('store', data.stores[i].value)
            }
        }
        if (data?.isActive?.value) formData.append('isActive', data?.isActive?.value);
        if (data?.isCashbackEnabled?.value) formData.append('isCashbackEnabled', data?.isCashbackEnabled?.value);
        formData.append('cashbackPercentage', data?.cashbackPercentage)
        formData.append('image', brandFile[0])
        formData.append('position', data?.position)

        formData.append('banner', bannerFiles[0])
        formData.append('bannerAr', bannerFilesAr[0])
        formData.append('poster', posterFile[0])
        formData.append('posterAr', posterFileAr[0])
        formData.append('ogImage', ogImageFile[0])
        formData.append('brandId', brandId)
        if (params.id) {
            formData.append('refid', params.id)

            api.post(endpoints.updateBrand, formData)
                .then((res) => {
                    if (res?.status == 200) {
                        toast.push(
                            <Notification
                                type="success"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                        // navigate('/catalog/brands')
                    }
                })
                .catch((error) => {
                    toast.push(
                        <Notification
                            type="warning"
                            title={error.response.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                })
        } else {
            api.post(endpoints.createBrand, formData)
                .then((res) => {
                    if (res?.status == 200) {
                        toast.push(
                            <Notification
                                type="success"
                                title={res.data.message}
                            />,
                            {
                                placement: 'top-center',
                            }
                        )
                        // navigate('/catalog/brands')
                    }
                })
                .catch((error) => {
                    toast.push(
                        <Notification
                            type="warning"
                            title={error.response.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                })
        }
    })

    const changeBannerType = (id: number, type: string) => {
        let newCats = [...pageSections];
        const i = newCats.findIndex((item: any) => item?.id == id);
        newCats[i].bannerType = type;
        setPageSections(newCats);
    }

    const handleSliderVideo = (e: any, field: string, id: string) => {
        const file = e.target.files[0]
        let newCats = [...pageSections];
        const i = newCats.findIndex((item: any) => item?.id == id);
        if (file) {
            const maxFileSize = 10 * 1024 * 1024
            if (file.size > maxFileSize) {
                toast.push(
                    <Notification
                        type="warning"
                        title="File size exceeds the maximum limit of 10MB."
                    />,
                    {
                        placement: 'top-center',
                    }
                )
                return
            }

            const formData = new FormData()
            formData.append('video', file)
            api.post(endpoints.videoUpload, formData).then((res) => {
                if (res?.status == 200) {
                    const attributes = field.split(".")
                    if (attributes.length == 2) {
                        newCats[i][attributes[0]] = {
                            ...newCats[i][attributes[0]],
                            [attributes[1]]: res.data?.videoUrl
                        }
                    } else {
                        newCats[i][field] = res.data?.videoUrl;
                    }
                    setPageSections(newCats);
                }
            })
        } else {
            toast.push(
                <Notification
                    type="warning"
                    title="Please select a video file."
                />,
                {
                    placement: 'top-center',
                }
            )
        }
    }

    const handleChange = (value: any, id: string, field: string) => {
        let newCats = [...pageSections];
        const i = newCats.findIndex((item: any) => item?.id == id);
        newCats[i][field] = value;
        // setPageSections(newCats);
        setPageSection(newCats[i])
    }

    function handleContentChange(value: any, id: string, field: string) {
        let newCats = [...pageSections];
        const i = newCats.findIndex((item: any) => item?.id == id);
        const [attribute, locale] = field.split(".")
        newCats[i] = {
            ...newCats[i],
            [attribute]: {
                ...newCats[i][attribute],
                [locale]: value
            }
        };
        setPageSections(newCats);
        setPageSection(newCats[i])
        console.log(newCats)
    }

    const productSelect = (option: any) => {
        const index = pageSection?.products.findIndex((item: any) => (item?._id ?? item)?.toString() == option?.value?.toString())
        let newCats = [...pageSections];
        if (index == -1) {
            const i = newCats.findIndex((item: any) => item?.id == pageSection?.id);
            newCats[i].products = option
            setPageSections(newCats);
            // setPageSection(newCats[i])
        } else {
            const i = newCats.findIndex((item: any) => item?.id == pageSection?.id);
            newCats[i].products.splice(index, 1)
            setPageSections(newCats);
            // setPageSection(newCats[i])
        }
    }

    console.log(pageSections)

    return (
        <div className="p-4">
            <form onSubmit={onSubmit}>
                <h3>{params.id ? 'Edit' : 'Add'} Brand</h3>
                <Breadcrumb items={breadcrumbItems} />
                <h4>ID: {brandId}</h4>
                <div className="w-full p-1 my-2 bg-slate-100 flex gap-2 rounded">
                    <button type='button' onClick={() => setSection("details")} className={`w-full py-1 ${section == "details" ? "bg-indigo-600 text-white rounded" : ""} duration-150`}>Details</button>
                    <button type='button' onClick={() => setSection("page")} className={`w-full py-1 ${section == "page" ? "bg-indigo-600 text-white rounded" : ""} duration-150`}>Page</button>
                    <button type='button' onClick={() => setSection("seo")} className={`w-full py-1 ${section == "seo" ? "bg-indigo-600 text-white rounded" : ""} duration-150`}>SEO</button>
                </div>
                {section == "details" && <>
                    <h5 className="mt-4">Name</h5>
                    <div className="grid grid-cols-2 gap-4 mt-2">
                        <FormItem label="English">
                            <Controller
                                name="nameEn"
                                rules={{ required: 'Field Required' }}
                                control={control}
                                defaultValue=""
                                render={({ field }) => <Input {...field} />}
                            />
                            {errors.nameEn && (
                                <small className="text-red-600">
                                    This field is required
                                </small>
                            )}
                        </FormItem>
                        <FormItem label="Arabic">
                            <Controller
                                name="nameAr"
                                control={control}
                                defaultValue=""
                                render={({ field }) => <Input dir='rtl' {...field} />}
                            />
                        </FormItem>
                    </div>

                    <h5>Overview</h5>
                    <div className="grid grid-cols-2 gap-4 mt-2">
                        <FormItem label="Description English">
                            <Controller
                                name="overviewEn"
                                control={control}
                                defaultValue=""
                                rules={{ required: true }}
                                render={({ field }) => (
                                    <Input textArea {...field} />
                                )}
                            />
                            {errors.overviewEn && (
                                <small className="text-red-600">
                                    This field is required
                                </small>
                            )}
                        </FormItem>
                        <FormItem label="Description Arabic">
                            <Controller
                                name="overviewAr"
                                control={control}
                                defaultValue=""
                                rules={{ required: true }}
                                render={({ field }) => (
                                    <Input dir='rtl' textArea {...field} />
                                )}
                            />
                            {errors.overviewAr && (
                                <small className="text-red-600">
                                    This field is required
                                </small>
                            )}
                        </FormItem>

                        <FormItem label="Stores">
                            <Controller
                                name="stores"
                                control={control}
                                defaultValue=""
                                // rules={{ required: 'Field Required' }}
                                render={({ field }) => (
                                    <Select {...field} options={stores} isMulti />
                                )}
                            />
                            {errors.stores && (
                                <small className="text-red-600">
                                    This field is required
                                </small>
                            )}
                        </FormItem>

                        <FormItem label="Position">
                            <Controller
                                name="position"
                                rules={{ required: 'Field Required' }}
                                control={control}
                                defaultValue=""
                                render={({ field }) => <Input type='number' {...field} />}
                            />
                            {errors.position && (
                                <small className="text-red-600">
                                    This field is required
                                </small>
                            )}
                        </FormItem>
                    </div>

                    {params.id && (
                        <div className="grid grid-cols-2 gap-4 mt-2">
                            <FormItem label="Is Active?">
                                <Controller
                                    name="isActive"
                                    control={control}
                                    defaultValue=""
                                    render={({ field }) => (
                                        <Select {...field} options={options} />
                                    )}
                                />
                            </FormItem>
                        </div>
                    )}
                    <div className="grid grid-cols-2 gap-4 mt-2">
                        <FormItem label="Cashback">
                            <Controller
                                name="isCashbackEnabled"
                                control={control}
                                defaultValue=""
                                render={({ field }) => (
                                    <Select {...field} options={options} />
                                )}
                            />
                        </FormItem>
                        <FormItem label="Cashback Percentage">
                            <Controller
                                name="cashbackPercentage"
                                control={control}
                                defaultValue="0"
                                rules={{ required: 'Field is required' }}
                                render={({ field }) => (
                                    <Input min={0} type="number" {...field} />
                                )}
                            />
                            {errors.cashbackPercentage && (
                                <small className="text-red-600 py-3">
                                    {errors.cashbackPercentage.message as string}
                                </small>
                            )}
                        </FormItem>
                    </div>
                    <div className="mt-2">
                        <FormItem label="Brand Image">
                            <Upload
                                draggable
                                uploadLimit={1}
                                accept="image/*"
                                fileList={brandFile}
                                onChange={handleImageUpload}
                                ratio={[360, 180]}
                            />
                        </FormItem>
                    </div>
                    {/* <div className="grid grid-cols-2 gap-4">
                    <FormItem label="Banner Image English">
                        <Upload
                            draggable
                            uploadLimit={1}
                            accept="image/*"
                            fileList={bannerFiles}
                            onChange={handleBannerUpload}
                            ratio={[1332, 240]}
                        />
                    </FormItem>
                    <FormItem label="Banner Image Arabic">
                        <Upload
                            draggable
                            uploadLimit={1}
                            accept="image/*"
                            fileList={bannerFilesAr}
                            onChange={handleBannerUploadAr}
                            ratio={[1332, 240]}
                        />
                    </FormItem>
                    </div> */}
                    <div className='grid grid-cols-2 gap-4'>
                        <FormItem label="Brand Poster English">

                            <Upload
                                draggable
                                uploadLimit={1}
                                accept="image/*"
                                fileList={posterFile}
                                onChange={handlePosterUpload}
                                ratio={[360, 180]}
                            />
                        </FormItem>
                        <FormItem label="Brand Poster Arabic">
                            <Upload
                                draggable
                                uploadLimit={1}
                                accept="image/*"
                                fileList={posterFileAr}
                                onChange={handlePosterUploadAr}
                                ratio={[360, 180]}
                            />
                        </FormItem>
                    </div>

                    {params.id && <div className="flex flex-col gap-4 w-full mb-4">
                        <p className='form-label'>Sub Brands</p>
                        {subBrands?.length > 0 ? <div className="flex flex-col gap-2 w-full">
                            {subBrands?.map((item: any, i: number) => <RenderSubCats root={brand?._id} key={item?._id} item={item} setTrigger={() => { setTrigger((prev: any) => !prev) }} />)}
                        </div> : ""}
                        <Button
                            className="float-right mt-4 w-fit"
                            variant="solid"
                            onClick={() => { setSubPop(true); }}
                            type='button'
                        >
                            Add sub Brand
                        </Button>
                    </div>}
                    <Dialog isOpen={subPop} onClose={() => setSubPop(false)} height="80%" >
                        <ManageSubCategory
                            handleClose={() => {
                                setSubPop(false);
                            }}
                            id={null}
                            parent={brand?._id}
                            getTrigger={() => { setTrigger((prev: any) => !prev) }}
                        />
                    </Dialog>
                </>}

                {section == "page" && <>
                    <DndContext
                        sensors={sensors}
                        // collisionDetection={closestCenter}
                        onDragEnd={handleDrag}
                    >
                        <SortableContext
                            strategy={verticalListSortingStrategy}
                            items={pageSections?.map((item: any) => item?.id)}>
                            <div className="flex flex-col gap-2 w-full">
                                {pageSections?.map((item: any, i: number) => <PageDraggable key={item?.id} id={item?.id} item={item}>
                                    <div className='w-full'>
                                        <p className="text-lg font-bold">{item?.title}</p>
                                        <div className={`flex justify-between mt-4 items-center gap-4 h-32 max-h-52 p-4 w-full overflow-y-auto relative ${!item?.isActive ? "opacity-50" : ""}`}>
                                            <div className="w-full h-full flex flex-wrap gap-4">
                                                {item?.type == "banner" ?
                                                    item?.bannerType == "image" ? <>
                                                        {item?.banner?.en && <img src={imageUrl + item?.banner?.en} alt="" className='h-full object-contain' />}
                                                        {item?.banner?.ar && <img src={imageUrl + item?.banner?.ar} alt="" className='h-full object-contain' />}
                                                    </>
                                                        : <>
                                                            {item?.src && <video src={imageUrl + item?.src} controls className='h-full object-contain' />}
                                                            {item?.mobileSrc && <video src={imageUrl + item?.mobileSrc} controls className='h-full object-contain' />}
                                                        </>

                                                    : item?.type == "content" ? <div className="flex flex-col gap-2">
                                                        <p dangerouslySetInnerHTML={{ __html: item?.content?.en }}></p>
                                                        <p dangerouslySetInnerHTML={{ __html: item?.content?.ar }}></p>
                                                    </div>
                                                        : item?.type == "carousel" ? <div className="flex flex-col gap-2">
                                                            <ul className='list-disc list-inside'>
                                                                {item?.products?.map((product: any) => <li>{product?.label}</li>)}
                                                            </ul>
                                                        </div>
                                                            : item?.type == "offerBanner" ? <>
                                                                {item?.banner?.en && <img src={imageUrl + item?.banner?.en} alt="" className='h-full object-contain' />}
                                                                {item?.banner?.ar && <img src={imageUrl + item?.banner?.ar} alt="" className='h-full object-contain' />}
                                                            </>
                                                                : <></>
                                                }
                                            </div>

                                            <div className="flex gap-4 ml-auto sticky top-9">
                                                <button type='button' onClick={() => { setPageSection(item); setShowPagePopup(true) }}>
                                                    <HiPencilSquare size={25} />
                                                </button>
                                                <Switcher
                                                    checked={item?.isActive}
                                                    onChange={(e: any) => handleActive(e, i)}
                                                />

                                            </div>
                                        </div>
                                    </div>
                                </PageDraggable>)}
                            </div>
                        </SortableContext>
                    </DndContext>

                    <Dialog contentClassName='overflow-y-auto' isOpen={showPagePopup} onClose={() => setShowPagePopup(false)} height="80%" width={"70%"} >
                        {
                            pageSection?.type == "banner" ? <ManageBanner pageSection={pageSection} changeBannerType={changeBannerType} handleSliderVideo={handleSliderVideo} />
                                : pageSection?.type == "content" ? <ManageContent pageSection={pageSection} handleContentChange={handleContentChange} />
                                    : pageSection?.type == "carousel" ? <ManageCarousel pageSection={pageSection} handleContentChange={handleContentChange} productSelect={productSelect} brand={brand} />
                                        : pageSection?.type == "offerBanner" ? <ManageOfferBanner pageSection={pageSection} handleSliderVideo={handleSliderVideo} />
                                            : <></>
                        }
                        <div className="flex justify-end gap-2 mt-4">
                            <Button
                                className="float-right mt-4 w-fit"
                                variant="solid"
                                onClick={() => setShowPagePopup(false)}
                                type='button'
                            >
                                Close
                            </Button>
                        </div>
                    </Dialog>
                </>}

                {section == "seo" && <>
                    <h5>Seo Section</h5>
                    <div className='grid grid-cols-2 gap-4'>
                        <FormItem label="Meta Title English">
                            <Controller
                                name="metaTitleEn"
                                control={control}
                                defaultValue=""
                                rules={{ required: 'Field is required' }}
                                render={({ field }) => (
                                    <Input
                                        type="text"
                                        {...field}
                                    />
                                )}
                            />
                            {errors.metaTitleEn && (
                                <small className="text-red-600 py-3">
                                    {errors.metaTitleEn.message as string}
                                </small>
                            )}
                        </FormItem>
                        <FormItem label="Meta Title Arabic">
                            <Controller
                                name="metaTitleAr"
                                control={control}
                                defaultValue=""
                                // rules={{ required: 'Field is required' }}
                                render={({ field }) => (
                                    <Input
                                        dir="rtl"
                                        type="text"
                                        {...field}
                                    />
                                )}
                            />
                            {errors.metaTitleAr && (
                                <small className="text-red-600 py-3">
                                    {errors.metaTitleAr.message as string}
                                </small>
                            )}
                        </FormItem>
                    </div>

                    <div className='grid grid-cols-2 gap-4'>
                        <FormItem label="Meta Description English">
                            <Controller
                                name="metaDescriptionEn"
                                control={control}
                                defaultValue=""
                                rules={{ required: 'Field is required' }}
                                render={({ field }) => (
                                    <Input
                                        type="text"
                                        {...field}
                                    />
                                )}
                            />
                            {errors.metaDescriptionEn && (
                                <small className="text-red-600 py-3">
                                    {errors.metaDescriptionEn.message as string}
                                </small>
                            )}
                        </FormItem>
                        <FormItem label="Meta Description Arabic">
                            <Controller
                                name="metaDescriptionAr"
                                control={control}
                                defaultValue=""
                                // rules={{ required: 'Field is required' }}
                                render={({ field }) => (
                                    <Input
                                        dir="rtl"
                                        type="text"
                                        {...field}
                                    />
                                )}
                            />
                            {errors.metaDescriptionAr && (
                                <small className="text-red-600 py-3">
                                    {errors.metaDescriptionAr.message as string}
                                </small>
                            )}
                        </FormItem>
                    </div>

                    <div className='grid grid-cols-2 gap-4'>
                        <FormItem label="Meta Keywords English">
                            <Controller
                                name="metaKeywordsEn"
                                control={control}
                                defaultValue=""
                                rules={{ required: 'Field is required' }}
                                render={({ field }) => (
                                    <Input
                                        type="text"
                                        {...field}
                                    />
                                )}
                            />
                            {errors.metaKeywordsEn && (
                                <small className="text-red-600 py-3">
                                    {errors.metaKeywordsEn.message as string}
                                </small>
                            )}
                        </FormItem>
                        <FormItem label="Meta Keywords Arabic">
                            <Controller
                                name="metaKeywordsAr"
                                control={control}
                                defaultValue=""
                                // rules={{ required: 'Field is required' }}
                                render={({ field }) => (
                                    <Input
                                        dir="rtl"
                                        type="text"
                                        {...field}
                                    />
                                )}
                            />
                            {errors.metaKeywordsAr && (
                                <small className="text-red-600 py-3">
                                    {errors.metaKeywordsAr.message as string}
                                </small>
                            )}
                        </FormItem>
                    </div>

                    <div className='grid grid-cols-2 gap-4'>
                        <FormItem label="Meta Canonical URL English">
                            <Controller
                                name="metaCanonicalUrl"
                                control={control}
                                defaultValue=""
                                rules={{ required: 'Field is required' }}
                                render={({ field }) => (
                                    <Input
                                        type="text"
                                        {...field}
                                    />
                                )}
                            />
                            {errors.metaCanonicalUrl && (
                                <small className="text-red-600 py-3">
                                    {errors.metaCanonicalUrl.message as string}
                                </small>
                            )}
                        </FormItem>

                        <FormItem label="Meta Canonical URL Arabic">
                            <Controller
                                name="metaCanonicalUrlAr"
                                control={control}
                                defaultValue=""
                                // rules={{ required: 'Field is required' }}
                                render={({ field }) => (
                                    <Input
                                        type="text"
                                        {...field}
                                    />
                                )}
                            />
                            {errors.metaCanonicalUrlAr && (
                                <small className="text-red-600 py-3">
                                    {errors.metaCanonicalUrlAr.message as string}
                                </small>
                            )}
                        </FormItem>
                    </div>

                    <div className='grid grid-cols-2 gap-4'>
                        <FormItem label="Meta OG Image">
                            <Upload
                                draggable
                                uploadLimit={1}
                                accept="image/*"
                                fileList={ogImageFile}
                                onChange={handleOgImageUpload}
                                ratio={[1126, 1200]}
                            />
                            {ogImageError && <small className="text-red-600">{ogImageError}</small>}
                        </FormItem>

                    </div>
                </>}

                <Button
                    className="float-right mt-4"
                    variant="solid"
                    type="submit"
                    icon={<AiOutlineSave />}
                >
                    Save
                </Button>
            </form>
        </div>
    )
}

export default ManageBrand

function RenderSubCats({ item, parent, setTrigger }: any) {
    // Base rendering for the item
    const [subPop, setSubPop] = useState(false)
    const [deleteModalOpen, setDeleteModalOpen] = useState(false)
    const [itemToDelete, setItemToDelete] = useState(null)
    const [id, setId] = useState<any>(null)

    const openDeleteModal = (refid: any) => {
        setDeleteModalOpen(true)
        setItemToDelete(refid)
    }

    const closeDeleteModal = () => {
        setDeleteModalOpen(false)
    }

    const confirmDelete = () => {
        api.put(endpoints.deleteBrand + item?.refid)
            .then((res) => {
                if (res.status == 200) {
                    toast.push(
                        <Notification
                            type="success"
                            title={res.data.message}
                        />,
                        {
                            placement: 'top-center',
                        }
                    )
                    closeDeleteModal()
                    setTrigger()
                }
            })
            .catch((err) => {
                toast.push(
                    <Notification
                        type="warning"
                        title={err.response.data.message}
                    />,
                    {
                        placement: 'top-center',
                    }
                )
                setTrigger()
                closeDeleteModal()
            })
    }

    return (
        <div className="w-full flex flex-col gap-2">
            <div key={item?._id} className="w-full flex gap-4 px-4 py-2 rounded border-2 justify-between">
                <p>{item?.name?.en}</p>
                <div className="flex gap-4">
                    <HiPencilSquare
                        onClick={() => {
                            setId(item?.refid)
                            setSubPop(true);
                        }}
                        size={25} className='cursor-pointer' />
                    <MdDeleteOutline
                        onClick={() => openDeleteModal(item?.refid)}
                        size={25} className='cursor-pointer' />
                </div>
            </div>
            <Dialog isOpen={subPop} onClose={() => setSubPop(false)} height="80%" >
                <ManageSubCategory
                    handleClose={() => {
                        setId(null);
                        setSubPop(false);
                    }}
                    parent={item?._id}
                    id={id}
                    getTrigger={setTrigger}
                />
            </Dialog>
            <DeleteModal
                isOpen={deleteModalOpen}
                title="Delete Brand"
                content="Are you sure you want to delete this Sub Brand?"
                onClose={closeDeleteModal}
                onConfirm={confirmDelete}
            />
        </div>
    );
}

function PageDraggable(props: any) {
    const {
        attributes,
        listeners,
        setNodeRef,
        transform,
        transition,
        isDragging
    } = useSortable({ id: props.id });
    const style = {
        transform: CSS.Transform.toString(transform),
        transition
    };
    return (
        <div
            style={style}
            className={`${isDragging ? "z-[1] opacity-50" : ""} flex gap-2 w-full border rounded p-2`}
            ref={setNodeRef}
        >
            <button type='button' className='cursor-grab active:cursor-grabbing' {...listeners} {...attributes}><MdDragIndicator /></button>
            {props.children}
        </div>

    )
}
