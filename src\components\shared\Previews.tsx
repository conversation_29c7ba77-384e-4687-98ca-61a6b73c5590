/* eslint-disable */

import { useEffect, useState } from "react";

interface PreviewsProps {
    imageUrls: string[];
}




const Previews = ({ imageUrls }: PreviewsProps) => {
    return (
        <div className="grid grid-cols-4 gap-3">
            {imageUrls.map((imageUrl: any, index: any) => (
                <img
                    key={index}
                    src={import.meta.env.VITE_ASSET_URL  + imageUrl}
                    alt={`Preview ${index + 1}`}
                    style={{ maxWidth: '100%', height: 'auto', margin: '10px' }}
                />
            ))}
        </div>
    );
};

export default Previews;
