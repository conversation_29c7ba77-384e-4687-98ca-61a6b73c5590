/* eslint-disable */
import SelectionItem from './SelectionItem'; // Import the SelectionItem component

interface BrandSelectionProps {
    brands: any[];
    onBrandChange: (brandId: string) => void;
}

const baseUrl = import.meta.env.VITE_ASSET_URL;

const BrandSelection = ({ brands, onBrandChange }: BrandSelectionProps) => (
    <div className="mt-5 max-h-full overflow-y-auto">
        <div className="flow-root">
            <h5>Select Brands</h5>
            <ul className="grid w-full gap-4 md:grid-cols-4 mb-4">
                {brands.map((brand, index) => (
                    <SelectionItem
                        key={brand.refid}
                        id={brand.refid}
                        label={brand.name.en}
                        image={`${baseUrl}${brand.image}`}
                        onChange={() => onBrandChange(brand.refid)}
                        checked={brand.inHome}
                    />
                ))}
            </ul>
        </div>
    </div>
);

export default BrandSelection;