import { FormItem, Input } from '@/components/ui'
import React from 'react'
import { Controller } from 'react-hook-form'

const myAccountMain: { label: string, value: string, db: string }[] = [
    {
        label: 'My Profile',
        value: 'MyProfile',
        db: 'myProfile',
    },
    {
        label: 'My Cart',
        value: 'MyCart',
        db: 'myCart'
    },
    {
        label: 'Try Cart',
        value: 'TryCart',
        db: 'tryCart'
    },
    {
        label: 'My Orders',
        value: 'MyOrders',
        db: 'myOrders'
    },
    {
        label: 'My Wishlist',
        value: 'MyWishlist',
        db: 'myWishlist'
    },
    {
        label: 'My AddressBook',
        value: 'MyAddressBook',
        db: 'myAddressBook'
    },
    {
        label: 'My Subscription',
        value: 'MySubscription',
        db: 'mySubscription'
    },
    {
        label: 'My Prescription',
        value: 'MyPrescription',
        db: 'myPrescription'
    },
    {
        label: 'My Cashbacks',
        value: 'MyCashbacks',
        db: 'myCashbacks'
    },
    {
        label: 'Login',
        value: 'Login',
        db: 'login'
    },
    {
        label: 'Logout',
        value: 'Logout',
        db: 'logout'
    },
    {
        label: 'Search',
        value: 'Search',
        db: 'search'
    },
    {
        label: 'Store Locator',
        value: 'StoreLocator',
        db: 'storeLocator'
    },
    {
        label: 'Personal Information',
        value: 'PersonalInformation',
        db: 'personalInformation'
    },
]

const myAddress: { label: string, value: string, db: string }[] = [
    {
        label: "Address Home",
        value: "AddressHome",
        db: "addressHome",
    },
    {
        label: "Address Work",
        value: "AddressWork",
        db: "addressWork",
    },
    {
        label: "Address Other",
        value: "AddressOther",
        db: "addressOther",
    },
    {
        label: "Address Default",
        value: "AddressDefault",
        db: "addressDefault",
    },
    {
        label: "Set Default Address",
        value: "SetDefaultAddress",
        db: "setDefaultAddress",
    },
    {
        label: "Address Edit",
        value: "AddressEdit",
        db: "addressEdit",
    },
    {
        label: "Address Remove",
        value: "AddressRemove",
        db: "addressRemove",
    },
    {
        label: "Address Add",
        value: "AddressAdd",
        db: "addressAdd",
    },
    {
        label: "Address Save Check",
        value: "AddressSaveCheck",
        db: "addressSaveCheck",
    },
]

const others: { label: string, value: string, db: string }[] = [
    {
        label: "Upload Prescription",
        value: "UploadPrescription",
        db: "uploadPrescription",
    },
    {
        label: "Empty Prescription",
        value: "EmptyPrescription",
        db: "emptyPrescription",
    },
    {
        label: "My Cashbacks",
        value: "MyCashbacks",
        db: "myCashbacks",
    },
    {
        label: "Total Cashback Earned",
        value: "TotalCashbackEarned",
        db: "totalCashbackEarned",
    },
    {
        label: "Total Cashback Spent",
        value: "TotalCashbackSpent",
        db: "totalCashbackSpent",
    },
    {
        label: "Cashback Balance",
        value: "CashbackBalance",
        db: "cashbackBalance",
    },
    {
        label: "Cashback Spent",
        value: "CashbackSpent",
        db: "cashbackSpent",
    },
    {
        label: "Cashback Earned",
        value: "CashbackEarned",
        db: "cashbackEarned",
    },
    {
        label: "No Cashback History",
        value: "NoCashbackHistory",
        db: "noCashbackHistory",
    },
    {
        label: "Notes",
        value: "Notes",
        db: "notes",
    },
]

const prescriptionUpload: { label: string, value: string, db: string }[] = [
    {
        label: "Prescription Upload Title",
        value: "PrescriptionUploadTitle",
        db: "prescriptionUploadTitle"
    },
    {
        label: "Prescription Upload Input Label",
        value: "PrescriptionUploadInputLabel",
        db: "prescriptionUploadInputLabel"
    },
    {
        label: "Prescription Upload Input Placeholder",
        value: "PrescriptionUploadInputPlaceholder",
        db: "prescriptionUploadInputPlaceholder"
    },
    {
        label: "Prescription Upload Input File",
        value: "PrescriptionUploadInputFile",
        db: "prescriptionUploadInputFile"
    },
    {
        label: "Prescription Upload File Info",
        value: "PrescriptionUploadFileInfo",
        db: "prescriptionUploadFileInfo"
    },
    {
        label: "Prescription Upload Info",
        value: "PrescriptionUploadInfo",
        db: "prescriptionUploadInfo"
    },
    {
        label: "Prescription Upload Submit",
        value: "PrescriptionUploadSubmit",
        db: "prescriptionUploadSubmit"
    },
    {
        label: "Prescription Upload Error",
        value: "PrescriptionUploadError",
        db: "prescriptionUploadError"
    },
    {
        label: "Prescription Create Success",
        value: "Prescription Create Success",
        db: "prescriptionCreateSuccess"
    },
    {
        label: "Prescription Delete Success",
        value: "PrescriptionDeleteSuccess",
        db: "prescriptionDeleteSuccess"
    },
    {
        label: "Prescription Delete Warning",
        value: "PrescriptionDeleteWarning",
        db: "prescriptionDeleteWarning"
    },
    {
        label: "Prescription Delete Yes",
        value: "PrescriptionDeleteYes",
        db: "prescriptionDeleteYes"
    },
    {
        label: "Prescription Delete No",
        value: "PrescriptionDeleteNo",
        db: "prescriptionDeleteNo"
    },
]

export const myAccount: { label: string, value: string, db: string }[] = [
    ...myAccountMain,
    ...myAddress,
    ...others,
    ...prescriptionUpload,
]

function MyAccountTab({ control, errors }: any) {
    return (
        <>
            <h3>My Account</h3>
            {myAccountMain.map((item) => (
                <Forms key={item.value} control={control} errors={errors} item={item} />
            ))}
            <h3>My Address Book</h3>
            {myAddress.map((item) => (
                <Forms key={item.value} control={control} errors={errors} item={item} />
            ))}
            <h3>Others</h3>
            {others.map((item) => (
                <Forms key={item.value} control={control} errors={errors} item={item} />
            ))}
              <h3>Prescription Upload Dialog</h3>
            {prescriptionUpload.map((item) => (
                <Forms key={item.value} control={control} errors={errors} item={item} />
            ))}
        </>
    )
}

function Forms({ item, control, errors }: any) {
    return (
        <div className="mt-2">
            <div className="grid grid-cols-2 gap-4">
                <FormItem label={`${item.label} English`}>
                    <Controller
                        control={control}
                        name={`myAccount${item.value}En`}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => <Input type="text" {...field} />}
                    />
                    {errors[`myAccount${item.value}En`] && (
                        <small className="text-red-600 py-3">
                            {' '}
                            {errors[`myAccount${item.value}En`].message as string}{' '}
                        </small>
                    )}
                </FormItem>
                <FormItem label={`${item.label} Arabic`}>
                    <Controller
                        control={control}
                        name={`myAccount${item.value}Ar`}
                        rules={{ required: 'Field Required' }}
                        render={({ field }) => <Input dir='rtl' type="text" {...field} />}
                    />
                    {errors[`myAccount${item.value}Ar`] && (
                        <small className="text-red-600 py-3">
                            {' '}
                            {errors[`myAccount${item.value}Ar`].message as string}{' '}
                        </small>
                    )}
                </FormItem>
            </div>
        </div>
    )
}

export default MyAccountTab